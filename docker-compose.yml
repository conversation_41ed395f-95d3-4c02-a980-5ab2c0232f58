version: '3.8'

services:
  base-services:
    image: alpine
    depends_on:
      - postgres-wms
      - postgres-fus
      - postgres-ims
      - postgres-data-service
      - postgres-partner-insight

  postgres-ims:
    image: postgres:alpine3.20
    command: postgres -c 'max_connections=250'
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: item_management_user
      POSTGRES_PASSWORD: mercaso
      POSTGRES_DB: item_management_service
      TZ: UTC
      PGTZ: UTC
    volumes:
      - /var/lib/postgresql/data

  postgres-fus:
    image: postgres:alpine3.20
    command: postgres -c 'max_connections=250'
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: fus_user
      POSTGRES_PASSWORD: mercaso
      POSTGRES_DB: fus
      TZ: UTC
      PGTZ: UTC
    volumes:
      - /var/lib/postgresql/data

  postgres-wms:
    image: postgis/postgis:16-3.4
    command: postgres -c 'max_connections=250'
    ports:
      - "5434:5432"
    environment:
      POSTGRES_USER: warehouse_management_user
      POSTGRES_PASSWORD: mercaso
      POSTGRES_DB: warehouse_management_service
      TZ: UTC
      PGTZ: UTC
    volumes:
      - /var/lib/postgresql/data
  postgres-data-service:
    image: postgres:alpine3.20
    command: postgres -c 'max_connections=250'
    ports:
      - "5436:5432"
    environment:
      POSTGRES_USER: data_user
      POSTGRES_PASSWORD: mercaso
      POSTGRES_DB: data_service
      TZ: UTC
      PGTZ: UTC
    volumes:
      - /var/lib/postgresql/data

  postgres-partner-insight:
    image: postgres:alpine3.20
    command: postgres -c 'max_connections=250'
    ports:
      - "5437:5432"
    environment:
      POSTGRES_USER: partner_insight_user
      POSTGRES_PASSWORD: mercaso
      POSTGRES_DB: partner_insight
      TZ: UTC
      PGTZ: UTC
    volumes:
      - /var/lib/postgresql/data

  zookeeper:
    image: bitnami/zookeeper:3.9.2
    expose:
      - "2181"
    environment:
      ALLOW_ANONYMOUS_LOGIN: "true"
      JVMFLAGS: -Xmx256m

  kafka:
    image: bitnami/kafka:2.8.0
    ports:
      - "19092:19092"
    expose:
      - "9092/tcp"
    environment:
      ALLOW_PLAINTEXT_LISTENER: "true"
      KAFKA_ADVERTISED_LISTENERS: INSIDE://kafka:9092,OUTSIDE://127.0.0.1:19092
      KAFKA_LISTENERS: INSIDE://:9092,OUTSIDE://:19092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INSIDE:PLAINTEXT,OUTSIDE:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: INSIDE
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_HEAP_OPTS: -Xmx256m
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock

  wait_for_dependencies:
    image: alpine
    depends_on:
      - base-services
    command: >
      sh -c '
      apk add --no-cache curl &&
      ./wait-for-it.sh zookeeper:2181 --timeout=60 &&
      ./wait-for-it.sh kafka:9092 --timeout=60 &&
      ./wait-for-it.sh postgres-ims:5432 --timeout=60 &&
      ./wait-for-it.sh postgres-fus:5433 --timeout=60 &&
      ./wait-for-it.sh postgres-wms:5434 --timeout=60 &&
      ./wait-for-it.sh postgres-data-service:5435 --timeout=60 &&
      ./wait-for-it.sh postgres-partner-insight:5437 --timeout=60
      '