spring:
  cloud:
    vault:
      enabled: false

security:
  enable-method-security: false
  public-paths: /**

auth0:
  role-mapping:
    user_group_to_role:
      rol_Db7YgptWv5j1cfBQ: rol_P955BxP3YFjoCyxM, rol_Db8YjfzWv5j1cfBp
      rol_Db7YminWv5j1cfBp: rol_Db7YHfzWv5j1cfBp
  mgmt:
    domain: integration.mgmt.auth0.com
    client-id: rol_Db7YgptWv5j1cfBQ
    client-secret: CZFqao8oVO6BS34qXRWZWTXiKoVda0ogl
  m2m:
    mgmt:
      domain: integration.m2m.auth0.com
      client-id: rol_Db7YgptWv5j1cfBH
      client-secret: CZFqao8oVO6BS34qXRWZWTXiKoVda0ow
    audience: integration.audience.auth0.com
    custom-domain: integration.audience.auth0.com
    clients:
      rW7FTLJJYAE4dfVmqGIbnQJ47JXZB5Y0: it-key-06e29b2e-3e2b-439a-9320-7264d22d2452

