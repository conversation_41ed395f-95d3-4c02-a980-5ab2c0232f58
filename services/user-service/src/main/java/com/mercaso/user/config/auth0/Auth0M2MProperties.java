package com.mercaso.user.config.auth0;

import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Setter
@Getter
@RequiredArgsConstructor
@ToString
@Component
@ConfigurationProperties(prefix = "auth0.m2m")
public class Auth0M2MProperties {

    private String audience;
    private String customDomain;
    private Mgmt mgmt;
    private Map<String, String> clients = Map.of();

    @Getter
    @Setter
    public static class Mgmt {

        private String domain;
        private String clientId;
        private String clientSecret;
    }
}
