package com.mercaso.user;

import java.net.InetAddress;
import java.net.UnknownHostException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.Banner.Mode;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@Slf4j
public class Application extends SpringBootServletInitializer {

    private static final String PORT_PROPERTY = "server.port";
    private static final String SERVICE_NAME = "user-service";

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
        displayApplicationUrls(context.getEnvironment());
    }

    private static void displayApplicationUrls(Environment env) {
        try {
            String port = env.getProperty(PORT_PROPERTY);
            String hostAddress = InetAddress.getLocalHost().getHostAddress();

            log.info("""
                Access URLs:
                ----------------------------------------------------------
                    \tService: \t{}\t
                \tLocal: \t\thttp://127.0.0.1:{}
                \tExternal: \thttp://{}:{}
                ----------------------------------------------------------
                """,
                SERVICE_NAME, port, hostAddress, port);
        } catch (UnknownHostException e) {
            log.error("Error getting host address", e);
        }
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application
            .sources(Application.class)
            .bannerMode(Mode.OFF);
    }
}