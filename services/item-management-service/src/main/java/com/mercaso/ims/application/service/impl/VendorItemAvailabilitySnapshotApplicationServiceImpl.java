package com.mercaso.ims.application.service.impl;

import com.mercaso.ims.application.dto.VendorItemAvailabilitySnapshotDetailDto;
import com.mercaso.ims.application.dto.VendorItemAvailabilitySnapshotDto;
import com.mercaso.ims.application.mapper.vendoritemavailabilitysnapshot.VendorItemAvailabilitySnapshotDetailDtoApplicationMapper;
import com.mercaso.ims.application.mapper.vendoritemavailabilitysnapshot.VendorItemAvailabilitySnapshotDtoApplicationMapper;
import com.mercaso.ims.application.service.VendorItemAvailabilitySnapshotApplicationService;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshot;
import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;
import com.mercaso.ims.domain.vendoritem.service.VendorItemAvailabilitySnapshotService;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class VendorItemAvailabilitySnapshotApplicationServiceImpl implements VendorItemAvailabilitySnapshotApplicationService {

    private final VendorItemAvailabilitySnapshotService snapshotService;
    private final VendorItemAvailabilitySnapshotDtoApplicationMapper snapshotDtoMapper;
    private final VendorItemAvailabilitySnapshotDetailDtoApplicationMapper detailDtoMapper;

    @Override
    public VendorItemAvailabilitySnapshotDto findById(UUID id) {
        VendorItemAvailabilitySnapshot snapshot = snapshotService.findById(id);
        if (snapshot == null) {
            return null;
        }
        return snapshotDtoMapper.domainToDto(snapshot);
    }

    @Override
    public List<VendorItemAvailabilitySnapshotDto> findByVendorId(UUID vendorId) {
        List<VendorItemAvailabilitySnapshot> restoreSnapshots = snapshotService.findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(
            vendorId,
            SnapshotType.RESTORE);
        List<VendorItemAvailabilitySnapshot> shutdownSnapshots = snapshotService.findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(
            vendorId,
            SnapshotType.SHUTDOWN);

        List<VendorItemAvailabilitySnapshot> allSnapshots = new java.util.ArrayList<>();
        allSnapshots.addAll(restoreSnapshots);
        allSnapshots.addAll(shutdownSnapshots);
        
        return allSnapshots.stream()
            .sorted((s1, s2) -> s2.getSnapshotTime().compareTo(s1.getSnapshotTime()))
            .map(snapshotDtoMapper::domainToDto)
            .toList();
    }

    @Override
    public List<VendorItemAvailabilitySnapshotDto> findByVendorIdAndSnapshotType(UUID vendorId, SnapshotType snapshotType) {
        List<VendorItemAvailabilitySnapshot> snapshots = snapshotService.findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(
            vendorId,
            snapshotType);
        return snapshots.stream()
            .map(snapshotDtoMapper::domainToDto)
            .toList();
    }

    @Override
    public VendorItemAvailabilitySnapshotDto findLatestByVendorIdAndSnapshotType(UUID vendorId, SnapshotType snapshotType) {
        return snapshotService.findLatestByVendorIdAndSnapshotType(vendorId, snapshotType)
            .map(snapshotDtoMapper::domainToDto)
            .orElse(null);
    }

    @Override
    public List<VendorItemAvailabilitySnapshotDto> findByVendorIdAndSnapshotTimeAfter(UUID vendorId, Instant startTime) {
        List<VendorItemAvailabilitySnapshot> shutdownSnapshots = snapshotService.findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(
            vendorId,
            SnapshotType.SHUTDOWN);
        List<VendorItemAvailabilitySnapshot> restoreSnapshots = snapshotService.findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(
            vendorId,
            SnapshotType.RESTORE);

        List<VendorItemAvailabilitySnapshot> allSnapshots = new java.util.ArrayList<>();
        allSnapshots.addAll(shutdownSnapshots);
        allSnapshots.addAll(restoreSnapshots);

        return allSnapshots.stream()
            .filter(snapshot -> snapshot.getSnapshotTime().isAfter(startTime))
            .sorted((s1, s2) -> s2.getSnapshotTime().compareTo(s1.getSnapshotTime()))
            .map(snapshotDtoMapper::domainToDto)
            .toList();
    }

    @Override
    public List<VendorItemAvailabilitySnapshotDetailDto> findDetailsBySnapshotId(UUID snapshotId) {
        VendorItemAvailabilitySnapshot snapshot = snapshotService.findById(snapshotId);
        if (snapshot == null || snapshot.getDetails() == null) {
            return List.of();
        }
        return snapshot.getDetails().stream()
            .map(detailDtoMapper::domainToDto)
            .toList();
    }
} 