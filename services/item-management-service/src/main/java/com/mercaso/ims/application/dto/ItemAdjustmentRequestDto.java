package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemAdjustmentRequestDto extends BaseDto {

    private UUID id;

    private String requestFile;

    private ItemAdjustmentRequestType type;

    private ItemAdjustmentRequestStatus status;
    private String failureReason;

    private Integer createdRowCount;
    private Integer modifiedRowCount;
    private Integer deletedRowCount;
    private Integer createFailedRowCount;
    private Integer createSuccessRowCount;
    private Integer modifyFailedRowCount;
    private Integer modifySuccessRowCount;

    private Integer deleteFailedRowCount;
    private Integer deleteSuccessRowCount;

    private Instant createdAt;

    private String createdBy;

    private String createdUserName;

    private String detailFile;

    private Instant updatedAt;

    private String updatedBy;

    private String updatedUserName;

    private String timeZone;

}