package com.mercaso.ims.domain.address.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum AddressPurpose {
    BILLING("Billing"),
    PAYMENT("Payment"),
    GENERAL("General"),
    SHIPPING("Shipping"),
    MAILING("Mailing"),
    BUSINESS("Business"),
    HOME("Home"),
    WAREHOUSE("Warehouse"),
    OFFICE("Office"),
    <PERSON>THER("Other"),
    UNKNOWN("Unknown"),
    ;

    private final String displayName;

    AddressPurpose(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JsonCreator
    public static AddressPurpose fromString(String name) {
        return Arrays.stream(values())
            .filter(v -> v.name().equalsIgnoreCase(name) || v.displayName.equalsIgnoreCase(name))
            .findFirst()
            .orElse(UNKNOWN);
    }
}
