package com.mercaso.ims.domain.vendor;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.address.Address;
import com.mercaso.ims.domain.email.Email;
import com.mercaso.ims.domain.phone.PhoneNumber;
import com.mercaso.ims.domain.supplieradditionalinfo.SupplierAdditionalInfo;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.domain.webaddress.WebAddress;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class Vendor extends BaseDomain {

    private final UUID id;

    private String vendorName;

    private String vendorContactName;

    private String vendorContactTel;

    private String vendorCompanyName;

    private VendorStatus vendorStatus;

    private Boolean externalPicking;

    private String finaleId;

    private Boolean shutdownWindowEnabled;

    private LocalTime shutdownWindowStart;

    private LocalTime shutdownWindowEnd;

    private String shutdownWindowDays;

    private String defaultTerms;

    private Integer defaultLeadDays;

    private String notes;

    private SupplierAdditionalInfo supplierAdditionalInfo;

    private List<Address> addresses;

    private List<Email> emails;

    private List<PhoneNumber> phoneNumbers;

    private List<WebAddress> webAddresses;

    public Vendor create(String vendorName, String vendorContactName, String vendorContactTel, String vendorCompanyName) {
        return Vendor.builder()
            .vendorName(vendorName)
            .vendorContactName(vendorContactName)
            .vendorContactTel(vendorContactTel)
            .vendorCompanyName(vendorCompanyName)
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
    }

    public void updateVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public void setShutdownWindow(Boolean shutdownWindowEnabled) {
        this.shutdownWindowEnabled = Boolean.TRUE.equals(shutdownWindowEnabled);
        this.shutdownWindowStart = LocalTime.of(5, 0);
        this.shutdownWindowEnd = LocalTime.of(6, 0);
        this.shutdownWindowDays = "FRIDAY,SATURDAY";
    }

    public void setCustomShutdownWindow(Boolean shutdownWindowEnabled, LocalTime startTime, LocalTime endTime, String days) {
        this.shutdownWindowEnabled = Boolean.TRUE.equals(shutdownWindowEnabled);
        this.shutdownWindowStart = startTime;
        this.shutdownWindowEnd = endTime;
        this.shutdownWindowDays = days;
    }

    public void setexternalPicking(Boolean externalPicking) {
        this.externalPicking = externalPicking;
    }

    public void updateVendorContactInfo(String vendorContactName, String vendorContactTel) {
        this.vendorContactName = vendorContactName;
        this.vendorContactTel = vendorContactTel;
    }

    public void updateStatus(VendorStatus vendorStatus) {
        this.vendorStatus = vendorStatus;
    }

    public Boolean judgePrimaryVendor(UUID primaryVendorId) {
        return this.id.equals(primaryVendorId);

    }

    public void updateFinaleId(String finaleId) {
        this.finaleId = finaleId;
    }

    public void updateShutdownWindow(Boolean enabled, LocalTime start, LocalTime end) {
        this.shutdownWindowEnabled = enabled;
        this.shutdownWindowStart = start;
        this.shutdownWindowEnd = end;
    }

    public void updateDefaultTerms(String defaultTerms) {
        this.defaultTerms = defaultTerms;
    }

    public void updateDefaultLeadDays(Integer defaultLeadDays) {
        this.defaultLeadDays = defaultLeadDays;
    }

    public void updateNotes(String notes) {
        this.notes = notes;
    }

    public void setSupplierAdditionalInfo(SupplierAdditionalInfo supplierAdditionalInfo) {
        this.supplierAdditionalInfo = supplierAdditionalInfo;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }

    public void setEmails(List<Email> emails) {
        this.emails = emails;
    }

    public void setPhoneNumbers(List<PhoneNumber> phoneNumbers) {
        this.phoneNumbers = phoneNumbers;
    }

    public void setWebAddresses(List<WebAddress> webAddresses) {
        this.webAddresses = webAddresses;
    }

    /**
     * Check if current time is within the vendor's shutdown window 
     * 
     * Rules:
     * - Single day windows: start time must be before end time (same day only)
     * - Multi-day windows: supports overnight/multi-day spans (e.g., "FRIDAY,SATURDAY" 23:30-06:00)
     * - For overnight windows, must specify multiple days (e.g., FRIDAY,SATURDAY for Fri 23:30 - Sat 06:00)
     * 
     * Examples:
     * - Valid: "FRIDAY" 09:00-17:00 (Friday 9 AM to 5 PM)
     * - Valid: "FRIDAY,SATURDAY" 23:30-06:00 (Friday 11:30 PM to Saturday 6 AM)
     * - Invalid: "FRIDAY" 23:30-06:00 (single day cannot span overnight)
     *
     * @return true if current time is within shutdown window, false otherwise
     */
    public boolean isWithinShutdownWindow() {
        if (!Boolean.TRUE.equals(this.shutdownWindowEnabled)) {
            return false;
        }

        if (this.shutdownWindowStart == null || this.shutdownWindowEnd == null) {
            return false;
        }

        if (this.shutdownWindowDays == null || this.shutdownWindowDays.isEmpty()) {
            return false;
        }

        ZoneId losAngelesZone = ZoneId.of("America/Los_Angeles");
        ZonedDateTime currentDateTime = ZonedDateTime.now(losAngelesZone);
        LocalTime currentTime = currentDateTime.toLocalTime();
        LocalTime startTime = this.shutdownWindowStart;
        LocalTime endTime = this.shutdownWindowEnd;
        String[] allowedDays = this.shutdownWindowDays.split(",");

        String currentDay = currentDateTime.getDayOfWeek().name();

        // Check if current day is in the allowed days
        boolean isCurrentDayAllowed = false;
        for (String day : allowedDays) {
            if (day.trim().equalsIgnoreCase(currentDay)) {
                isCurrentDayAllowed = true;
                break;
            }
        }

        // Handle different scenarios based on the day configuration
        if (allowedDays.length == 1) {
            // Single day window - start time must be before end time for same day
            if (startTime.isAfter(endTime) || startTime.equals(endTime)) {
                // Invalid configuration: single day cannot have overnight window
                // For overnight windows, must specify multiple days (e.g., FRIDAY,SATURDAY)
                log.warn("Invalid shutdown window configuration for vendor {}: single day {} with start time {} after end time {}. " +
                        "For overnight windows, specify multiple days (e.g., FRIDAY,SATURDAY).", 
                        vendorName, allowedDays[0], startTime, endTime);
                return false;
            }
            
            // Valid same-day window
            return isCurrentDayAllowed &&
                   (currentTime.isAfter(startTime) || currentTime.equals(startTime)) &&
                   (currentTime.isBefore(endTime) || currentTime.equals(endTime));
        } else {
            // Multi-day window
            if (!isCurrentDayAllowed) {
                return false;
            }
            
            String firstDay = allowedDays[0].trim().toUpperCase();
            String lastDay = allowedDays[allowedDays.length - 1].trim().toUpperCase();
            
            if (currentDay.equalsIgnoreCase(firstDay)) {
                // First day: from start time onwards
                return currentTime.isAfter(startTime) || currentTime.equals(startTime);
            } else if (currentDay.equalsIgnoreCase(lastDay)) {
                // Last day: until end time
                return currentTime.isBefore(endTime) || currentTime.equals(endTime);
            } else {
                // Middle days: entire day is within window
                return true;
            }
        }
    }
}
