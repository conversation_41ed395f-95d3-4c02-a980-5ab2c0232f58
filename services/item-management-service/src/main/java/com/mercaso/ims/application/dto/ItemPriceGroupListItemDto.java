package com.mercaso.ims.application.dto;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemPriceGroupListItemDto extends BaseDto {

    private UUID id;

    private String groupName;

    private BigDecimal price;

    private Instant createdAt;

    private String createdBy;

    private String createdUserName;

    private Integer itemCount;
}
