package com.mercaso.ims.infrastructure.excel.processor;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.infrastructure.excel.data.DollarEmpireDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.listener.DollarEmpireItemDailyUpdatedDataListener;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DollarEmpireItemDailyUpdateSheetProcessor {

    private final DocumentApplicationService documentApplicationService;

    public List<DollarEmpireDailyUpdatedData> process(String fileName) {
        List<DollarEmpireDailyUpdatedData> dollarEmpireDailyUpdatedDataList = new ArrayList<>();
        byte[] document = documentApplicationService.downloadDocument(fileName);
        try (ExcelReader excelReader = EasyExcelFactory.read(new ByteArrayInputStream(document)).build()) {

            ReadSheet vendorItemCostSheet = EasyExcelFactory.readSheet(0)
                .head(DollarEmpireDailyUpdatedData.class)
                .registerReadListener(new DollarEmpireItemDailyUpdatedDataListener(
                    dollarEmpireDailyUpdatedDataList))
                .build();
            excelReader.read(vendorItemCostSheet);
        }
        return dollarEmpireDailyUpdatedDataList;
    }
}
