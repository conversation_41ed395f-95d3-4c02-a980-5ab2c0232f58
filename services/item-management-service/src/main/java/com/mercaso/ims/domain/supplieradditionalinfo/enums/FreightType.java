package com.mercaso.ims.domain.supplieradditionalinfo.enums;

import lombok.Getter;

@Getter
public enum FreightType {
    
    DELIVERY("Delivery"),
    PICKUP("Pick-up");

    private final String displayName;

    FreightType(String displayName) {
        this.displayName = displayName;
    }

    public static FreightType fromDisplayName(String displayName) {
        for (FreightType type : values()) {
            if (type.displayName.equalsIgnoreCase(displayName)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown freight type: " + displayName);
    }
}
