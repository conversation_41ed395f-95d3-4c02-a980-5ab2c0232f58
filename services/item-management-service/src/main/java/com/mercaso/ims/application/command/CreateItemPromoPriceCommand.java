package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateItemPromoPriceCommand extends BaseCommand {

    private UUID itemId;

    private BigDecimal promoPrice;

    private Instant promoBeginTime;

    private Instant promoEndTime;

    private Boolean promoFlag;
    
}
