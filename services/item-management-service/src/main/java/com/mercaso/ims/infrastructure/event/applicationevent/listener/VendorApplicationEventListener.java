package com.mercaso.ims.infrastructure.event.applicationevent.listener;


import com.mercaso.ims.application.dto.event.VendorAmendApplicationEvent;
import com.mercaso.ims.application.dto.payload.VendorAmendPayloadDto;
import com.mercaso.ims.infrastructure.schedule.VendorShutdownWindowScheduler;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class VendorApplicationEventListener {

    private final VendorShutdownWindowScheduler vendorShutdownWindowScheduler;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleVendorAmendApplicationEvent(VendorAmendApplicationEvent vendorAmendApplicationEvent) {
        log.info("handleVendorAmendApplicationEvent for vendorAmendPayloadDto={}",
            vendorAmendApplicationEvent.getPayload());
        VendorAmendPayloadDto vendorAmendPayloadDto = vendorAmendApplicationEvent.getPayload();
        if (vendorAmendPayloadDto.isShutdownWindowEnabledChanged()) {
            UUID vendorId = vendorAmendPayloadDto.getVendorId();
            Boolean currentShutdownWindowEnabled = vendorAmendPayloadDto.getCurrent().getShutdownWindowEnabled();

            if (Boolean.TRUE.equals(currentShutdownWindowEnabled)) {
                log.info("Shutdown window enabled for vendor: {}, triggering manual shutdown", vendorId);
                vendorShutdownWindowScheduler.manualShutdown(vendorId);
            } else {
                log.info("Shutdown window disabled for vendor: {}, triggering manual restore", vendorId);
                vendorShutdownWindowScheduler.manualRestore(vendorId);
            }
        }
    }


}
