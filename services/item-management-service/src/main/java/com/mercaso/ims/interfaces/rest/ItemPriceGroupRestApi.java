package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.BatchBindingItemToPriceGroupCommand;
import com.mercaso.ims.application.command.BatchUnbindingItemFromPriceGroupCommand;
import com.mercaso.ims.application.command.CreateItemPriceGroupCommand;
import com.mercaso.ims.application.command.UpdateItemPriceGroupCommand;
import com.mercaso.ims.application.dto.BatchBindingItemPriceGroupResultDto;
import com.mercaso.ims.application.dto.BatchUnbindingItemPriceGroupResultDto;
import com.mercaso.ims.application.dto.ItemPriceGroupAuditHistoryInfoDto;
import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.application.service.ItemPriceGroupApplicationService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/item-price-group", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class ItemPriceGroupRestApi {

    private final ItemPriceGroupApplicationService itemPriceGroupApplicationService;


    @PostMapping
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemPriceGroupDto createItemPriceGroup(@RequestBody CreateItemPriceGroupCommand command) {
        log.info("[createItemPriceGroup] param command: {}.", command);
        return itemPriceGroupApplicationService.create(command);
    }


    @PutMapping
    @PreAuthorize("hasAuthority('ims:write:items')")
    public ItemPriceGroupDto updateItemPriceGroup(@RequestBody UpdateItemPriceGroupCommand command) {
        log.info("[updateItemPriceGroup] param command: {}.", command);
        return itemPriceGroupApplicationService.update(command);
    }


    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:read:items')")
    public ItemPriceGroupDto getItemPriceGroup(@PathVariable("id") UUID id) {
        log.info("[getItemPriceGroup] param itemId: {}.", id);
        return itemPriceGroupApplicationService.findItemPriceGroup(id);
    }

    @GetMapping("/{id}/audit-history")
    @PreAuthorize("hasAuthority('ims:read:items')")
    public List<ItemPriceGroupAuditHistoryInfoDto> itemPriceGroupAuditHistories(@PathVariable("id") UUID id) {
        log.info("[itemPriceGroupAuditHistories] param id: {}.", id);
        return itemPriceGroupApplicationService.getAuditHistories(id);
    }


    @PutMapping("/batch-binding-items")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public BatchBindingItemPriceGroupResultDto batchBindingItemsToPriceGroup(
        @RequestBody BatchBindingItemToPriceGroupCommand command) {
        log.info("[batchBindingItemPriceGroup] param command: {}.", command);
        return itemPriceGroupApplicationService.batchBindingItemsToPriceGroup(command);
    }

    @PutMapping("/batch-unbinding-items")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public BatchUnbindingItemPriceGroupResultDto batchUnbindingItemsFromPriceGroup(@RequestBody
    BatchUnbindingItemFromPriceGroupCommand command) {
        log.info("[batchUnbindingItemPriceGroup] param command: {}.", command);
        return itemPriceGroupApplicationService.batchUnbindingItemsFromPriceGroup(command);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public void deleteItemPriceGroup(@PathVariable("id") UUID id) {
        log.info("[deleteItemPriceGroup] param id: {}.", id);
        itemPriceGroupApplicationService.deleteItemPriceGroup(id);
    }
}
