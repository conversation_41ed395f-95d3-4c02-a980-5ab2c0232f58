package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemAmendPayloadDto extends BusinessEventPayloadDto<AmendDto<ItemDto>> {

    private UUID itemId;

    private UUID itemAdjustmentRequestDetailId;

    private ItemDto previous;

    private ItemDto current;

    @Builder
    public ItemAmendPayloadDto(ItemDto previous, ItemDto current, UUID itemAdjustmentRequestDetailId, UUID itemId) {
        super(new AmendDto<>(previous, current));
        this.itemId = itemId;
        this.itemAdjustmentRequestDetailId = itemAdjustmentRequestDetailId;
        this.previous = previous;
        this.current = current;
    }

    public boolean isPriceChanged() {
        BigDecimal currentPrice = current.getRegPrice();
        BigDecimal previousPrice = previous != null ? previous.getPrice() : null;

        if (currentPrice == null && previousPrice == null) {
            return false;
        }
        if (currentPrice == null || previousPrice == null) {
            return true;
        }
        return currentPrice.compareTo(previousPrice) != 0;
    }

    public boolean isPrimaryVendorChanged() {
        boolean primaryChanged = !Objects.equals(previous.getPrimaryVendorId(), current.getPrimaryVendorId());
        boolean backupChanged = !Objects.equals(previous.getBackupVendorId(), current.getBackupVendorId());
        return primaryChanged || backupChanged;
    }

    public boolean isStatusChanged() {
        return !Objects.equals(previous.getAvailabilityStatus(), current.getAvailabilityStatus());
    }

    public boolean isPromoFlagChanged() {
        return !Objects.equals(previous.getPromoFlag(), current.getPromoFlag());
    }

}