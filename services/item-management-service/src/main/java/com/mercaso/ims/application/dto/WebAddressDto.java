package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.webaddress.enums.WebAddressType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WebAddressDto extends BaseDto {

    private UUID id;

    private String entityType;

    private UUID entityId;

    private WebAddressType webAddressType;

    private String webAddress;
}
