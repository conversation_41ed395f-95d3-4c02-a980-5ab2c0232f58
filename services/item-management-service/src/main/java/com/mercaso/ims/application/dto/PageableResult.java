package com.mercaso.ims.application.dto;

import java.util.List;

/**
 * Generic interface for pageable results
 * @param <T> The type of data items in the result
 */
public interface PageableResult<T> {
    
    /**
     * Get the list of data items
     * @return List of data items
     */
    List<T> getData();
    
    /**
     * Get the total count of items
     * @return Total count
     */
    Long getTotalCount();
}
