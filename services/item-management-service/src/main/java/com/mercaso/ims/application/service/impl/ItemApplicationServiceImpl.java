package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.BRAND_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_SHOULD_BE_NOT_NULL;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_BACKUP_VENDOR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_PRIMARY_JIT_VENDOR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_PRIMARY_PO_VENDOR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVALID_SKU_NUMBER;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_ALREADY_EXIST;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_BINDING_PHOTO_ERROR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.PRIMARY_VENDOR_ITEM_NOT_FOUND;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

import com.alibaba.excel.util.DateUtils;
import com.google.api.client.util.Lists;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BatchUpdateItemPhotoCommand;
import com.mercaso.ims.application.command.BatchUpdateItemStatusCommand;
import com.mercaso.ims.application.command.CleanItemUpcCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.CreateItemPromoPriceCommand;
import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.command.UpdateItemBackupVendorCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemPrimaryVendorCommand;
import com.mercaso.ims.application.command.UpdateItemPromoPriceCommand;
import com.mercaso.ims.application.command.UpdateItemUpcCommand;
import com.mercaso.ims.application.command.ValidateBarcodeCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPhotoResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPromoPriceResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemStatusResultDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.ItemTagDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.PriceDto;
import com.mercaso.ims.application.dto.ValidateBarcodeResultDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.mapper.item.ItemDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemFactory;
import com.mercaso.ims.domain.item.ItemImage;
import com.mercaso.ims.domain.item.ItemUPC;
import com.mercaso.ims.domain.item.enums.ArchivedReason;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.item.enums.ImageType;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPriceFactory;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.VendorSpecification;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.metrics.MetricsTypeEnum;
import com.mercaso.ims.infrastructure.config.metrics.ReportMetric;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.BarcodeValidator;
import com.mercaso.ims.infrastructure.util.FileUtil;
import com.mercaso.ims.infrastructure.util.ImageOverlayUtil;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ItemApplicationServiceImpl implements ItemApplicationService {

    private final BusinessEventService businessEventService;
    private final ItemService itemService;
    private final ItemDtoApplicationMapper itemDtoApplicationMapper;

    private final ItemRegPriceService itemRegPriceService;
    private final ItemPromoPriceService itemPromoPriceService;
    private final DocumentApplicationService documentApplicationService;
    private final BrandService brandService;

    private final ItemQueryApplicationService itemQueryApplicationService;
    private final VendorItemService vendorItemService;
    private final VendorItemApplicationService vendorItemApplicationService;
    private final VendorSpecification vendorSpecification;
    private final ResourceLoader resourceLoader;

    @Override
    public ItemDto create(CreateItemCommand createItemCommand) {
        if (StringUtils.isBlank(createItemCommand.getSkuNumber())) {
            throw new ImsBusinessException(INVALID_SKU_NUMBER);
        }
        Item item = itemService.findBySku(createItemCommand.getSkuNumber());
        if (item != null) {
            throw new ImsBusinessException(ITEM_ALREADY_EXIST);
        }
        if (!vendorSpecification.isSatisfiedBackupVendor(createItemCommand.getBackupVendorId())) {
            throw new ImsBusinessException(INVALID_BACKUP_VENDOR);
        }

        String brandName = null;
        if (null != createItemCommand.getBrandId() && !createItemCommand.getBrandId().equalsIgnoreCase("N/A")) {
            brandName = fetchBrandOrThrow(UUID.fromString(createItemCommand.getBrandId())).getName();
        }
        item = ItemFactory.createItem(createItemCommand);
        item.setItemDefaultTags(brandName);
        List<String> itemTags = createItemCommand.getItemTags() == null ? null
            : createItemCommand.getItemTags().stream().map(ItemTagDto::getTagName).toList();
        item.addTagsIfNotExists(itemTags);
        String itemPhoto = getItemPhoto(item.getSkuNumber(), createItemCommand.getPhotoUrl(), createItemCommand.getPhotoName());
        updateItemImages(item,
            itemPhoto,
            null,
            createItemCommand.getPromoFlag(),
            null,
            createItemCommand.getRegPrice());
        item = itemService.save(item);

        createItemPrice(item.getId(),
            createItemCommand.getRegPrice(),
            createItemCommand.getCrvFlag(),
            item.getPackageSize(),
            item.getItemBottleSizeAttribute(),
            createItemCommand.getItemPromoPrices());

        createVendorItems(createItemCommand.getVendorItems(), item, createItemCommand.getItemAdjustmentRequestDetailId());
        setDefaultBackupVendorItem(item.getId(), item.getBackupVendorId());
        ItemDto itemDto = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemCreatedPayloadDto.builder()
            .itemId(itemDto.getId())
            .itemAdjustmentRequestDetailId(createItemCommand.getItemAdjustmentRequestDetailId())
            .data(itemDto)
            .build());
        return itemDto;
    }

    @Override
    public ItemDto update(UpdateItemCommand updateItemCommand) {
        Item item;
        if (updateItemCommand.getId() != null) {
            item = fetchItemOrThrow(updateItemCommand.getId());
        } else {
            log.info("Update item by sku: {}", updateItemCommand.getSkuNumber());
            item = fetchItemBySkuOrThrow(updateItemCommand.getSkuNumber());
        }
        if (!vendorSpecification.isSatisfiedBackupVendor(updateItemCommand.getBackupVendorId())) {
            throw new ImsBusinessException(INVALID_BACKUP_VENDOR);
        }
        ItemDto previous = itemQueryApplicationService.findById(item.getId());

        if (updateItemCommand.getItemAttributes() != null) {
            List<ItemAttribute> attributes = new ArrayList<>();
            updateItemCommand.getItemAttributes().forEach(
                itemAttributeDto -> attributes.add(ItemAttribute.builder()
                    .attributeId(itemAttributeDto.getAttributeId())
                    .attributeType(itemAttributeDto.getAttributeType())
                    .unit(itemAttributeDto.getUnit())
                    .value(itemAttributeDto.getValue())
                    .build())
            );
            item.updateItemAttributes(attributes);
        }

        if (updateItemCommand.getItemUPCs() != null) {
            List<ItemUPC> itemUPCs = convertToItemUPCs(updateItemCommand.getItemUPCs());
            item.updateItemUPCs(itemUPCs);
        }
        item = item.update(updateItemCommand);
        String itemPhoto = getItemPhoto(item.getSkuNumber(), updateItemCommand.getPhotoUrl(), updateItemCommand.getPhotoName());
        List<String> newTags = updateItemCommand.getItemTags() == null ? null
            : updateItemCommand.getItemTags().stream().map(ItemTagDto::getTagName).toList();
        item.updateItemTags(newTags);

        ItemAttribute itemBottleSizeAttribute = item.getItemBottleSizeAttribute();
        ItemRegPrice regPrice = itemRegPriceService.update(item.getId(),
            updateItemCommand.getRegPrice(),
            updateItemCommand.getCrvFlag(),
            item.getPackageSize(),
            itemBottleSizeAttribute);
        setDefaultBackupVendorItem(item.getId(), item.getBackupVendorId());
        boolean promoFlag =
            updateItemCommand.getPromoFlag() == null ? previous.getPromoFlag() : updateItemCommand.getPromoFlag();
        updateItemImages(item, itemPhoto, previous.getPromoFlag(), promoFlag, previous.getRegPrice(), regPrice.getRegPrice());

        // Optimize backup vendor update logic
        updateBackupVendorIfNeeded(item, updateItemCommand, previous);

        item = itemService.save(item);

        updateItemPromoPrice(
            item.getId(),
            item.getPackageSize(),
            regPrice.getCrvFlag(),
            regPrice.getCrv(),
            updateItemCommand.getItemPromoPrices()
        );

        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .itemAdjustmentRequestDetailId(updateItemCommand.getItemAdjustmentRequestDetailId())
            .previous(previous)
            .current(current)
            .build());
        return current;

    }

    @Override
    public ItemDto deleteItemById(DeleteItemCommand deleteItemCommand) {
        Item item = fetchItemOrThrow(deleteItemCommand.getId());
        item = itemService.delete(item.getId());
        ItemDto dto = itemQueryApplicationService.findById(deleteItemCommand.getId());
        businessEventService.dispatch(ItemDeletedPayloadDto.builder()
            .itemId(item.getId())
            .itemAdjustmentRequestDetailId(deleteItemCommand.getItemAdjustmentRequestDetailId())
            .data(dto)
            .build());
        return dto;
    }

    public void updateItemPromoPrice(UUID itemId, Integer packageSize,
        Boolean crvFlag,
        BigDecimal crv, List<ItemPromoPriceDto> itemPromoPrices) {

        if (CollectionUtils.isNotEmpty(itemPromoPrices)) {
            ItemPromoPriceDto promoPrice = itemPromoPrices.getFirst();
            updateItemPromoPrice(itemId,
                promoPrice.getPromoPrice(),
                promoPrice.getPromoFlag(),
                promoPrice.getPromoBeginTime(),
                promoPrice.getPromoEndTime(),
                packageSize,
                crvFlag,
                crv);
        }
    }


    private void updateItemPromoPrice(UUID itemId,
        BigDecimal promoPrice,
        Boolean promoFlag,
        Instant promoBeginTime,
        Instant promoEndTime,
        Integer packageSize,
        Boolean crvFlag,
        BigDecimal crv) {
        ItemPromoPrice itemPromoPrice = itemPromoPriceService.findByItemId(itemId).stream().findFirst().orElse(null);

        if (itemPromoPrice == null && promoPrice == null) {
            log.warn("ItemPromoPrice not found as updateItemPromoPrice id: {}", itemId);
            return;
        }

        promoPrice = (promoPrice != null) ? promoPrice
            : Optional.ofNullable(itemPromoPrice).map(ItemPromoPrice::getPromoPrice).orElse(null);

        if (itemPromoPrice == null) {
            itemPromoPriceService.save(ItemPromoPriceFactory.create(CreateItemPromoPriceCommand.builder()
                .itemId(itemId)
                .promoPrice(promoPrice)
                .promoFlag(promoFlag)
                .promoBeginTime(promoBeginTime)
                .promoEndTime(promoEndTime)
                .build(), packageSize, crvFlag, crv));
        } else {
            PriceDto priceDto = new PriceDto(promoPrice, packageSize, crvFlag, crv);
            itemPromoPrice.updatePrice(priceDto);
            if (promoFlag != null) {
                itemPromoPrice.setPromoFlag(promoFlag);
            }
            itemPromoPrice.setPromoBeginTime(promoBeginTime);
            itemPromoPrice.setPromoEndTime(promoEndTime);

            itemPromoPriceService.save(itemPromoPrice);
        }

    }

    @Override
    @ReportMetric(metricsType = MetricsTypeEnum.ITEM_BINDING_PHOTO)
    public ItemDto bindingPhoto(UUID id, MultipartFile file) {
        Item item = fetchItemOrThrow(id);
        return bindingPhotoAndSendEvent(item, file);
    }

    @Override
    public void cleanItemUPCs(CleanItemUpcCommand command) {
        Item item = fetchItemOrThrow(command.getId());
        ItemDto previous = itemQueryApplicationService.findById(item.getId());

        item.cleanItemUPCs();
        item.setMissingUpcReason(null, null);
        item = itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .itemAdjustmentRequestDetailId(command.getItemAdjustmentRequestDetailId())
            .previous(previous)
            .current(current)
            .build());
    }

    @Override
    public ItemDto active(UUID id) {
        return updateItemStatus(id, AvailabilityStatus.ACTIVE);

    }

    @Override
    public ItemDto draft(UUID id) {
        return updateItemStatus(id, AvailabilityStatus.DRAFT);

    }

    @Override
    public ItemDto archive(UUID id, ArchivedReason archivedReason) {
        return updateItemStatusWithReason(id, AvailabilityStatus.ARCHIVED, archivedReason);
    }

    @Override
    public BatchUpdateItemStatusResultDto batchUpdateItemStatus(BatchUpdateItemStatusCommand command) {
        if (CollectionUtils.isEmpty(command.getItemIds())) {
            log.warn("No item ids provided for batch update item status");
            return BatchUpdateItemStatusResultDto.builder()
                .updatedCount(0)
                .failedSkuNumbers(Collections.emptyList())
                .build();
        }

        List<String> failedSkuNumbers = new ArrayList<>();
        command.getItemIds().forEach(id -> {
            try {
                if (AvailabilityStatus.ARCHIVED.equals(command.getStatus())) {
                    updateItemStatusWithReason(id, command.getStatus(), command.getArchivedReason());
                } else {
                    updateItemStatus(id, command.getStatus());
                }
            } catch (Exception e) {
                log.error("Failed to update status for item {}", id, e);
                failedSkuNumbers.add(getSkuNumberForFailedItem(id));
            }
        });

        int updatedCount = command.getItemIds().size() - failedSkuNumbers.size();
        return BatchUpdateItemStatusResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }

    @Override
    public ValidateBarcodeResultDto validateBarcode(ValidateBarcodeCommand command) {
        boolean isValid = BarcodeValidator.isValidBarcode(command.getBarcode());
        if (isValid) {
            List<Item> items = itemService.findAllByUpcAndUpcType(command.getBarcode(), ItemUpcType.valueOf(command.getType()));
            List<ItemDto> itemDto = items.stream().map(itemDtoApplicationMapper::domainToDto).toList();
            return ValidateBarcodeResultDto.builder()
                .isValid(true)
                .message("UPC is valid")
                .associatedItems(itemDto)
                .build();
        }
        return ValidateBarcodeResultDto.builder()
            .isValid(false)
            .message("Invalid UPC number")
            .build();
    }

    @Override
    public List<ValidateBarcodeResultDto> batchValidateBarcode(List<ValidateBarcodeCommand> commands) {
        if (CollectionUtils.isEmpty(commands)) {
            return List.of();
        }

        List<ValidateBarcodeResultDto> validateBarcodeResultDtos = Lists.newArrayList();
        commands.forEach(command -> validateBarcodeResultDtos.add(validateBarcode(command)));
        return validateBarcodeResultDtos;
    }

    @Override
    public BatchUpdateItemPhotoResultDto batchUpdateItemPhoto(List<BatchUpdateItemPhotoCommand> commands) {
        if (CollectionUtils.isEmpty(commands)) {
            log.warn("No item ids provided for batch update item photo");
            return BatchUpdateItemPhotoResultDto.builder()
                .updatedCount(0)
                .failedSkuNumbers(Collections.emptyList())
                .build();
        }

        List<String> failedSkuNumbers = new ArrayList<>();
        commands.forEach(command -> {
            ItemDto previous = null;
            try {
                previous = itemQueryApplicationService.findById(command.getItemId());
                Item item = fetchItemOrThrow(command.getItemId());
                updateItemImages(item,
                    command.getPhotoName(),
                    previous.getPromoFlag(),
                    previous.getPromoFlag(),
                    previous.getRegPrice(),
                    previous.getRegPrice());
                itemService.save(item);
                ItemDto current = itemQueryApplicationService.findById(command.getItemId());
                businessEventService.dispatch(ItemAmendPayloadDto.builder()
                    .itemId(command.getItemId())
                    .previous(previous)
                    .current(current)
                    .build());
            } catch (Exception e) {
                log.error("Failed to update status for item {}", command.getItemId(), e);
                if (previous != null) {
                    failedSkuNumbers.add(previous.getSkuNumber());
                } else {
                    failedSkuNumbers.add(command.getItemId().toString());
                }

            }
        });

        int updatedCount = commands.size() - failedSkuNumbers.size();
        return BatchUpdateItemPhotoResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }

    @Override
    public ItemDto updatePromoPrice(UpdateItemPromoPriceCommand command) {
        return updatePromoPriceAndSendEvent(command);
    }

    @Override
    public ItemDto updateItemUpc(UpdateItemUpcCommand command) {
        Item item = fetchItemOrThrow(command.getItemId());

        ItemDto previous = itemQueryApplicationService.findById(command.getItemId());
        List<ItemUPC> itemUPCs = convertToItemUPCs(command.getItemUPCs());
        item.updateItemUPCs(itemUPCs);
        item.setMissingUpcReason(command.getMissingEachUpcReason(), command.getMissingCaseUpcReason());
        itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(command.getItemId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(command.getItemId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    @Override
    @Transactional(propagation = REQUIRES_NEW)
    public ItemDto refreshPrimaryBackupVendor(UUID id) {
        Item item = fetchItemOrThrow(id);
        ItemDto previous = itemQueryApplicationService.findById(id);

        UUID primaryVendorId = refreshPrimaryVendorId(previous);

        UUID backupVendorId = refreshBackupVendorId(previous);

        if (null == primaryVendorId && null == backupVendorId) {
            log.warn("Primary and backup vendor not found for item: {}", id);
            return previous;
        }

        boolean primaryChanged = !Objects.equals(previous.getPrimaryVendorId(), primaryVendorId);
        boolean backupChanged = !Objects.equals(previous.getBackupVendorId(), backupVendorId);
        if (primaryChanged || backupChanged) {
            item.setPrimaryVendorId(primaryVendorId);
            item.setBackupVendorId(backupVendorId);
            item = itemService.save(item);

            ItemDto current = itemQueryApplicationService.findById(item.getId());
            businessEventService.dispatch(ItemAmendPayloadDto.builder()
                .itemId(item.getId())
                .previous(previous)
                .current(current)
                .build());
            return current;
        }
        return previous;
    }

    @Override
    public BatchUpdateItemPromoPriceResultDto batchUpdatePromoPrice(List<UpdateItemPromoPriceCommand> commands) {
        List<String> failedSkuNumbers = new ArrayList<>();

        for (UpdateItemPromoPriceCommand command : commands) {
            try {
                updatePromoPriceAndSendEvent(command);
            } catch (Exception e) {
                log.error("Failed to update promo price for item {}", command.getItemId(), e);
                failedSkuNumbers.add(getSkuNumberForFailedItem(command.getItemId()));
            }
        }

        int updatedCount = commands.size() - failedSkuNumbers.size();
        return BatchUpdateItemPromoPriceResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }

    @Override
    public ItemDto bindingSkuPhoto(MultipartFile file) {
        String skuNumber = StringUtils.substringBefore(file.getOriginalFilename(), ".");
        Item item = fetchItemBySkuOrThrow(skuNumber);
        return bindingPhotoAndSendEvent(item, file);
    }


    private ItemDto updateItemStatus(UUID id, AvailabilityStatus status) {
        return updateItemStatusWithReason(id, status, null);
    }

    private ItemDto updateItemStatusWithReason(UUID id, AvailabilityStatus status, ArchivedReason archivedReason) {
        Item item = Optional.ofNullable(itemService.findById(id))
            .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND.getCode()));
        ItemDto previous = itemQueryApplicationService.findById(item.getId());

        log.info("Update item status for item: {} to {}", id, status);
        UUID categoryId = previous.getCategoryId();
        if (null == categoryId) {
            throw new ImsBusinessException(CATEGORY_SHOULD_BE_NOT_NULL);
        }

        switch (status) {
            case ACTIVE:
                item.active();
                break;
            case DRAFT:
                item.draft();
                break;
            case ARCHIVED:
                item.archive(archivedReason);
                break;
            default:
                throw new IllegalArgumentException("Unsupported status: " + status);
        }
        item = itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }


    @Override
    public ItemDto updatePrimaryVendor(UpdateItemPrimaryVendorCommand command) {
        ItemDto previous = itemQueryApplicationService.findById(command.getId());
        Item item = fetchItemOrThrow(command.getId());
        if (null != command.getPrimaryVendorId()) {
            VendorItem primaryVendorItem = vendorItemService.findByVendorIDAndItemId(command.getPrimaryVendorId(),
                command.getId());
            if (primaryVendorItem == null) {
                throw new ImsBusinessException(PRIMARY_VENDOR_ITEM_NOT_FOUND);
            }
            if (VendorItemType.JIT.getTypeName()
                .equals(primaryVendorItem.getVendorItemType())) {
                throw new ImsBusinessException(INVALID_PRIMARY_PO_VENDOR);
            }
        }

        item.setPrimaryVendorId(command.getPrimaryVendorId());
        item = itemService.save(item);
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    @Override
    public ItemDto updateBackupVendor(UpdateItemBackupVendorCommand command) {
        ItemDto previous = itemQueryApplicationService.findById(command.getId());

        if (command.getBackupVendorId() != null) {
            VendorItem backupVendorItem = vendorItemService.findByVendorIDAndItemId(command.getBackupVendorId(),
                command.getId());
            if (backupVendorItem == null || VendorItemType.DIRECT.getTypeName()
                .equals(backupVendorItem.getVendorItemType())) {
                throw new ImsBusinessException(INVALID_PRIMARY_JIT_VENDOR);
            }
        }

        Item item = fetchItemOrThrow(command.getId());
        item.setBackupVendorId(command.getBackupVendorId());
        item = itemService.save(item);
        setDefaultBackupVendorItem(item.getId(), item.getBackupVendorId());
        ItemDto current = itemQueryApplicationService.findById(item.getId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(item.getId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    private Item fetchItemOrThrow(UUID id) {
        return Optional.ofNullable(itemService.findById(id))
            .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND));
    }

    private Item fetchItemBySkuOrThrow(String sku) {
        return Optional.ofNullable(itemService.findBySku(sku))
            .orElseThrow(() -> new ImsBusinessException(ErrorCodeEnums.NOT_FOUND, "SKU:" + sku, "for Item"));
    }

    private String getItemPhoto(String sku, String externalImageUrl, String photoName) {
        if (StringUtils.isNotBlank(photoName) && StringUtils.isNotBlank(documentApplicationService.getSignedUrl(photoName))) {
            return photoName;
        }

        if (StringUtils.isBlank(externalImageUrl)) {
            return null;
        }
        byte[] docBytes = FileUtil.downloadFile(externalImageUrl);
        return getItemPhoto(sku, docBytes);
    }

    private String getItemPhoto(String sku, byte[] fileBytes) {
        String fileExtension = FileUtil.getFileExtension(fileBytes);
        if (StringUtils.isBlank(fileExtension)) {
            log.error("[getItemPhoto] Unable to get imageFormat item: {}  ", sku);
            return null;
        }
        String filename = sku.concat("-")
            .concat(DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14))
            .concat(fileExtension);
        DocumentResponse documentResponse = documentApplicationService.uploadImage(fileBytes, filename);
        return documentResponse.getName();
    }


    private String determineVendorItemType(VendorItemDto vendorItemDto, boolean primaryAndBackupSame, UUID primaryVendorId) {
        // If vendor item type is already set, use it
        if (vendorItemDto.getVendorItemType() != null) {
            return vendorItemDto.getVendorItemType();
        }

        // If primary and backup vendors are the same
        if (primaryAndBackupSame) {
            return VendorItemType.DIRECT_JIT.getTypeName();
        }

        // Determine type based on whether this is primary vendor
        return vendorItemDto.getVendorId().equals(primaryVendorId)
            ? VendorItemType.DIRECT.getTypeName()
            : VendorItemType.JIT.getTypeName();
    }

    private void createVendorItems(List<VendorItemDto> vendorItemDtos, Item item, UUID itemAdjustmentRequestDetailId) {
        if (CollectionUtils.isNotEmpty(vendorItemDtos)) {
            boolean primaryAndBackupSame = Objects.equals(item.getPrimaryVendorId(), item.getBackupVendorId());
            vendorItemDtos.forEach(vendorItemDto -> vendorItemApplicationService.create(CreateVendorItemCommand.builder()
                .vendorId(vendorItemDto.getVendorId())
                .itemId(item.getId())
                .vendorSkuNumber(vendorItemDto.getVendorSkuNumber())
                .aisle(vendorItemDto.getAisle())
                .cost(vendorItemDto.getCost())
                .backupCost(vendorItemDto.getBackupCost())
                .vendorItemType(determineVendorItemType(vendorItemDto, primaryAndBackupSame, item.getPrimaryVendorId()))
                .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
                .availability(vendorItemDto.getAvailability())
                .itemVendorRebateDtos(vendorItemDto.getItemVendorRebateDtos())
                .build()));
        }
    }


    private void createItemPromoPrice(UUID itemId,
        List<ItemPromoPriceDto> itemPromoPrices,
        Integer packSize,
        Boolean crvFlag,
        BigDecimal crv) {
        if (CollectionUtils.isNotEmpty(itemPromoPrices)) {
            ItemPromoPriceDto promoPrice = itemPromoPrices.getFirst();

            itemPromoPriceService.save(ItemPromoPriceFactory.create(CreateItemPromoPriceCommand.builder()
                .itemId(itemId)
                .promoPrice(promoPrice.getPromoPrice())
                .promoFlag(promoPrice.getPromoFlag())
                .promoBeginTime(promoPrice.getPromoBeginTime())
                .promoEndTime(promoPrice.getPromoEndTime())
                .build(), packSize, crvFlag, crv));
        }
    }

    private Brand fetchBrandOrThrow(UUID brandId) {
        return Optional.ofNullable(brandService.findById(brandId))
            .orElseThrow(() -> new ImsBusinessException(BRAND_NOT_FOUND));
    }

    private void updateItemImages(Item item,
        String itemPhoto,
        Boolean previousPromoFlag,
        Boolean promoFlag,
        BigDecimal previousRegPrice,
        BigDecimal regPrice) {
        log.info("Update item images for item: {}  itemPhoto: {} promoFlag: {} previousRegPrice :{} regPrice: {}",
            item.getId(), itemPhoto, promoFlag, previousRegPrice, regPrice);
        if (item.getPhoto() != null && item.getPhoto().equals(itemPhoto) && Objects.equals(previousPromoFlag, promoFlag)
            && Objects.equals(previousRegPrice, regPrice)) {
            return;
        }

        List<ItemImage> itemImages = Optional.ofNullable(item.getItemImages()).orElse(new ArrayList<>());

        if (StringUtils.isNotBlank(itemPhoto)) {
            itemImages.removeIf(img -> img.getImageType() == ImageType.MAIN_IMAGE);
            itemImages.add(ItemImage.builder()
                .fileName(itemPhoto)
                .imageType(ImageType.MAIN_IMAGE)
                .build());
            item.setPhoto(itemPhoto);
        }
        ItemImage mainImage = itemImages.stream()
            .filter(img -> img.getImageType() == ImageType.MAIN_IMAGE)
            .findFirst()
            .orElse(null);

        if (mainImage != null && Boolean.TRUE.equals(promoFlag)) {
            ItemImage promoImage = generatePromoPhoto(
                item.getId(),
                mainImage.getFileName(),
                item.getSkuNumber(),
                regPrice
            );
            if (promoImage != null) {
                itemImages.removeIf(img -> img.getImageType() == ImageType.PROMO_IMAGE);
                itemImages.add(promoImage);
            }
        }

        item.updateItemImages(itemImages);

    }

    private void setDefaultBackupVendorItem(UUID itemId, UUID backupVendorId) {
        if (backupVendorId != null) {
            VendorItem backupVendorItem = vendorItemService.findByVendorIDAndItemId(backupVendorId, itemId);
            if (backupVendorItem == null) {
                vendorItemApplicationService.create(CreateVendorItemCommand.builder()
                    .vendorId(backupVendorId)
                    .itemId(itemId)
                    .build());
            }
        }
    }

    private void createItemPrice(UUID itemId,
        BigDecimal regPrice,
        Boolean crvFlag,
        Integer packageSize,
        ItemAttribute bottleSizeAttribute, List<ItemPromoPriceDto> itemPromoPrices) {
        ItemRegPrice itemRegPrice = itemRegPriceService.save(itemId,
            regPrice,
            crvFlag,
            packageSize,
            bottleSizeAttribute);
        createItemPromoPrice(itemId,
            itemPromoPrices,
            packageSize,
            itemRegPrice.getCrvFlag(),
            itemRegPrice.getCrv()
        );

    }

    private List<ItemUPC> convertToItemUPCs(List<ItemUPCDto> targetItemUPCs) {
        List<ItemUPC> itemUPCs = new ArrayList<>();

        targetItemUPCs.stream().filter(itemUPCDto -> StringUtils.isNotBlank(itemUPCDto.getUpcNumber()))
            .forEach(
                itemUPCDto -> itemUPCs.add(ItemUPC.builder()
                    .upcNumber(itemUPCDto.getUpcNumber().trim())
                    .itemUpcType(itemUPCDto.getItemUpcType())
                    .build())
            );
        return itemUPCs;
    }

    private VendorItem getLowestJitCostVendorItem(ItemDto item) {
        List<VendorItem> vendorItems = vendorItemService.findByItemID(item.getId());
        if (CollectionUtils.isEmpty(vendorItems)) {
            return null;
        }

        Instant monthAgo = Instant.now().minus(28, ChronoUnit.DAYS);

        return vendorItems.stream()
            .filter(v -> isValidJitVendorItem(v, monthAgo)
                && priceCostComparison(item.getPrice(), v.getBackupPackPlusCrvCost())
            )
            .min(Comparator.comparing(VendorItem::getBackupPackPlusCrvCost))
            .orElse(null);
    }

    private boolean isValidJitVendorItem(VendorItem v, Instant cutoffTime) {
        return v.getVendorItemType() != null &&
            (VendorItemType.JIT.getTypeName().equals(v.getVendorItemType()) ||
                VendorItemType.DIRECT_JIT.getTypeName().equals(v.getVendorItemType())) &&
            Boolean.TRUE.equals(v.getAvailability()) &&
            v.getBackupPackPlusCrvCost() != null &&
            v.getBackupCostFreshnessTime() != null &&
            v.getBackupCostFreshnessTime().isAfter(cutoffTime);
    }

    private boolean priceCostComparison(BigDecimal price, BigDecimal cost) {
        return price != null && cost != null && price.compareTo(cost) > 0;
    }


    private ItemDto updatePromoPriceAndSendEvent(UpdateItemPromoPriceCommand command) {
        ItemDto previous = itemQueryApplicationService.findById(command.getItemId());

        ItemRegPriceDto itemPromoPrice = previous.getItemRegPrice();
        updateItemPromoPrice(
            previous.getId(),
            command.getPromoPrice(),
            command.getPromoFlag(),
            command.getPromoBeginTime(),
            command.getPromoEndTime(),
            previous.getPackageSize(),
            itemPromoPrice.getCrvFlag(),
            itemPromoPrice.getCrv()
        );

        if (Boolean.TRUE.equals(command.getPromoFlag()) && !Boolean.TRUE.equals(previous.getPromoFlag())) {
            Item item = fetchItemOrThrow(command.getItemId());
            List<ItemImage> itemImages = item.getItemImages();
            ItemImage promoImage = generatePromoPhoto(previous.getId(),
                previous.getPhotoName(),
                previous.getSkuNumber(),
                previous.getRegPrice());
            if (promoImage != null) {
                itemImages.removeIf(img -> img.getImageType() == ImageType.PROMO_IMAGE);
                itemImages.add(promoImage);
            }
            item.updateItemImages(itemImages);
            itemService.save(item);
        }

        // Send event after price update
        ItemDto current = itemQueryApplicationService.findById(command.getItemId());
        businessEventService.dispatch(ItemAmendPayloadDto.builder()
            .itemId(command.getItemId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    private String getSkuNumberForFailedItem(UUID itemId) {
        try {
            ItemDto failedItem = itemQueryApplicationService.findById(itemId);
            return failedItem != null ? failedItem.getSkuNumber() : itemId.toString();
        } catch (Exception e) {
            return itemId.toString();
        }
    }

    private UUID refreshPrimaryVendorId(ItemDto item) {
        List<VendorItem> vendorItems = vendorItemService.findByItemID(item.getId());

        // Early return if no vendor items or no direct vendor items
        if (CollectionUtils.isEmpty(vendorItems) || !hasDirectVendorItems(vendorItems)) {
            return null;
        }

        UUID currentPrimaryVendorId = item.getPrimaryVendorId();

        // Check if current primary vendor is still valid
        if (currentPrimaryVendorId != null && !isCurrentPrimaryVendorValid(currentPrimaryVendorId, vendorItems)) {
            currentPrimaryVendorId = null;
        }

        // If no valid primary vendor and no JIT vendors exist, find the lowest cost direct vendor
        if (currentPrimaryVendorId == null && !hasJitVendorItems(vendorItems)) {
            return findLowestCostDirectVendor(vendorItems);
        }

        return currentPrimaryVendorId;
    }

    /**
     * Check if there are any direct vendor items
     */
    private boolean hasDirectVendorItems(List<VendorItem> vendorItems) {
        return vendorItems.stream().anyMatch(VendorItem::isDirectVendorItem);
    }

    /**
     * Check if there are any JIT vendor items
     */
    private boolean hasJitVendorItems(List<VendorItem> vendorItems) {
        return vendorItems.stream().anyMatch(VendorItem::isJitVendorItem);
    }

    /**
     * Check if the current primary vendor is still valid A primary vendor becomes invalid if: 1. No vendor item matches the
     * primary vendor ID 2. The matching vendor item has changed to JIT type
     */
    private boolean isCurrentPrimaryVendorValid(UUID primaryVendorId, List<VendorItem> vendorItems) {
        Optional<VendorItem> primaryVendorItem = vendorItems.stream()
            .filter(v -> primaryVendorId.equals(v.getVendorId()))
            .findFirst();

        if (primaryVendorItem.isEmpty()) {
            return false; // No matching vendor found
        }

        // Check if primary vendor is now JIT type (invalid for primary vendor)
        return !VendorItemType.JIT.getTypeName().equals(primaryVendorItem.get().getVendorItemType());
    }

    /**
     * Find the direct vendor with lowest cost
     */
    private UUID findLowestCostDirectVendor(List<VendorItem> vendorItems) {
        return vendorItems.stream()
            .filter(v -> v.isDirectVendorItem() && v.getVendorId() != null)
            .min(Comparator.comparing(VendorItem::getCost))
            .map(VendorItem::getVendorId)
            .orElse(null);
    }


    private UUID refreshBackupVendorId(ItemDto item) {
        UUID backupVendorId = item.getBackupVendorId();
        VendorItem lowestCostJitVendorItem = getLowestJitCostVendorItem(item);
        List<VendorItemDto> vendorItemDtos = item.getVendorItemDtos();

        if (lowestCostJitVendorItem == null) {
            boolean hasAvailableJitVendor = vendorItemDtos.stream()
                .filter(v -> Boolean.TRUE.equals(v.getAvailability()))
                .anyMatch(VendorItemDto::isJitVendorItem);

            if (!hasAvailableJitVendor) {
                return null;
            }

            if (backupVendorId != null && item.getBackupVendorItem() != null && !item.getBackupVendorItem().isJitVendorItem()) {
                backupVendorId = null;
            }

            if (backupVendorId == null && item.getPrimaryVendorId() == null) {
                return vendorItemDtos.stream()
                    .filter(v -> v.isJitVendorItem() && v.getVendorId() != null
                        && Boolean.TRUE.equals(v.getAvailability()))
                    .map(VendorItemDto::getVendorId)
                    .findFirst()
                    .orElse(null);
            }
            return backupVendorId;
        }

        return lowestCostJitVendorItem.getVendorId();
    }

    private ItemImage generatePromoPhoto(UUID itemId, String photoName, String skuNumber, BigDecimal regPrice) {
        try {
            log.info("Generating promo photo for item: {}", itemId);

            byte[] photo = documentApplicationService.downloadDocument(photoName);

            String locationName = "classpath:file/promo_label.png";
            Resource resource = resourceLoader.getResource(locationName);
            byte[] labelPhoto = resource.getInputStream().readAllBytes();
            NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);

            String formattedPrice = currencyFormat.format(regPrice);
            byte[] promoFileBytes = ImageOverlayUtil.overlayLabel(photo, labelPhoto, "Was " + formattedPrice);

            String itemPromoPhoto = getItemPhoto(skuNumber + "-promo", promoFileBytes);
            log.info("Generated promo photo :{} for item: {}", itemPromoPhoto, itemId);

            return ItemImage.builder()
                .itemId(itemId)
                .fileName(itemPromoPhoto)
                .imageType(ImageType.PROMO_IMAGE)
                .build();


        } catch (Exception e) {
            log.error("Error generating promo photo for item: {}", itemId, e);
            return null;
        }

    }

    private ItemDto bindingPhotoAndSendEvent(Item item, MultipartFile file) {
        ItemDto previous = itemQueryApplicationService.findById(item.getId());
        log.info("[bindingSkuPhoto] Binding photo for item with sku: {} ", item.getSkuNumber());
        try {
            String itemPhoto = getItemPhoto(item.getSkuNumber(), file.getBytes());
            updateItemImages(item,
                itemPhoto,
                previous.getPromoFlag(),
                previous.getPromoFlag(),
                previous.getRegPrice(),
                previous.getRegPrice());
            item = itemService.save(item);
            ItemDto current = itemQueryApplicationService.findById(item.getId());
            businessEventService.dispatch(ItemAmendPayloadDto.builder()
                .itemId(item.getId())
                .previous(previous)
                .current(current)
                .build());
            log.info("[bindingSkuPhoto] end binding photo for itemDto: {} ", current);
            return current;
        } catch (Exception e) {
            log.error("Error binding photo for item with sku: {} ", item.getSkuNumber(), e);
            throw new ImsBusinessException(ITEM_BINDING_PHOTO_ERROR);
        }
    }

    /**
     * Update backup vendor only when there are price changes and backup vendor has changed
     */
    private void updateBackupVendorIfNeeded(Item item, UpdateItemCommand updateItemCommand, ItemDto previous) {
        // Early return if no price changes
        if (!hasPriceChanges(updateItemCommand, previous)) {
            return;
        }

        UUID newBackupVendorId = refreshBackupVendorId(previous);
        if (!Objects.equals(previous.getBackupVendorId(), newBackupVendorId)) {
            item.setBackupVendorId(newBackupVendorId);
        }
    }

    /**
     * Check if there are any price changes (regular or promo price)
     */
    private boolean hasPriceChanges(UpdateItemCommand updateItemCommand, ItemDto previous) {
        boolean regPriceChange =
            updateItemCommand.getRegPrice() != null
                && !isSamePrice(updateItemCommand.getRegPrice(), previous.getRegPrice());

        if (CollectionUtils.isEmpty(updateItemCommand.getItemPromoPrices())) {
            return regPriceChange;
        }

        ItemPromoPriceDto promoPrice = updateItemCommand.getItemPromoPrices().getFirst();

        boolean promoPriceChange =
            (Boolean.TRUE.equals(previous.getPromoFlag())
                || Boolean.TRUE.equals(promoPrice.getPromoFlag()))
                && promoPrice.getPromoPrice() != null
                && !isSamePrice(promoPrice.getPromoPrice(), previous.getPromoPrice());

        return regPriceChange || promoPriceChange;
    }

    /**
     * Compare two BigDecimal values safely, handling null values Uses compareTo() to ignore scale differences (e.g., 10.50 equals
     * 10.5)
     */
    private boolean isSamePrice(BigDecimal price1, BigDecimal price2) {
        if (price1 == null && price2 == null) {
            return true;
        }
        if (price1 == null || price2 == null) {
            return false;
        }
        return price1.compareTo(price2) == 0;
    }

}
