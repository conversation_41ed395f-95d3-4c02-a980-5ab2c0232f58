package com.mercaso.ims.domain.itemversion.service;

import com.mercaso.ims.domain.itemversion.ItemVersion;
import java.util.List;
import java.util.UUID;

public interface ItemVersionService {

    ItemVersion findBySkuAndVersion(String skuNumber, Integer version);

    ItemVersion findByItemIdAndVersion(UUID itemId, Integer version);

    List<ItemVersion> findByItemId(UUID itemId);

    ItemVersion save (ItemVersion itemVersion);

    ItemVersion update (ItemVersion itemVersion);

    ItemVersion delete (UUID id);


}
