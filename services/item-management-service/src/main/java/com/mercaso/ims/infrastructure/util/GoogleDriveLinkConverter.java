package com.mercaso.ims.infrastructure.util;

import java.net.MalformedURLException;
import java.net.URL;

public class GoogleDriveLinkConverter {

    private GoogleDriveLinkConverter() {
        throw new IllegalStateException("Utility class");
    }

    public static String getDirectImageUrl(String sharedLink) {
        String fileId = extractFileId(sharedLink);
        if (fileId == null) {
            throw new IllegalArgumentException("Invalid Google Drive shared link.");
        }
        return "https://drive.google.com/uc?export=view&id=" + fileId;
    }

    private static String extractFileId(String sharedLink) {
        String regex = "/file/d/(\\S+)/view\\?usp=sharing";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(sharedLink);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public static boolean isGoogleShareLink(String urlString) {
        try {
            URL url = new URL(urlString);
            String host = url.getHost();
            String path = url.getPath();

            return "drive.google.com".equals(host) && path.startsWith("/file/d/") && (path.contains("/view")
                || path.contains("/edit"));
        } catch (MalformedURLException e) {
            return false;
        }
    }
}