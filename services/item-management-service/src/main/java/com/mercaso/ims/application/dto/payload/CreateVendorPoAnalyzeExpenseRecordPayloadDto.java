package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.VendorPoAnalyzeRecordDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreateVendorPoAnalyzeExpenseRecordPayloadDto extends BusinessEventPayloadDto<VendorPoAnalyzeRecordDto> {

    private UUID vendorPoAnalyzeRecordId;

    @Builder
    public CreateVendorPoAnalyzeExpenseRecordPayloadDto(VendorPoAnalyzeRecordDto data, UUID vendorPoAnalyzeRecordId) {
        super(data);
        this.vendorPoAnalyzeRecordId = vendorPoAnalyzeRecordId;
    }
}