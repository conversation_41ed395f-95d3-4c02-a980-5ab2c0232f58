package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestCompletedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestProcessedApplicationEvent;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCompletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestProcessedPayloadDto;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.infrastructure.excel.processor.ItemAdjustmentRequestTemplateSheetProcessor;
import com.mercaso.ims.infrastructure.excel.processor.ItemVendorRebateRequestTemplateSheetProcessor;
import com.mercaso.ims.infrastructure.excel.processor.UpdateVendorCostTemplateSheetProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ItemAdjustmentRequestApplicationEventListener {

    private final ItemAdjustmentRequestTemplateSheetProcessor mecasoItemAdjustmentRequestSheetProcessor;
    private final UpdateVendorCostTemplateSheetProcessor updateVendorCostTemplateSheetProcessor;
    private final ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    private final ItemVendorRebateRequestTemplateSheetProcessor itemVendorRebateRequestTemplateSheetProcessor;


    @EventListener
    @Async("taskExecutor")
    public void handleItemAdjustmentRequestCreatedApplicationEvent(ItemAdjustmentRequestCreatedApplicationEvent requestCreatedApplicationEvent) {
        ItemAdjustmentRequestCreatedPayloadDto requestCreatedPayloadDto = requestCreatedApplicationEvent.getPayload();
        ItemAdjustmentRequestDto requestDto = requestCreatedPayloadDto.getData();
        try {

            switch (requestDto.getType()) {
                case NEW_TEMPLATE_ADJUSTMENT:
                    mecasoItemAdjustmentRequestSheetProcessor.process(requestDto);
                    break;
                case COSTCO_VENDOR_COST_UPDATE:
                    updateVendorCostTemplateSheetProcessor.process(requestDto, VendorConstant.COSTCO);
                    break;
                case VERNON_VENDOR_COST_UPDATE:
                    updateVendorCostTemplateSheetProcessor.process(requestDto, VendorConstant.VERNON_SALES);
                    break;
                case ITEM_VENDOR_REBATE:
                    itemVendorRebateRequestTemplateSheetProcessor.process(requestDto);
                    break;
                default:
                    break;
            }

            log.info("handleItemAdjustmentRequestCreatedApplicationEvent for request={}", requestCreatedPayloadDto);
        } catch (Exception e) {
            log.error("handleItemAdjustmentRequestCreatedApplicationEvent error for request={}", requestCreatedPayloadDto, e);
            itemAdjustmentRequestApplicationService.processedFailure(requestDto.getId(),
                "Failed to read the contents of the file, please check the contents.");
        }
    }

    @EventListener
    public void handleItemAdjustmentRequestCompletedApplicationEvent(ItemAdjustmentRequestCompletedApplicationEvent requestCompletedApplicationEvent) {
        ItemAdjustmentRequestCompletedPayloadDto requestCompletedPayloadDto = requestCompletedApplicationEvent.getPayload();

        log.info("handleItemAdjustmentRequestCompletedApplicationEvent for request={}", requestCompletedPayloadDto);
    }

    @EventListener
    public void handleItemAdjustmentRequestProcessedApplicationEvent(ItemAdjustmentRequestProcessedApplicationEvent requestProcessedApplicationEvent) {
        ItemAdjustmentRequestProcessedPayloadDto requestProcessedPayloadDto = requestProcessedApplicationEvent.getPayload();
        itemAdjustmentRequestApplicationService.checkAndComplete(requestProcessedPayloadDto.getItemAdjustmentRequestId());
        log.info("handleItemAdjustmentRequestProcessedApplicationEvent for request={}", requestProcessedPayloadDto);

    }
}
