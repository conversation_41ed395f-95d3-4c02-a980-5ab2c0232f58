package com.mercaso.ims.application.service;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import java.io.IOException;
import java.util.UUID;
import org.springframework.web.multipart.MultipartFile;

public interface ItemAdjustmentRequestApplicationService {

    DocumentResponse downloadItemAdjustmentDetail(UUID itemAdjustmentRequestId) throws IOException;

    ItemAdjustmentRequestDto uploadItemAdjustmentRequest(MultipartFile file, ItemAdjustmentRequestType type, String timeZone);

    DocumentResponse getItemAdjustmentRequestFile(UUID id);

    ItemAdjustmentRequestDto addCountOfParsed(UUID id,
        Integer createdRowCount,
        Integer modifiedRowCount,
        Integer deletedRowCount);

    void checkAndComplete(UUID id);

    void finishProcessed(UUID id);

    void processedFailure(UUID id, String failureReason);


}
