package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.application.dto.ItemPriceGroupListDto;
import com.mercaso.ims.application.query.ItemPriceGroupQuery;
import com.mercaso.ims.application.searchservice.ItemPriceGroupSearchApplicationService;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping(value = "/v1/search/item-price-group", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
@Validated
public class SearchItemPriceGroupRestApi {

    private final ItemPriceGroupSearchApplicationService itemPriceGroupSearchApplicationService;


    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:items')")
    public ItemPriceGroupListDto searchItemPriceGroupRequest(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        @Max(value = 100, message = "Page size must not exceed 100")
        int pageSize,
        @RequestParam(value = "createdStartDate", required = false) Instant createdStartDate,
        @RequestParam(value = "groupName", required = false) String groupName,
        @RequestParam(value = "createdEndDate", required = false) Instant createdEndDate,
        @RequestParam(value = "createdBy", required = false) String createdBy,
        @RequestParam(value = "sort", required = false) ItemPriceGroupQuery.SortType sort) {
    
        log.info(
            "[searchItemPriceGroupRequest] param groupName :{} createdStartDate: {}, created EndDat: {}, createdUserName: {}, sort: {}.",
            groupName,
            createdStartDate,
            createdEndDate,
            createdBy,
            sort);
        
        return itemPriceGroupSearchApplicationService.searchItemPriceGroupList(ItemPriceGroupQuery
            .builder()
            .page(page)
            .pageSize(pageSize)
            .createdBy(createdBy)
            .createdAtBegin(createdStartDate)
            .createdAtEnd(createdEndDate)
            .groupName(groupName)
            .sort(sort)
            .build());
    }

}
