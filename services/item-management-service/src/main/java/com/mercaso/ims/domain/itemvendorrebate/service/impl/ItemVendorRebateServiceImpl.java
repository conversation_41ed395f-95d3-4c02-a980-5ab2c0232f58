package com.mercaso.ims.domain.itemvendorrebate.service.impl;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemVendorRebateServiceImpl implements ItemVendorRebateService {

    private final ItemVendorRebateRepository itemVendorRebateRepository;

    @Override
    public ItemVendorRebate save(ItemVendorRebate itemVendorRebate) {
        return itemVendorRebateRepository.save(itemVendorRebate);
    }

    @Override
    public ItemVendorRebate update(ItemVendorRebate itemVendorRebate) {
        return itemVendorRebateRepository.update(itemVendorRebate);
    }

    @Override
    public void delete(UUID id) {
        itemVendorRebateRepository.deleteById(id);
    }

    @Override
    public ItemVendorRebate findById(UUID id) {
        return itemVendorRebateRepository.findById(id);
    }

    @Override
    public List<ItemVendorRebate> findByVendorItemId(UUID vendorItemId) {
        return itemVendorRebateRepository.findByVendorItemId(vendorItemId);
    }

    @Override
    public List<ItemVendorRebate> findByVendorIdAndItemId(UUID vendorId, UUID itemId) {
        return itemVendorRebateRepository.findByVendorIdAndItemId(vendorId, itemId);
    }

    @Override
    public List<ItemVendorRebate> findByVendorItemIds(List<UUID> vendorItemIds) {
        return itemVendorRebateRepository.findByVendorItemIds(vendorItemIds);
    }
}
