package com.mercaso.ims.infrastructure.excel.factory;

import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.infrastructure.excel.strategy.BulkExportStrategy;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class BulkExportStrategyFactory {

    private final Map<ExportType, BulkExportStrategy> strategyMap;

    public BulkExportStrategyFactory(List<BulkExportStrategy> strategies) {
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(BulkExportStrategy::getExportType, Function.identity()));
    }

    public BulkExportStrategy getStrategy(ExportType exportType) {
        BulkExportStrategy strategy = strategyMap.get(exportType);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported export type: " + exportType);
        }
        return strategy;
    }
}
