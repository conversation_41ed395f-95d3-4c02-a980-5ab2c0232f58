package com.mercaso.ims.application.mapper.brand;

import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.ims.domain.brand.Brand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BrandDtoApplicationMapper extends BaseDtoApplicationMapper<Brand, BrandDto> {

    @Override
    @Mapping(source = "id", target = "brandId")
    @Mapping(source = "name", target = "brandName")
    BrandDto domainToDto(Brand domain);

}
