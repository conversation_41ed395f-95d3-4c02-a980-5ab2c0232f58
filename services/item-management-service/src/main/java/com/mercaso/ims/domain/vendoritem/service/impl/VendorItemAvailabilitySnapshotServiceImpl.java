package com.mercaso.ims.domain.vendoritem.service.impl;

import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshot;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotDetail;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotRepository;
import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;
import com.mercaso.ims.domain.vendoritem.service.VendorItemAvailabilitySnapshotService;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class VendorItemAvailabilitySnapshotServiceImpl implements VendorItemAvailabilitySnapshotService {

    private final VendorItemAvailabilitySnapshotRepository snapshotRepository;

    @Override
    public VendorItemAvailabilitySnapshot save(VendorItemAvailabilitySnapshot snapshot) {
        return snapshotRepository.save(snapshot);
    }

    @Override
    public VendorItemAvailabilitySnapshot findById(UUID id) {
        return snapshotRepository.findById(id);
    }

    @Override
    public List<VendorItemAvailabilitySnapshot> findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(UUID vendorId,
        SnapshotType snapshotType) {
        return snapshotRepository.findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(vendorId, snapshotType);
    }

    @Override
    public Optional<VendorItemAvailabilitySnapshot> findLatestByVendorIdAndSnapshotType(UUID vendorId,
        SnapshotType snapshotType) {
        return snapshotRepository.findLatestByVendorIdAndSnapshotType(vendorId, snapshotType);
    }

    @Override
    @Transactional
    public void createShutdownSnapshot(UUID vendorId, List<VendorItemAvailabilitySnapshotDetail> details) {
        VendorItemAvailabilitySnapshot snapshot = VendorItemAvailabilitySnapshot.builder()
            .vendorId(vendorId)
            .snapshotTime(Instant.now())
            .snapshotType(SnapshotType.SHUTDOWN)
            .build();

        snapshotRepository.saveSnapshotWithDetails(snapshot, details);
        log.info("Created shutdown snapshot for vendor: {} with {} details", vendorId, details.size());
    }

    @Override
    @Transactional
    public void createRestoreSnapshot(UUID vendorId, List<VendorItemAvailabilitySnapshotDetail> details) {
        VendorItemAvailabilitySnapshot snapshot = VendorItemAvailabilitySnapshot.builder()
            .vendorId(vendorId)
            .snapshotTime(Instant.now())
            .snapshotType(SnapshotType.RESTORE)
            .build();

        snapshotRepository.saveSnapshotWithDetails(snapshot, details);
        log.info("Created restore snapshot for vendor: {} with {} details", vendorId, details.size());
    }

    @Override
    public List<VendorItemAvailabilitySnapshotDetail> getLatestShutdownSnapshotDetails(UUID vendorId) {
        Optional<VendorItemAvailabilitySnapshot> latestSnapshot = findLatestByVendorIdAndSnapshotType(vendorId,
            SnapshotType.SHUTDOWN);
        return latestSnapshot.map(VendorItemAvailabilitySnapshot::getDetails).orElse(List.of());
    }
} 