package com.mercaso.ims.interfaces.rest;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.AWS_ANALYZE_EXPENSE_ERROR;

import com.mercaso.ims.application.service.AwsAnalyzeApplicationService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping(value = "/v1/analyze-po-invoice", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class AnalyzeVendorPoRestApi {

    private final AwsAnalyzeApplicationService awsAnalyzeApplicationService;

    @PostMapping("/downey-cost")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public void analyzeDowneyCost(@RequestParam("requestFile") MultipartFile file) {
        log.info("[analyzeDowneyCost] request analyzeLineItem.");
        try {
            awsAnalyzeApplicationService.analyzeDowneyExpense(file.getBytes(), file.getOriginalFilename());
        } catch (Exception e) {
            log.error("Failed to analyze expense: ", e);
            throw new ImsBusinessException(AWS_ANALYZE_EXPENSE_ERROR);
        }
    }
}
