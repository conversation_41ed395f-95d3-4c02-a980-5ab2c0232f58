package com.mercaso.ims.application.mapper.itemadjustmentrequest;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ItemAdjustmentRequestDtoApplicationMapper extends
    BaseDtoApplicationMapper<ItemAdjustmentRequest, ItemAdjustmentRequestDto> {

    @Override
    @Mapping(target = "timeZone", ignore = true)
    ItemAdjustmentRequestDto domainToDto(ItemAdjustmentRequest domain);
}
