package com.mercaso.ims.infrastructure.excel.processor;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.*;
import com.mercaso.ims.infrastructure.excel.listener.*;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class ItemVendorRebateRequestTemplateSheetProcessor {

    private final ItemRepository itemRepository;

    private final ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;
    private final ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    private final DocumentApplicationService documentApplicationService;
    private final VendorRepository vendorRepository;
    private final VendorItemRepository vendorItemRepository;
    private final CategoryApplicationService categoryApplicationService;
    private final FeatureFlagsManager featureFlagsManager;
    private final BrandRepository brandRepository;
    private final ItemVendorRebateService itemVendorRebateService;

    public void process(ItemAdjustmentRequestDto requestDto) {

        UUID requestId = requestDto.getId();
        String requestFile = requestDto.getRequestFile();
        String timeZone = requestDto.getTimeZone();
        byte[] document = documentApplicationService.downloadDocument(requestFile);
        try (ExcelReader excelReader = EasyExcelFactory.read(new ByteArrayInputStream(document)).build()) {

            ReadSheet createItemVendorRebateRequestSheet =
                    EasyExcelFactory.readSheet("Create Item Vendor Rebates")
                            .head(CreateItemVendorRebateRequestData.class)
                            .registerReadListener(new CreateItemVendorRebateRequestDataListener(requestId, timeZone,
                                    itemAdjustmentRequestDetailApplicationService,
                                    itemAdjustmentRequestApplicationService,
                                    itemRepository,
                                    vendorRepository, vendorItemRepository,
                                    categoryApplicationService, featureFlagsManager, brandRepository, itemVendorRebateService))
                            .build();

            ReadSheet updateItemVendorRebateRequestSheet =
                    EasyExcelFactory.readSheet("Update Item Vendor Rebates")
                            .head(UpdateItemVendorRebateRequestData.class)
                            .registerReadListener(new UpdateItemVendorRebateRequestDataListener(requestId, timeZone,
                                    itemAdjustmentRequestDetailApplicationService,
                                    itemAdjustmentRequestApplicationService,
                                    itemRepository,
                                    vendorRepository, vendorItemRepository,
                                    categoryApplicationService, featureFlagsManager, brandRepository, itemVendorRebateService))
                            .build();

            ReadSheet deleteItemVendorRebateRequestSheet =
                    EasyExcelFactory.readSheet("Delete Item Vendor Rebates")
                            .head(DeleteItemVendorRebateRequestData.class)
                            .registerReadListener(
                                    new DeleteItemVendorRebateRequestDataListener(
                                            requestId,
                                            itemAdjustmentRequestDetailApplicationService,
                                            itemAdjustmentRequestApplicationService,
                                            itemRepository,
                                            vendorRepository,
                                            vendorItemRepository,
                                            categoryApplicationService,
                                            featureFlagsManager,
                                            brandRepository, itemVendorRebateService))
                            .build();

            excelReader.read(
                createItemVendorRebateRequestSheet,
                updateItemVendorRebateRequestSheet,
                deleteItemVendorRebateRequestSheet
            );
            itemAdjustmentRequestApplicationService.finishProcessed(requestId);
        }

    }
}
