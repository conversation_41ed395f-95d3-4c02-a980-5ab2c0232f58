package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_PRICE_GROUP_ALREADY_EXISTS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_PRICE_GROUP_NOT_FOUND;

import com.mercaso.ims.application.command.BatchBindingItemToPriceGroupCommand;
import com.mercaso.ims.application.command.BatchUnbindingItemFromPriceGroupCommand;
import com.mercaso.ims.application.command.CreateItemPriceGroupCommand;
import com.mercaso.ims.application.command.UpdateItemPriceGroupCommand;
import com.mercaso.ims.application.dto.*;
import com.mercaso.ims.application.dto.payload.ItemPriceGroupAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemPriceGroupCreatedPayloadDto;
import com.mercaso.ims.application.queryservice.ItemVendorRebateQueryApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.application.service.ItemPriceGroupApplicationService;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.enums.EntityEnums;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.service.ItemPriceGroupService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itempricegroup.jpa.CustomizedItemPriceGroupJpaDao;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ItemPriceGroupApplicationServiceImpl implements ItemPriceGroupApplicationService {

    private final BusinessEventService businessEventService;
    private final ItemPriceGroupService itemPriceGroupService;
    private final CustomizedItemPriceGroupJpaDao customizedItemPriceGroupJpaDao;
    private final ItemPriceApplicationService itemPriceApplicationService;
    private final ItemService itemService;
    private final ItemVendorRebateQueryApplicationService itemVendorRebateQueryApplicationService;

    @Override
    public ItemPriceGroupDto create(CreateItemPriceGroupCommand command) {
        ItemPriceGroup itemPriceGroup = itemPriceGroupService.findByGroupName(command.getGroupName());
        if (!Objects.isNull(itemPriceGroup)) {
            throw new ImsBusinessException(ITEM_PRICE_GROUP_ALREADY_EXISTS);
        }

        ItemPriceGroup result = itemPriceGroupService.save(ItemPriceGroup.builder()
            .groupName(command.getGroupName())
            .price(command.getPrice())
            .build());
        UUID itemPriceGroupId = result.getId();
        if (CollectionUtils.isNotEmpty(command.getItemIds())) {
            command.getItemIds().forEach(itemId -> itemPriceApplicationService.bndingItemPriceGroup(itemId, itemPriceGroupId));
        }

        ItemPriceGroupDto itemPriceGroupDto = findItemPriceGroup(itemPriceGroupId);
        businessEventService.dispatch(
            ItemPriceGroupCreatedPayloadDto.builder()
                .itemPriceGroupId(itemPriceGroupDto.getId())
                .data(itemPriceGroupDto)
                .build()
        );

        return itemPriceGroupDto;
    }

    @Override
    public ItemPriceGroupDto update(UpdateItemPriceGroupCommand command) {
        ItemPriceGroup itemPriceGroupByName = itemPriceGroupService.findByGroupName(command.getGroupName());
        if (!Objects.isNull(itemPriceGroupByName) && !itemPriceGroupByName.getId().equals(command.getId())) {
            throw new ImsBusinessException(ITEM_PRICE_GROUP_ALREADY_EXISTS);
        }
        ItemPriceGroupDto previous = findItemPriceGroup(command.getId());

        ItemPriceGroup itemPriceGroup = itemPriceGroupService.findById(command.getId());
        itemPriceGroup.setGroupName(command.getGroupName());
        itemPriceGroup.setPrice(command.getPrice());
        ItemPriceGroup result = itemPriceGroupService.save(itemPriceGroup);

        ItemPriceGroupDto current = findItemPriceGroup(result.getId());
        businessEventService.dispatch(
            ItemPriceGroupAmendPayloadDto.builder()
                .itemPriceGroupId(current.getId())
                .previous(previous)
                .current(current)
                .build()
        );

        return current;

    }

    @Override
    public ItemPriceGroupDto findItemPriceGroup(UUID id) {
        ItemPriceGroup itemPriceGroup = itemPriceGroupService.findById(id);
        if (itemPriceGroup == null) {
            throw new ImsBusinessException(ITEM_PRICE_GROUP_NOT_FOUND);
        }
        List<ItemPriceGroupItemDto> itemPriceGroupItemDtos = customizedItemPriceGroupJpaDao.getItemPriceGroupItemDto(id);

        List<UUID> vendorItemIds = itemPriceGroupItemDtos.stream()
            .map(ItemPriceGroupItemDto::getPrimaryVendorItemId)
            .filter(Objects::nonNull)
            .distinct()
            .toList();

        List<ItemVendorRebateDto> itemVendorRebateDtos = itemVendorRebateQueryApplicationService.findByVendorItemIds(vendorItemIds);

        Map<UUID, List<ItemVendorRebateDto>> rebatesByVendorItemId = itemVendorRebateDtos.stream()
            .collect(Collectors.groupingBy(ItemVendorRebateDto::getVendorItemId));

        itemPriceGroupItemDtos.forEach(itemDto -> {
            UUID primaryVendorItemId = itemDto.getPrimaryVendorItemId();
            List<ItemVendorRebateDto> rebatesForItem = primaryVendorItemId != null 
                ? rebatesByVendorItemId.getOrDefault(primaryVendorItemId, List.of())
                : List.of();
            itemDto.setItemVendorRebateDtos(rebatesForItem);
        });

        return ItemPriceGroupDto.builder()
            .id(itemPriceGroup.getId())
            .items(itemPriceGroupItemDtos)
            .groupName(itemPriceGroup.getGroupName())
            .price(itemPriceGroup.getPrice())
            .createdAt(itemPriceGroup.getCreatedAt())
            .createdBy(itemPriceGroup.getCreatedBy())
            .createdUserName(itemPriceGroup.getCreatedUserName())
            .build();
    }

    @Override
    public List<ItemPriceGroupAuditHistoryInfoDto> getAuditHistories(UUID id) {
        List<BusinessEvent> businessEvents = businessEventService.findByEntityIdAndType(id,
            EntityEnums.ITEM_PRICE_GROUP.getValue());
        if (CollectionUtils.isEmpty(businessEvents)) {
            return List.of();
        }
        return businessEvents.stream()
            .map(this::buildItemPriceGroupAuditHistoryInfoDto)
            .toList();
    }

    @Override
    public BatchBindingItemPriceGroupResultDto batchBindingItemsToPriceGroup(BatchBindingItemToPriceGroupCommand command) {
        List<String> failedSkuNumbers = new ArrayList<>();
        UUID itemPriceGroupId = command.getItemPriceGroupId();
        ItemPriceGroupDto previous = findItemPriceGroup(itemPriceGroupId);

        command.getItemIds().forEach(itemId -> {
            try {
                itemPriceApplicationService.bndingItemPriceGroup(itemId, itemPriceGroupId);
            } catch (Exception e) {
                log.error("Failed to binding item price group for item {}", itemId, e);
                failedSkuNumbers.add(getSkuNumberForFailedItem(itemId));
            }
        });
        int updatedCount = command.getItemIds().size() - failedSkuNumbers.size();
        ItemPriceGroupDto current = findItemPriceGroup(itemPriceGroupId);
        businessEventService.dispatch(
            ItemPriceGroupAmendPayloadDto.builder()
                .itemPriceGroupId(current.getId())
                .previous(previous)
                .current(current)
                .build()
        );
        return BatchBindingItemPriceGroupResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }

    @Override
    public BatchUnbindingItemPriceGroupResultDto batchUnbindingItemsFromPriceGroup(BatchUnbindingItemFromPriceGroupCommand command) {
        List<String> failedSkuNumbers = new ArrayList<>();
        UUID itemPriceGroupId = command.getItemPriceGroupId();
        ItemPriceGroupDto previous = findItemPriceGroup(itemPriceGroupId);

        command.getItemIds().forEach(itemId -> {
            try {
                itemPriceApplicationService.unbindingItemPriceGroup(itemId, itemPriceGroupId);
            } catch (Exception e) {
                log.error("Failed to unbinding item price group for item {}", itemId, e);
                failedSkuNumbers.add(getSkuNumberForFailedItem(itemId));
            }
        });
        int updatedCount = command.getItemIds().size() - failedSkuNumbers.size();
        ItemPriceGroupDto current = findItemPriceGroup(itemPriceGroupId);
        businessEventService.dispatch(
            ItemPriceGroupAmendPayloadDto.builder()
                .itemPriceGroupId(current.getId())
                .previous(previous)
                .current(current)
                .build()
        );
        return BatchUnbindingItemPriceGroupResultDto.builder()
            .updatedCount(updatedCount)
            .failedSkuNumbers(failedSkuNumbers)
            .build();
    }

    @Override
    public void deleteItemPriceGroup(UUID id) {
        ItemPriceGroupDto itemPriceGroup = findItemPriceGroup(id);
        if (CollectionUtils.isNotEmpty(itemPriceGroup.getItems())) {
            throw new ImsBusinessException("Cannot delete item price group with bound items.");
        }
        itemPriceGroupService.deleteById(id);
        log.info("ItemPriceGroup with ID {} deleted successfully", id);
    }


    private ItemPriceGroupAuditHistoryInfoDto buildItemPriceGroupAuditHistoryInfoDto(BusinessEvent businessEvent) {
        return switch (businessEvent.getType()) {
            case ITEM_PRICE_GROUP_CREATED -> {
                ItemPriceGroupCreatedPayloadDto payload = SerializationUtils.deserialize(businessEvent.getPayload(),
                    ItemPriceGroupCreatedPayloadDto.class);
                yield ItemPriceGroupAuditHistoryInfoDto.builder()
                    .previous(null)
                    .current(payload.getData())
                    .type(businessEvent.getType())
                    .updatedBy(businessEvent.getCreatedBy())
                    .updatedAt(businessEvent.getCreatedAt())
                    .updatedUserName(businessEvent.getCreatedUserName())
                    .build();
            }
            case ITEM_PRICE_GROUP_AMEND -> {
                ItemPriceGroupAmendPayloadDto amendPayloadDto = SerializationUtils.deserialize(businessEvent.getPayload(),
                    ItemPriceGroupAmendPayloadDto.class);
                yield ItemPriceGroupAuditHistoryInfoDto.builder()
                    .previous(amendPayloadDto.getPrevious())
                    .current(amendPayloadDto.getCurrent())
                    .type(businessEvent.getType())
                    .updatedBy(businessEvent.getCreatedBy())
                    .updatedAt(businessEvent.getCreatedAt())
                    .updatedUserName(businessEvent.getCreatedUserName())
                    .build();
            }
            default -> null;
        };
    }

    private String getSkuNumberForFailedItem(UUID itemId) {
        try {
            Item failedItem = itemService.findById(itemId);
            return failedItem != null ? failedItem.getSkuNumber() : itemId.toString();
        } catch (Exception e) {
            return itemId.toString();
        }
    }
}
