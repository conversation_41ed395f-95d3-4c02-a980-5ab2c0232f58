package com.mercaso.ims.infrastructure.process.parser;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import java.util.List;
import java.util.UUID;

public interface ItemCostCollectionParser {

    List<ItemCostCollectionItemParsingResultDto> parse(UUID itemCostCollectionId);

    boolean isSupported(String vendorName, ItemCostCollectionSources sources);

    boolean isUpdateAvailability();


}
