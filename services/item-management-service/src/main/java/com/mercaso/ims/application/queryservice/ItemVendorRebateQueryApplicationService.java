package com.mercaso.ims.application.queryservice;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;

import java.util.List;
import java.util.UUID;

public interface ItemVendorRebateQueryApplicationService {

    List<ItemVendorRebateDto> findByVendorItemId (UUID vendorItemId);

    List<ItemVendorRebateDto> findByVendorIdAndItemId (UUID vendorId, UUID itemId);

    List<ItemVendorRebateDto> findByVendorItemIds (List<UUID> vendorItemIds);

}
