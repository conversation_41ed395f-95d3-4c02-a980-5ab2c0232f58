package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.domain.attribute.enums.AttributeFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateAttributeCommand extends BaseCommand {

    private String name;

    private UUID categoryId;

    private String description;

    private AttributeFormat attributeFormat;

}
