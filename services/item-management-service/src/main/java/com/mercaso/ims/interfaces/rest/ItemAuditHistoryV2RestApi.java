package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.dto.ItemAuditHistoryInfoDto;
import com.mercaso.ims.application.queryservice.ItemAuditHistoryQueryApplicationService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v2/item-audit-history", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class ItemAuditHistoryV2RestApi {

    private final ItemAuditHistoryQueryApplicationService itemAuditHistoryQueryApplicationService;

    @GetMapping("/{itemId}")
    @PreAuthorize("hasAuthority('ims:read:items')")
    public List<ItemAuditHistoryInfoDto> itemAuditHistories(@PathVariable("itemId") UUID itemId) {
        log.info("[itemAuditHistoryListV2] param itemId: {}.", itemId);
        return itemAuditHistoryQueryApplicationService.itemAuditHistories(itemId);
    }
}
