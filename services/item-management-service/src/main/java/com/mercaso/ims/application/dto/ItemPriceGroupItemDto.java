package com.mercaso.ims.application.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemPriceGroupItemDto extends BaseDto {

    private UUID id;

    private String itemTitle;

    private String skuNumber;

    private BigDecimal price;

    private UUID primaryVendorItemId;

    // this is po cost
    private BigDecimal cost;

    private BigDecimal backupCost;

    private String availabilityStatus;

    private List<ItemVendorRebateDto> itemVendorRebateDtos;

}