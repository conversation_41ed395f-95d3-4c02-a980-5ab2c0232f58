package com.mercaso.ims.domain.itemadjustmentrequestdetail;

import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import java.util.Objects;

public class ItemAdjustmentRequestDetailFactory {

    private ItemAdjustmentRequestDetailFactory() {
    }

    public static ItemAdjustmentRequestDetail create(CreateItemAdjustmentRequestDetailCommand command) {
        return ItemAdjustmentRequestDetail.builder()
            .requestId(command.getRequestId())
            .type(command.getType())
            .status(Objects.isNull(command.getStatus()) ? ItemAdjustmentStatus.PENDING : command.getStatus())
            .sku(command.getSku())
            .itemStatus(command.getItemStatus())
            .primaryVendorItemAisle(command.getAisle())
            .primaryPoVendor(command.getPrimaryPoVendor())
            .primaryJitVendor(command.getPrimaryJitVendor())
            .title(command.getTitle())
            .packageSize(command.getPackageSize())
            .itemSize(command.getItemSize())
            .itemUnitMeasure(command.getItemUnitMeasure() == null ? null : command.getItemUnitMeasure().toLowerCase())
            .department(command.getDepartment())
            .category(command.getCategory())
            .subCategory(command.getSubCategory())
            .classType(command.getClassType())
            .brand(command.getBrand())
            .vendorItemNumber(command.getVendorItemNumber())
            .disposition(command.getDisposition())
            .caseUpc(command.getCaseUpc())
            .eachUpc(command.getEachUpc())
            .missingEachUPCReason(command.getMissingEachUPCReason())
            .missingCaseUPCReason(command.getMissingCaseUPCReason())
            .inventory(command.getInventory())
            .primaryPoVendorItemCost(command.getPrimaryPoVendorItemCost())
            .primaryJitVendorItemCost(command.getPrimaryJitVendorItemCost())
            .poVendorItemCost(command.getPoVendorItemCost())
            .jitVendorItemCost(command.getJitVendorItemCost())
            .regPricePackNoCrv(command.getRegPricePackNoCrv())
            .failureReason(command.getFailureReason())
            .companyId(command.getCompanyId())
            .locationId(command.getLocationId())
            .newDescription(command.getNewDescription())
            .vendor(command.getVendor())
            .vendorAisle(command.getVendorAisle())
            .attributeName(command.getAttributeName())
            .attributeValue(command.getAttributeValue())
            .promoFlag(command.getPromoFlag())
            .promoPricePackNoCrv(command.getPromoPricePackNoCrv())
            .crvFlag(command.getCrvFlag())
            .imageUrl(command.getImageUrl())
            .tags(command.getTags())
            .length(command.getLength())
            .height(command.getHeight())
            .width(command.getWidth())
            .caseWeight(command.getCaseWeight())
            .caseWeightUnit(command.getCaseWeightUnit())
            .eachWeight(command.getEachWeight())
            .eachWeightUnit(command.getEachWeightUnit())
            .vendorItemAvailability(command.getVendorItemAvailability())
            .vendorItemType(command.getVendorItemType())
            .cooler(command.getCooler())
            .highValue(command.getHighValue())
            .archivedReason(command.getArchivedReason())
            .rebateStartDate(command.getRebateStartDate())
            .rebateEndDate(command.getRebateEndDate())
            .rebatePerSellingUnit(command.getRebatePerSellingUnit())
            .build();
    }
}
