package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.TestOrderCreatedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class TestOrderCreatedApplicationEvent extends BaseApplicationEvent<TestOrderCreatedPayloadDto> {

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source  the object on which the event initially occurred or with which the event is associated (never {@code null})
     * @param payload event payload dto
     */
    public TestOrderCreatedApplicationEvent(Object source, TestOrderCreatedPayloadDto payload) {
        super(source, payload);
    }
}
