package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.AWS_ANALYZE_EXPENSE_ERROR;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.CreateVendorPoAnalyzeRecordCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.dto.VendorPoAnalyzeRecordDto;
import com.mercaso.ims.application.dto.payload.CreateVendorPoAnalyzeExpenseRecordPayloadDto;
import com.mercaso.ims.application.queryservice.VendorQueryApplicationService;
import com.mercaso.ims.application.service.AwsAnalyzeApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.VendorPoAnalyzeRecordApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisSource;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.aws_ocr.AnalyzeLineItemDtoFactory;
import com.mercaso.ims.infrastructure.external.aws_ocr.AwsAnalyzeExpenseAdaptor;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.AnalyzeExpenseResponseDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.SummaryFieldDto;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import com.mercaso.ims.infrastructure.util.ShortRandomIdUtil;
import java.util.concurrent.ThreadLocalRandom;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.services.textract.model.GetExpenseAnalysisResponse;

@Service
@Slf4j
@RequiredArgsConstructor
public class AwsAnalyzeApplicationServiceImpl implements AwsAnalyzeApplicationService {

    private final DocumentApplicationService documentApplicationService;
    private final AwsAnalyzeExpenseAdaptor awsAnalyzeExpenseAdaptor;
    private final VendorPoAnalyzeRecordApplicationService vendorPoAnalyzeRecordApplicationService;
    private final BusinessEventService businessEventService;
    private final VendorQueryApplicationService vendorQueryApplicationService;
    private static final String DOWNEY_RCPT_ORDER = "Downey-Rcpt-Order";
    private static final String FILE_EXTENSION = ".pdf";

    @Override
    @Async("taskExecutor")
    public void analyzeDowneyExpense(byte[] file, String originalFilename) {
        try {
            log.info("[analyzeExpense] request analyzeLineItem, originalFilename:{}", originalFilename);
            String uniqueDocTypeFileName = String.format(DOWNEY_RCPT_ORDER+"-%s", ShortRandomIdUtil.shortRandomId(3));
            DocumentResponse documentResponse = documentApplicationService.uploadFileContent(file, uniqueDocTypeFileName, FILE_EXTENSION, false);

            log.info("[analyzeExpense] documentResponse: {}.", documentResponse);
            long begin = System.currentTimeMillis();
            GetExpenseAnalysisResponse analyzeExpenseResponse = awsAnalyzeExpenseAdaptor.analyzeExpense(documentResponse.getName());
            log.info("[analyzeExpense] analyzeExpenseResponse, cost: {}ms.", System.currentTimeMillis() - begin);

            if (null == analyzeExpenseResponse) {
                log.error("Failed to analyze expense: analyzeExpenseResponse is null.");
                throw new ImsBusinessException(AWS_ANALYZE_EXPENSE_ERROR);
            }

            AnalyzeExpenseResponseDTO analyzeExpenseResponseDTO = AnalyzeLineItemDtoFactory.convert(analyzeExpenseResponse);

            SummaryFieldDto summaryFieldDto = AnalyzeLineItemDtoFactory.mapToSummaryFieldDtos(analyzeExpenseResponseDTO);

            VendorDto vendorDto = vendorQueryApplicationService.queryOrFilterVendors(DOWNEY_WHOLESALE).getFirst();

            VendorPoAnalyzeRecordDto vendorPoAnalyzeRecord = vendorPoAnalyzeRecordApplicationService.save(
                CreateVendorPoAnalyzeRecordCommand.builder()
                    .vendorId(vendorDto.getId())
                    .vendorName(vendorDto.getVendorName())
                    .analysisSource(AnalysisSource.AWS)
                    .rcptDate(summaryFieldDto.getRcptDate())
                    .rcptId(summaryFieldDto.getRcptId())
                    .status(AnalysisStatus.APPROVED)
                    .originalFileName(documentResponse.getName())
                    .analysisExpensePayload(SerializationUtils.serialize(analyzeExpenseResponseDTO))
                    .build());

            businessEventService.dispatch(CreateVendorPoAnalyzeExpenseRecordPayloadDto.builder()
                .vendorPoAnalyzeRecordId(vendorPoAnalyzeRecord.getId())
                .data(vendorPoAnalyzeRecord)
                .build());

        } catch (Exception e) {
            log.error("Failed to analyze expense: ", e);
            throw new ImsBusinessException(AWS_ANALYZE_EXPENSE_ERROR);
        }
    }
}
