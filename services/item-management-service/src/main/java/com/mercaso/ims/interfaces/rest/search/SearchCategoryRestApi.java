package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.application.query.CategoryQuery;
import com.mercaso.ims.application.searchservice.CategorySearchApplicationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/search/category", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
@Validated
public class SearchCategoryRestApi {
    private final CategorySearchApplicationService categorySearchApplicationService;

    @GetMapping("/department-tree")
    @PreAuthorize("hasAuthority('ims:read:categories')")
    public List<CategoryTreeDto> searchDepartmentCategoryTrees() {
        log.info("[searchCategories] Searching for category tree");
        CategoryQuery categoryQuery = CategoryQuery.builder().depth(0).build();
        return categorySearchApplicationService.searchDepartmentCategoryTrees(categoryQuery);
    }
}
