package com.mercaso.ims.domain.email.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum EmailType {
    PERSONAL("Personal"),
    WORK("Work"),
    BILLING("Billing"),
    PAYMENT("Payment"),
    HOME("Home"),
    BUSINESS("Business"),
    SUPPORT("Support"),
    SALES("Sales"),
    MARKETING("Marketing"),
    <PERSON>THER("Other"),
    UNKNOWN("Unknown"),
    ;

    private final String displayName;

    EmailType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JsonCreator
    public static EmailType fromString(String name) {
        return Arrays.stream(values())
            .filter(v -> v.name().equalsIgnoreCase(name) || v.displayName.equalsIgnoreCase(name))
            .findFirst()
            .orElse(UNKNOWN);
    }
}
