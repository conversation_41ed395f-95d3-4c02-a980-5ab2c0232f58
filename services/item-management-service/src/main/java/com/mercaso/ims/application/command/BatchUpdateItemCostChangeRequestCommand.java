package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class BatchUpdateItemCostChangeRequestCommand extends BaseCommand {

    private List<UpdateItemCostChangeRequestCommand> updateItemCostChangeRequestCommands;

}
