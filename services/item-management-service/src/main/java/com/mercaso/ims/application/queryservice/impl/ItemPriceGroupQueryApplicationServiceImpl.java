package com.mercaso.ims.application.queryservice.impl;

import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.application.mapper.itempricegroup.ItemPriceGroupDtoApplicationMapping;
import com.mercaso.ims.application.queryservice.ItemPriceGroupQueryApplicationService;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.service.ItemPriceGroupService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemPriceGroupQueryApplicationServiceImpl implements ItemPriceGroupQueryApplicationService {

    private final ItemPriceGroupService itemPriceGroupService;

    private final ItemPriceGroupDtoApplicationMapping itemPriceGroupDtoMapping;


    @Override
    public List<ItemPriceGroupDto> queryOrFilterItemPriceGroups(String itemPriceGroupName) {
        List<ItemPriceGroup> itemPriceGroupDtos = StringUtils.isBlank(itemPriceGroupName)
            ? itemPriceGroupService.findAll()
            : itemPriceGroupService.findByFuzzyName(itemPriceGroupName);

        return itemPriceGroupDtos.stream()
            .map(itemPriceGroupDtoMapping::domainToDto)
            .toList();
    }
}
