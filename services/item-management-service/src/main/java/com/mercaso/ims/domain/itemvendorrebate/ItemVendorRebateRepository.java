package com.mercaso.ims.domain.itemvendorrebate;

import com.mercaso.ims.domain.BaseDomainRepository;

import java.util.List;
import java.util.UUID;

/**
 * Repository interface for ItemVendorRebate domain entity
 */
public interface ItemVendorRebateRepository extends BaseDomainRepository<ItemVendorRebate, UUID> {

    /**
     * Find rebates by vendor ID
     * @param vendorId the vendor ID
     * @return list of rebates for the vendor
     */
    List<ItemVendorRebate> findByVendorId(UUID vendorId);

    /**
     * Find rebates by item ID
     * @param itemId the item ID
     * @return list of rebates for the item
     */
    List<ItemVendorRebate> findByItemId(UUID itemId);

    /**
     * Find rebates by vendor item ID
     * @param vendorItemId the vendor item ID
     * @return list of rebates for the vendor item
     */
    List<ItemVendorRebate> findByVendorItemId(UUID vendorItemId);

    List<ItemVendorRebate> findByVendorIdAndItemId(UUID vendorId, UUID itemId);

    List<ItemVendorRebate> findByVendorItemIds(List<UUID> vendorItemIds);
}
