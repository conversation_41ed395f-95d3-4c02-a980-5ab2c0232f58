package com.mercaso.ims.application.queryservice.impl;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.mapper.itemvendorrebate.ItemVendorRebateDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemVendorRebateQueryApplicationService;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemVendorRebateQueryApplicationServiceImpl implements ItemVendorRebateQueryApplicationService {

    private final ItemVendorRebateService itemVendorRebateService;

    private final ItemVendorRebateDtoApplicationMapper itemVendorRebateDtoApplicationMapper;

    @Override
    public List<ItemVendorRebateDto> findByVendorItemId(UUID vendorItemId) {
        List<ItemVendorRebate> itemVendorRebates = itemVendorRebateService.findByVendorItemId(vendorItemId);

        if (CollectionUtils.isEmpty(itemVendorRebates)) {
            return List.of();
        }

        return itemVendorRebates.stream().map(itemVendorRebateDtoApplicationMapper :: domainToDto).toList();
    }

    @Override
    public List<ItemVendorRebateDto> findByVendorIdAndItemId(UUID vendorId, UUID itemId) {
        List<ItemVendorRebate> itemVendorRebates = itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId);

        if (CollectionUtils.isEmpty(itemVendorRebates)) {
            return List.of();
        }

        return itemVendorRebates.stream().map(itemVendorRebateDtoApplicationMapper :: domainToDto).toList();
    }

    @Override
    public List<ItemVendorRebateDto> findByVendorItemIds(List<UUID> vendorItemIds) {
        List<ItemVendorRebate> itemVendorRebates = itemVendorRebateService.findByVendorItemIds(vendorItemIds);

        if (CollectionUtils.isEmpty(itemVendorRebates)) {
            return List.of();
        }

        return itemVendorRebates.stream().map(itemVendorRebateDtoApplicationMapper :: domainToDto).toList();
    }
}
