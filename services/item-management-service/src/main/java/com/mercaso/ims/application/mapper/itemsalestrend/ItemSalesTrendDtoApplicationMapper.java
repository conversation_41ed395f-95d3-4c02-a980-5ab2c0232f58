package com.mercaso.ims.application.mapper.itemsalestrend;

import com.mercaso.ims.application.dto.ItemSalesTrendDto;
import com.mercaso.ims.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ItemSalesTrendDtoApplicationMapper extends BaseDtoApplicationMapper<ItemSalesTrend, ItemSalesTrendDto> {

    @Override
    ItemSalesTrendDto domainToDto(ItemSalesTrend domain);


    ItemSalesTrend dtoToDomain(ItemSalesTrendDto dto);

}
