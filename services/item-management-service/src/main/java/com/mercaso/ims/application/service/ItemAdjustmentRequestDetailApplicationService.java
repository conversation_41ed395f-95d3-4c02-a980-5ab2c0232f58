package com.mercaso.ims.application.service;

import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import java.util.UUID;

public interface ItemAdjustmentRequestDetailApplicationService {

    ItemAdjustmentRequestDetailDto create(CreateItemAdjustmentRequestDetailCommand command);

    void updateIms(UUID id);

    ItemAdjustmentRequestDetailDto completeItemAdjustmentRequest(UUID id);

    void updateImsAsItemAdjustmentRequest(ItemAdjustmentRequestDetailDto requestDetailDto);

    void syncToShopify(UUID id);

    void syncToShopifyFailure(UUID id, String failureReason);
}
