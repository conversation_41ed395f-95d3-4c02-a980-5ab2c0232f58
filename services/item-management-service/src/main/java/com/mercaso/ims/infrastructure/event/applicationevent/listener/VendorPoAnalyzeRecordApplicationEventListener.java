package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.dto.VendorPoAnalyzeRecordDto;
import com.mercaso.ims.application.dto.event.CreateAnalyzeExpenseRecordApplicationEvent;
import com.mercaso.ims.application.dto.payload.CreateVendorPoAnalyzeExpenseRecordPayloadDto;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.application.service.VendorPoAnalyzeRecordApplicationService;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import static com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes.PDF_FILE;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class VendorPoAnalyzeRecordApplicationEventListener {

    private final VendorPoAnalyzeRecordApplicationService vendorPoAnalyzeRecordApplicationService;

    private final ItemCostCollectionApplicationService itemCostCollectionApplicationService;

    @EventListener
    public void handleApprovedAnalyzeRecordApplicationEvent(
        CreateAnalyzeExpenseRecordApplicationEvent approvedAnalyzeRecordApplicationEvent) {
        try {
            CreateVendorPoAnalyzeExpenseRecordPayloadDto payload = approvedAnalyzeRecordApplicationEvent.getPayload();

            log.info("handleApprovedAnalyzeRecordApplicationEvent for request ={}", payload);

            VendorPoAnalyzeRecordDto vendorPoAnalyzeRecordDto = vendorPoAnalyzeRecordApplicationService.findById(payload.getVendorPoAnalyzeRecordId());
            if (null == vendorPoAnalyzeRecordDto) {
                log.error("VendorPoAnalyzeRecordDto not found for id = {}", payload.getVendorPoAnalyzeRecordId());
                return;
            }

            VendorPoAnalyzeRecordDto vendorPoAnalyzeRecordPayload = payload.getData();
            ItemCostCollectionDto itemCostCollectionDto = createItemCostCollection(vendorPoAnalyzeRecordPayload);

            vendorPoAnalyzeRecordApplicationService.updateWithItemCostCollection(
                vendorPoAnalyzeRecordDto.getId(),
                itemCostCollectionDto
            );
        } catch (Exception e) {
            log.error("Error in handleApprovedAnalyzeRecordApplicationEvent", e);
        }
    }

    private ItemCostCollectionDto createItemCostCollection(VendorPoAnalyzeRecordDto analyzeExpenseDto) {
        CreateItemCostCollectionCommand createItemCostCollectionCommand = CreateItemCostCollectionCommand.builder()
            .vendorId(analyzeExpenseDto.getVendorId())
            .vendorName(analyzeExpenseDto.getVendorName())
            .source(ItemCostCollectionSources.AWS_ANALYZE)
            .type(PDF_FILE)
            .vendorCollectionNumber(StringUtils.isBlank(analyzeExpenseDto.getRcptId()) ? StringUtils.EMPTY : analyzeExpenseDto.getRcptId())
            .fileName(analyzeExpenseDto.getOriginalFileName())
            .build();

        return itemCostCollectionApplicationService.create(createItemCostCollectionCommand);
    }
}
