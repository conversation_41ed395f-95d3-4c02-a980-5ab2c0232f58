package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.application.dto.ItemSalesTrendDto;
import com.mercaso.ims.application.queryservice.ItemSalesTrendQueryApplicationService;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping(value = "/v1/query/item-sales-trend", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class QueryItemSalesTrendRestApi {

    private final ItemSalesTrendQueryApplicationService itemSalesTrendQueryApplicationService;

    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:items')")
    public List<ItemSalesTrendDto> searchItemSalesTrend(@RequestParam(value = "itemId") UUID itemId,
        @RequestParam(value = "timeGrain") ItemSalesTrendTimeGrain timeGrain) {
        log.info("[searchItemSalesTrend] param itemId: {}, timeGrain: {}.", itemId, timeGrain);
        return itemSalesTrendQueryApplicationService.findByItemIdAndTimeGrain(itemId, timeGrain);
    }

}
