package com.mercaso.ims.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DifyCategoryMatchingResultDto {
  private String matchedCategoryFullPath;

  private String matchedDepartment;
  private String matchedDepartmentId;

  private String matchedCategory;
  private String matchedCategoryId;

  private String matchedSubCategory;
  private String matchedSubCategoryId;

  private String matchedClazz;
  private String matchedClazzId;

  private String matchedCredibility;

  private String recommendedCategory;
  private String recommendedCredibility;
}
