package com.mercaso.ims.infrastructure.excel.listener;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.FinalePurchaseOrderDetailData;
import com.mercaso.ims.infrastructure.excel.data.OnOrderDetailData;
import com.mercaso.ims.infrastructure.excel.data.PurchaseOrderData;
import com.mercaso.ims.infrastructure.excel.data.Q4DeliveryDetailData;
import com.mercaso.ims.infrastructure.excel.data.Q4PurchaseOrderDetailData;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@Getter
@SuperBuilder
@Slf4j
public class PurchaseOrderDataListener implements ReadListener<PurchaseOrderData> {

    private VendorItemRepository vendorItemRepository;
    private VendorRepository vendorRepository;
    private ItemRepository itemRepository;
    private ByteArrayOutputStream outputStream;
    private String supplier = null;
    private List<FinalePurchaseOrderDetailData> finalePurchaseOrderDetailDataList = new ArrayList<>();
    private Q4DeliveryDetailData q4DeliveryDetailData = null;
    private List<Q4PurchaseOrderDetailData> q4PurchaseOrderDetailDataList = new ArrayList<>();
    private List<OnOrderDetailData> onOrderDetailDataList = new ArrayList<>();
    private Integer totalQuantity = 0;
    private BigDecimal totalPrice = BigDecimal.ZERO;
    private String poNumber = null;
    private String orderDate = null;
    private String pickupDate = null;
    private String destination = null;


    public PurchaseOrderDataListener(VendorItemRepository vendorItemRepository, VendorRepository vendorRepository,
        ItemRepository itemRepository) {
        this.itemRepository = itemRepository;
        this.vendorRepository = vendorRepository;
        this.vendorItemRepository = vendorItemRepository;
        this.outputStream = new ByteArrayOutputStream();
    }


    public void invoke(PurchaseOrderData t, AnalysisContext analysisContext) {
        log.info("The result of parsing a line of data :{}", SerializationUtils.serialize(t));
        try {
            setPoInfo(t);
            Item item = getItemBySupplierProductId(t.getSupplierProductId());
            if (item == null) {
                log.warn("Item not found for supplierProductId: {}", t.getSupplierProductId());
                return;
            }
            totalQuantity = totalQuantity + t.getQuantity();
            totalPrice = totalPrice.add(t.getUnitPrice().multiply(BigDecimal.valueOf(t.getQuantity())));
            finalePurchaseOrderDetailDataList.add(buildFinalePurchaseOrderDetailData(t, item.getSkuNumber()));
            q4PurchaseOrderDetailDataList.add(buildQ4PurchaseOrderDetailData(t, item));
            onOrderDetailDataList.add(buildOnOrderDetailData(t, item));
        } catch (Exception e) {
            log.warn("parsing data error :{} ", e.getMessage(), e);
            throw new ImsBusinessException("parsing data error : " + e.getMessage(), e);
        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (q4DeliveryDetailData != null) {
            q4DeliveryDetailData.setTotalQuantity(totalQuantity);
            q4DeliveryDetailData.setSubTotal(totalPrice);
            q4DeliveryDetailData.setGrandTotalPrice(totalPrice);
        }

        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build()) {
            WriteSheet finalePurchaseOrderSheet = EasyExcelFactory.writerSheet(0, "Finale")
                .head(FinalePurchaseOrderDetailData.class)
                .build();
            excelWriter.write(finalePurchaseOrderDetailDataList, finalePurchaseOrderSheet);
            WriteSheet q4DeliverySheet = EasyExcelFactory.writerSheet(1, "Q4 Delivery")
                .head(Q4DeliveryDetailData.class)
                .build();
            List<Q4DeliveryDetailData> q4DeliveryDetailDataList = new ArrayList<>();
            q4DeliveryDetailDataList.add(q4DeliveryDetailData);
            excelWriter.write(q4DeliveryDetailDataList, q4DeliverySheet);
            WriteSheet q4PoDetailSheet = EasyExcelFactory.writerSheet(2, "Q4 PO Detail")
                .head(Q4PurchaseOrderDetailData.class)
                .build();
            excelWriter.write(q4PurchaseOrderDetailDataList, q4PoDetailSheet);

            WriteSheet onOrderSheet = EasyExcelFactory.writerSheet(3, "On Order")
                .head(OnOrderDetailData.class)
                .build();
            excelWriter.write(onOrderDetailDataList, onOrderSheet);
            log.info("doAfterAllAnalysed");
        } catch (Exception e) {
            log.error("Error while generating excel for PurchaseOrder ");
        }
    }

    private Item getItemBySupplierProductId(String supplierProductId) {
        if (StringUtils.isBlank(supplierProductId)) {
            log.warn("Vendor item number is blank");
            return null;
        }

        Vendor costco = vendorRepository.findByVendorName(supplier);
        if (costco == null) {
            log.warn("Vendor not found for name: {}", supplier);
            return null;
        }

        List<VendorItem> vendorItems = vendorItemRepository.findByVendorIDAndVendorSkuNum(costco.getId(), supplierProductId);
        if (CollectionUtils.isEmpty(vendorItems) || vendorItems.getFirst().getItemId() == null) {
            log.warn("VendorItem not found or itemId is null for vendorId: {} and vendorSkuNum: {}",
                costco.getId(), supplierProductId);
            return null;
        }

        Item item = itemRepository.findById(vendorItems.getFirst().getItemId());
        if (item == null) {
            log.warn("Item not found for itemId: {}", vendorItems.getFirst().getItemId());
            return null;
        }

        return item;
    }

    private FinalePurchaseOrderDetailData buildFinalePurchaseOrderDetailData(PurchaseOrderData data, String sku) {
        FinalePurchaseOrderDetailData finalePurchaseOrderDetailData = new FinalePurchaseOrderDetailData();
        finalePurchaseOrderDetailData.setPoNumber(poNumber);
        finalePurchaseOrderDetailData.setOrderDate(orderDate);
        finalePurchaseOrderDetailData.setSupplier(supplier);
        finalePurchaseOrderDetailData.setDestination(destination);
        finalePurchaseOrderDetailData.setEstimatedReceiveDate(pickupDate);
        finalePurchaseOrderDetailData.setSku(sku);
        finalePurchaseOrderDetailData.setQuantity(data.getQuantity());
        finalePurchaseOrderDetailData.setUnitPrice(data.getUnitPrice());
        return finalePurchaseOrderDetailData;
    }

    private void buildQ4DeliveryDetailData() {
        q4DeliveryDetailData = new Q4DeliveryDetailData();
        q4DeliveryDetailData.setSupplier(supplier);
        q4DeliveryDetailData.setPoNumber(poNumber);
        q4DeliveryDetailData.setOrderDate(orderDate);
        q4DeliveryDetailData.setPickUpDate(pickupDate);

    }

    private Q4PurchaseOrderDetailData buildQ4PurchaseOrderDetailData(PurchaseOrderData data, Item item) {
        Q4PurchaseOrderDetailData q4PurchaseOrderDetailData = new Q4PurchaseOrderDetailData();
        q4PurchaseOrderDetailData.setSku(item.getSkuNumber());
        q4PurchaseOrderDetailData.setQuantity(data.getQuantity());
        q4PurchaseOrderDetailData.setUnitPrice(data.getUnitPrice());
        q4PurchaseOrderDetailData.setSupplierSku(data.getSupplierProductId());
        q4PurchaseOrderDetailData.setDescription(item.getTitle());
        q4PurchaseOrderDetailData.setExtendedCost(data.getExtendedCost());
        return q4PurchaseOrderDetailData;
    }

    private OnOrderDetailData buildOnOrderDetailData(PurchaseOrderData data, Item item) {
        OnOrderDetailData onOrderDetailData = new OnOrderDetailData();
        onOrderDetailData.setPoNumber(poNumber);
        onOrderDetailData.setSku(item.getSkuNumber());
        onOrderDetailData.setQuantity(data.getQuantity());
        onOrderDetailData.setDescription(item.getTitle());
        onOrderDetailData.setPickupDate(pickupDate);
        return onOrderDetailData;
    }

    private void setPoInfo(PurchaseOrderData data) {
        if (StringUtils.isBlank(supplier)) {
            supplier = data.getSupplier();
        }
        if (StringUtils.isBlank(orderDate)) {
            orderDate = data.getOrderDate();
        }
        if (StringUtils.isBlank(pickupDate)) {
            pickupDate = data.getPickupDate();
        }
        if (StringUtils.isBlank(destination)) {
            destination = data.getDestination();
        }
        if (StringUtils.isBlank(poNumber)) {
            poNumber = data.getPoNumber();
        }
        if (q4DeliveryDetailData == null) {
            buildQ4DeliveryDetailData();
        }
    }
}
