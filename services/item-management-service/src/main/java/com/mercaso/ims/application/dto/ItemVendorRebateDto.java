package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * Data Transfer Object for ItemVendorRebate
 *
 * Business Fields:
 * - Start Date: Required - when rebate becomes effective
 * - End Date: Optional - if null, rebate is continuous and valid indefinitely
 * - Supplier: Required - must be a direct supplier
 * - SKU: Required - item identification
 * - Rebate Amount per Unit Sold: Required - amount supplier refunds per sales unit
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemVendorRebateDto extends BaseDto {

    private UUID id;

    private UUID vendorItemId;

    /** Direct Supplier ID */
    private UUID vendorId;

    private String vendorName;

    /** Item ID associated with the SKU */
    private UUID itemId;

    private String skuNumber;

    private String itemDescription;

    /** Start Date - when rebate becomes effective (Required) */
    private Instant startDate;

    /** End Date - when rebate terminates (Optional - null means continuous) */
    private Instant endDate;

    /** Rebate Amount per Unit Sold ($) - amount refunded per sales unit */
    private BigDecimal rebatePerUnit;

    private ItemVendorRebateStatus itemVendorRebateStatus;
}
