package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.ItemAdjustmentFailureProcessedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ItemAdjustmentRequestFailureProcessedApplicationEvent extends
    BaseApplicationEvent<ItemAdjustmentFailureProcessedPayloadDto> {

    public ItemAdjustmentRequestFailureProcessedApplicationEvent(Object source,
        ItemAdjustmentFailureProcessedPayloadDto payload) {
        super(source, payload);
    }
}

