package com.mercaso.ims.domain.itempricegroup;

import com.mercaso.ims.domain.BaseDomain;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class ItemPriceGroup extends BaseDomain {
    private UUID id;
    private String groupName;
    private BigDecimal price;

}