package com.mercaso.ims.domain.itemadjustmentrequest.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum ItemAdjustmentRequestType {
    DOWNEY_ADJUSTMENT,
    JC_ADJUSTMENT,
    NEW_TEMPLATE_ADJUSTMENT,
    COSTCO_VENDOR_COST_UPDATE,
    VERNON_VENDOR_COST_UPDATE,
    ITEM_VENDOR_REBATE,
    UNKNOWN,
    ;

    @JsonCreator
    public static ItemAdjustmentRequestType fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equals(name)).findFirst().orElse(UNKNOWN);
    }
}
