package com.mercaso.ims.interfaces.rest.search;


import com.mercaso.ims.application.dto.ItemCostChangeRequestListDto;
import com.mercaso.ims.application.dto.ItemCostCollectionListDto;
import com.mercaso.ims.application.query.ItemCostChangeRequestQuery;
import com.mercaso.ims.application.query.ItemCostCollectionQuery;
import com.mercaso.ims.application.query.ItemCostCollectionQuery.SortType;
import com.mercaso.ims.application.searchservice.ItemCostCollectionSearchApplicationService;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/search/item-cost-collection", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
@Validated
public class SearchItemCostCollectionRestApi {

    private final ItemCostCollectionSearchApplicationService itemCostCollectionSearchApplicationService;

    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:vendor-items')")
    public ItemCostCollectionListDto searchItemCostCollectionList(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        @Max(value = 100, message = "Page size must not exceed 100")
        int pageSize,
        @RequestParam(value = "sort", required = false) SortType sort,
        @RequestParam(value = "createdStartDate", required = false) Instant createdStartDate,
        @RequestParam(value = "createdEndDate", required = false) Instant createdEndDate,
        @RequestParam(value = "createdBy", required = false) String createdBy,
        @RequestParam(value = "hasPending", required = false) Boolean hasPending,
        @RequestParam(value = "vendorId", required = false) UUID vendorId,
        @RequestParam(value = "vendorType", required = false) String vendorType) {
        return itemCostCollectionSearchApplicationService.searchItemCostCollectionList(ItemCostCollectionQuery.builder()
            .page(page)
            .pageSize(pageSize)
            .createdAtBegin(createdStartDate)
            .createdAtEnd(createdEndDate)
            .createdBy(createdBy)
            .hasPending(hasPending)
            .sort(sort)
            .vendorId(vendorId)
            .vendorType(vendorType)
            .build());
    }

    @GetMapping("/item-cost-request")
    @PreAuthorize("hasAuthority('ims:read:vendor-items')")
    public ItemCostChangeRequestListDto searchItemCostRequestList(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        @Max(value = 100, message = "Page size must not exceed 100")
        int pageSize,
        @RequestParam(value = "sort", required = false) ItemCostChangeRequestQuery.SortType sort,
        @RequestParam(value = "itemCostCollectionId") UUID itemCostCollectionId,
        @RequestParam(value = "isPrimaryVendor", required = false) Boolean isPrimaryVendor,
        @RequestParam(value = "isBackupVendor", required = false) Boolean isBackupVendor,
        @RequestParam(value = "status", required = false) String status,
        @RequestParam(value = "itemStatus", required = false) List<String> itemStatus,
        @RequestParam(value = "itemSkuNumbers", required = false) List<String> itemSkuNumbers,
        @RequestParam(value = "hasPrimaryVendor", required = false) Boolean hasPrimaryVendor) {
        return itemCostCollectionSearchApplicationService.searchItemCostChangeRequestList(ItemCostChangeRequestQuery.builder()
            .page(page)
            .pageSize(pageSize)
            .itemCostCollectionId(itemCostCollectionId)
            .isPrimaryVendor(isPrimaryVendor)
            .isBackupVendor(isBackupVendor)
            .status(status)
            .itemStatus(itemStatus)
            .itemSkuNumbers(itemSkuNumbers)
            .hasPrimaryVendor(hasPrimaryVendor)
            .sortType(sort)
            .build());
    }
}
