package com.mercaso.ims.application.searchservice.impl;

import com.mercaso.ims.application.dto.*;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.query.ItemQuery.ItemQueryFilterKey;
import com.mercaso.ims.application.queryservice.ItemVendorRebateQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.item.jpa.CustomizedItemJpaDao;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class ItemSearchApplicationServiceImpl implements ItemSearchApplicationService {

    private final CustomizedItemJpaDao customizedItemJpaDao;

    private final DocumentApplicationService documentApplicationService;

    private final ItemVendorRebateQueryApplicationService itemVendorRebateQueryApplicationService;


    private final AtomicInteger idGen = new AtomicInteger(1);

    private final int nThreads = Runtime.getRuntime().availableProcessors() * 2 + 1;

    private final ExecutorService payoutSearchThreadPool =
        new ThreadPoolExecutor(nThreads, nThreads,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(200),
            r -> new Thread(r, "payout-search-thread-pool-" + idGen.getAndIncrement()),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public ItemListDto searchItemListV2(ItemQuery itemQuery) {
        log.debug("[searchItemListV2] param itemQuery: {}.", itemQuery);
        Future<List<ItemSerachDto>> itemDtoFuture = payoutSearchThreadPool.submit(() -> {
            List<ItemSerachDto> itemDtos = customizedItemJpaDao.getItemDtoList(itemQuery);
            itemDtos.forEach(itemSerachDto -> {
                if (StringUtils.isNotBlank(itemSerachDto.getPhotoName())) {
                    itemSerachDto.setPhotoUrl(documentApplicationService.getImsUrl(itemSerachDto.getPhotoName()));
                }
            });

            List<UUID> vendorItemIds = itemDtos.stream()
                    .map(ItemSerachDto::getPrimaryVendorItemId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            itemVendorRebateQueryApplicationService.findByVendorItemIds(vendorItemIds);


            return itemDtos;
        });

        Future<Long> itemDtoCountFuture = payoutSearchThreadPool.submit(() -> customizedItemJpaDao.countQueryV2(itemQuery));

        try {
            return ItemListDto.builder().data(itemDtoFuture.get())
                .totalCount(itemDtoCountFuture.get())
                .build();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread was interrupted: ", e);
            throw new ImsBusinessException("Concurrency error for querying item list.");
        } catch (ExecutionException e) {
            log.error("Concurrency error for querying item list: ", e);
            throw new ImsBusinessException("Concurrency error for querying item list.");
        }
    }

    @Override
    public List<ItemListCustomFilterKeyDto> searchItemsListCustomFilter() {
        ItemQueryFilterKey[] values = ItemQueryFilterKey.values();
        return Arrays.stream(values)
            .map(itemQueryFilterKey -> ItemListCustomFilterKeyDto.builder()
                .customFilterKeyName(itemQueryFilterKey.getParamKey())
                .customFilterKeyType(itemQueryFilterKey.getType().name())
                .build())
            .toList();
    }

    @Override
    public List<CategoryItemCountsDto> countItemsByCategoryIdAndStatus(List<UUID> categoryIds) {
        return customizedItemJpaDao.countItemsByCategoryIdAndStatus(categoryIds);
    }

    @Override
    public List<UUID> searchItemListIds(ItemQuery itemQuery) {
        List<UUID> itemIdList = customizedItemJpaDao.getItemIdListV2(itemQuery);
        if (CollectionUtils.isEmpty(itemIdList)) {
            return List.of();
        }
        return itemIdList;
    }

    @Override
    public Long searchItemCount(ItemQuery itemQuery) {
        return customizedItemJpaDao.countQueryV2(itemQuery);
    }
}
