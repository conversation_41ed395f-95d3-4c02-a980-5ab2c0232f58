package com.mercaso.ims.domain.address;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.ValueObject;
import com.mercaso.ims.domain.address.enums.AddressPurpose;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.EqualsBuilder;

import java.util.UUID;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class Address extends BaseDomain implements ValueObject<Address> {

    private UUID id;

    private String entityType;

    private UUID entityId;

    private String streetAddress;

    private String city;

    private String state;

    private String postalCode;

    private String country;

    private String directions;

    private AddressPurpose purpose;

    private String additionalLines;

    @Override
    public boolean sameValueAs(Address other) {
        return other != null && new EqualsBuilder()
            .append(entityType, other.entityType)
            .append(entityId, other.entityId)
            .append(streetAddress, other.streetAddress)
            .append(city, other.city)
            .append(state, other.state)
            .append(postalCode, other.postalCode)
            .append(country, other.country)
            .append(directions, other.directions)
            .append(purpose, other.purpose)
            .append(additionalLines, other.additionalLines)
            .isEquals();
    }
}
