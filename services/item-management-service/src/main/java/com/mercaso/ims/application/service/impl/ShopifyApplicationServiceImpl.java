package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.domain.item.enums.AvailabilityStatus.ACTIVE;

import com.google.common.annotations.VisibleForTesting;
import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.event.ItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.application.service.ShopifyApplicationService;
import com.mercaso.ims.domain.itemsyncinfo.service.ItemAdjustmentSyncStatusService;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyRequestDtoFactory;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class ShopifyApplicationServiceImpl implements ShopifyApplicationService {

    private final ItemQueryApplicationService itemQueryApplicationService;
    private final ShopifyAdaptor shopifyAdaptor;
    private final ItemAdjustmentSyncStatusService itemAdjustmentSyncStatusService;
    private final Executor syncShopifyTaskExecutor;
    private final ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;

    @Override
    public void syncItemCreatedEvent(ItemCreatedApplicationEvent itemCreatedApplicationEvent) {
        ItemCreatedPayloadDto itemCreatedPayloadDto = itemCreatedApplicationEvent.getPayload();
        ItemDto itemDto = itemQueryApplicationService.findById(itemCreatedPayloadDto.getItemId());
        CompletableFuture.runAsync(() -> syncItemCreatedEventToShopify(itemCreatedPayloadDto,
            itemDto,
            itemCreatedApplicationEvent.getBusinessEventId()), syncShopifyTaskExecutor);
        log.info("syncItemCreatedEvent for request ={}", itemCreatedPayloadDto);

    }

    @Override
    public void syncItemAmendEvent(ItemAmendApplicationEvent itemAmendApplicationEvent) {
        ItemAmendPayloadDto itemAmendPayloadDto = itemAmendApplicationEvent.getPayload();
        UUID itemId = itemAmendPayloadDto.getItemId();
        CompletableFuture.runAsync(() -> syncItemAmendEventToShopify(itemAmendPayloadDto,
            itemId,
            itemAmendApplicationEvent.getBusinessEventId()), syncShopifyTaskExecutor);
        log.debug("syncItemAmendEvent for request ={}", itemAmendPayloadDto);
    }

    @Override
    public void syncItemDeleteEvent(ItemDeletedApplicationEvent itemDeletedApplicationEvent) {
        ItemDeletedPayloadDto itemDeletedPayloadDto = itemDeletedApplicationEvent.getPayload();
        ItemDto itemDto = itemDeletedPayloadDto.getData();
        CompletableFuture.runAsync(() -> syncItemDeletedEventToShopify(itemDeletedPayloadDto,
            itemDto,
            itemDeletedApplicationEvent.getBusinessEventId()), syncShopifyTaskExecutor);

    }


    private boolean isItemPhotoUrlChanged(AmendDto<ItemDto> itemAmendPayloadDto) {
        return !StringUtils.equals(itemAmendPayloadDto.getPrevious().getPhotoName(),
            itemAmendPayloadDto.getCurrent().getPhotoName());
    }

    @VisibleForTesting
    protected void syncItemCreatedEventToShopify(ItemCreatedPayloadDto itemCreatedPayloadDto,
        ItemDto itemDto,
        UUID businessEventId) {
        try {
            ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = shopifyAdaptor.queryProduct(itemDto.getSkuNumber());
            ShopifyProductDto shopifyProduct = null;
            if (null == shopifyGraphQLQueryResponseDto || CollectionUtils.isEmpty(shopifyGraphQLQueryResponseDto.getData()
                .getProducts().getEdges())) {
                log.warn("syncItemCreatedEvent for sku ={}, already exist in shopify", itemDto.getSkuNumber());
                shopifyProduct = shopifyAdaptor.createShopifyProduct(ShopifyRequestDtoFactory.buildShopifyProductDto(
                    itemDto, null));
            }

            ItemDto data = itemCreatedPayloadDto.getData();

            if (CollectionUtils.isNotEmpty(data.getItemImages()) || Boolean.TRUE.equals(data.getPromoFlag())) {
                log.info("begin createdShopifyProductImage for sku ={}, photoName ={}", itemDto.getSkuNumber(),
                    itemDto.getPhotoName());
                shopifyAdaptor.modifyShopifyProductImage(ShopifyRequestDtoFactory.buildVariantImageDto(itemDto,
                    null, shopifyProduct));
            }

            shopifyAdaptor.createOrModifyShopifyProductMetaFields(ShopifyRequestDtoFactory.buildMetaFieldDto(itemDto),
                shopifyProduct);

            shopifyAdaptor.deletedShopifyProductMetaFields(ShopifyRequestDtoFactory.buildDeleteMetaFieldDto(itemDto),
                shopifyProduct);

            shopifyAdaptor.modifyShopifyInventoryItem(ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto,
                shopifyProduct));

            if (ACTIVE.name().equals(itemDto.getAvailabilityStatus())) {
                shopifyAdaptor.setShopifyChannels(shopifyProduct);
            }

            itemAdjustmentSyncStatusService.saveItemAdjustmentSyncSuccessStatus(businessEventId);
            if (itemCreatedPayloadDto.getItemAdjustmentRequestDetailId() != null) {
                itemAdjustmentRequestDetailApplicationService.syncToShopify(itemCreatedPayloadDto.getItemAdjustmentRequestDetailId());
            }

        } catch (Exception e) {
            log.error("syncItemCreatedEvent error for request ={}", itemCreatedPayloadDto, e);
            itemAdjustmentSyncStatusService.saveItemAdjustmentSyncFiledStatus(businessEventId);
            if (itemCreatedPayloadDto.getItemAdjustmentRequestDetailId() != null) {
                itemAdjustmentRequestDetailApplicationService.syncToShopifyFailure(itemCreatedPayloadDto.getItemAdjustmentRequestDetailId(),
                    e.getMessage());
            }
        }
    }

    @VisibleForTesting
    protected void syncItemAmendEventToShopify(ItemAmendPayloadDto itemAmendPayloadDto,
        UUID itemId,
        UUID businessEventId) {
        try {
            ItemDto itemDto = itemQueryApplicationService.findById(itemId);
            log.info("begin syncItemAmendEvent for sku ={}, time ={}", itemDto.getSkuNumber(), System.currentTimeMillis());
            ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = shopifyAdaptor.queryProduct(itemDto.getSkuNumber());
            if (isItemPhotoUrlChanged(itemAmendPayloadDto.getData()) || itemAmendPayloadDto.isPromoFlagChanged()
                || itemAmendPayloadDto.isPriceChanged()) {
                log.info("begin modifyShopifyProductImage for sku ={}, photoName ={}",
                    itemDto.getSkuNumber(),
                    itemDto.getPhotoName());
                shopifyAdaptor.modifyShopifyProductImage(ShopifyRequestDtoFactory.buildVariantImageDto(itemDto,
                    shopifyGraphQLQueryResponseDto, null));
            }

            ShopifyProductDto shopifyProductDto = shopifyAdaptor.modifyShopifyProduct(ShopifyRequestDtoFactory.buildShopifyProductDto(
                itemDto, shopifyGraphQLQueryResponseDto));

            shopifyAdaptor.createOrModifyShopifyProductMetaFields(ShopifyRequestDtoFactory.buildMetaFieldDto(itemDto),
                shopifyProductDto);

            shopifyAdaptor.deletedShopifyProductMetaFields(ShopifyRequestDtoFactory.buildDeleteMetaFieldDto(itemDto),
                shopifyProductDto);

            shopifyAdaptor.modifyShopifyInventoryItem(ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto,
                shopifyProductDto));
            if (ACTIVE.name().equals(itemDto.getAvailabilityStatus())) {
                shopifyAdaptor.setShopifyChannels(shopifyProductDto);
            }
            log.info("end syncItemAmendEvent for sku ={}, time ={}", itemDto.getSkuNumber(), System.currentTimeMillis());
            itemAdjustmentSyncStatusService.saveItemAdjustmentSyncSuccessStatus(businessEventId);
            if (itemAmendPayloadDto.getItemAdjustmentRequestDetailId() != null) {
                itemAdjustmentRequestDetailApplicationService.syncToShopify(itemAmendPayloadDto.getItemAdjustmentRequestDetailId());
            }
        } catch (Exception e) {
            log.error("syncItemAmendEvent error for request ={}", itemAmendPayloadDto, e);
            itemAdjustmentSyncStatusService.saveItemAdjustmentSyncFiledStatus(businessEventId);
            if (itemAmendPayloadDto.getItemAdjustmentRequestDetailId() != null) {
                itemAdjustmentRequestDetailApplicationService.syncToShopifyFailure(itemAmendPayloadDto.getItemAdjustmentRequestDetailId(),
                    e.getMessage());
            }
        }
    }

    @VisibleForTesting
    protected void syncItemDeletedEventToShopify(ItemDeletedPayloadDto itemDeletedPayloadDto,
        ItemDto itemDto,
        UUID businessEventId) {
        try {
            log.info("begin syncItemDeleteEvent for sku ={}, time ={}", itemDto.getSkuNumber(), System.currentTimeMillis());

            ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = shopifyAdaptor.queryProduct(itemDto.getSkuNumber());
            Optional.ofNullable(shopifyGraphQLQueryResponseDto.getData().getProducts().getEdges())
                .orElse(Collections.emptyList())
                .forEach(productEdgeDto -> shopifyAdaptor.deleteShopifyProduct(ShopifyRequestDtoFactory.extractId(
                    productEdgeDto.getNode()
                        .getId())));
            log.info("end syncItemDeleteEvent for sku ={}, time ={}", itemDto.getSkuNumber(), System.currentTimeMillis());
            itemAdjustmentSyncStatusService.saveItemAdjustmentSyncSuccessStatus(businessEventId);
            if (itemDeletedPayloadDto.getItemAdjustmentRequestDetailId() != null) {
                itemAdjustmentRequestDetailApplicationService.syncToShopify(itemDeletedPayloadDto.getItemAdjustmentRequestDetailId());
            }
        } catch (Exception e) {
            log.error("syncItemDeleteEvent error for request ={}", itemDeletedPayloadDto, e);
            itemAdjustmentSyncStatusService.saveItemAdjustmentSyncFiledStatus(businessEventId);
            if (itemDeletedPayloadDto.getItemAdjustmentRequestDetailId() != null) {
                itemAdjustmentRequestDetailApplicationService.syncToShopifyFailure(itemDeletedPayloadDto.getItemAdjustmentRequestDetailId(),
                    e.getMessage());
            }
        }
    }

}
