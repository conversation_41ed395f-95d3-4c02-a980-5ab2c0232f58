package com.mercaso.ims.application.service.impl;

import com.mercaso.ims.application.dto.DifyCategoryMatchingFinalResultDto;
import com.mercaso.ims.application.dto.DifyCategoryMatchingOutputDto;
import com.mercaso.ims.application.service.DifyworkflowApplicationService;
import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecord;
import com.mercaso.ims.domain.difyworkflowsrecord.enums.WorkflowStatus;
import com.mercaso.ims.domain.difyworkflowsrecord.service.DifyWorkflowRecordService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.dify.DifyApiClient;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.UUID;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.GET_RECOMMENDED_CATEGORIES_ERROR;

@Service
@Slf4j
@RequiredArgsConstructor
public class DifyworkflowApplicationServiceImpl implements DifyworkflowApplicationService {

    private static final String LOG_PREFIX = "[getRecommendedCategories]";
    private static final String INPUT_KEY_DESCRIPTION = "description";

    private final DifyApiClient difyApiClient;
    private final DifyWorkflowRecordService difyWorkflowRecordService;

    @Override
    public DifyCategoryMatchingFinalResultDto getRecommendedCategories(String description) {
        if (StringUtils.isBlank(description)) {
            log.warn("{} description is blank", LOG_PREFIX);
            return createEmptyResult();
        }

        try {
            DifyWorkflowResult workflowResult = callDifyWorkflow(description);

            saveWorkflowRecord(description, workflowResult);

            return processWorkflowResult(workflowResult.getResult());

        } catch (Exception e) {
            log.error("{} failed to get recommended categories for description: {}", LOG_PREFIX, description, e);
            throw new ImsBusinessException(GET_RECOMMENDED_CATEGORIES_ERROR);
        }
    }

    private DifyWorkflowResult callDifyWorkflow(String description) throws Exception {
        log.info("{} calling dify workflow with description length: {}", LOG_PREFIX, description.length());
        return difyApiClient.callDifyWorkflow(INPUT_KEY_DESCRIPTION, description);
    }


    private void saveWorkflowRecord(String description, DifyWorkflowResult workflowResult) {
        try {
            DifyWorkflowRecord difyWorkflowRecord = buildWorkflowRecord(description, workflowResult);
            difyWorkflowRecordService.save(difyWorkflowRecord);
            log.info("{} workflow record saved, workflowId: {}", LOG_PREFIX, workflowResult.getWorkflowRunId());
        } catch (Exception e) {
            log.error("{} failed to save workflow record for workflowId: {}", 
                     LOG_PREFIX, workflowResult.getWorkflowRunId(), e);
        }
    }

    private DifyWorkflowRecord buildWorkflowRecord(String description, DifyWorkflowResult workflowResult) {
        return DifyWorkflowRecord.builder()
                .input(description)
                .workflowId(parseUUIDSafely(workflowResult.getWorkflowRunId()))
                .totalTokens(workflowResult.getTotalTokens())
                .totalSteps(workflowResult.getTotalSteps())
                .elapsedTime(workflowResult.getElapsedTime())
                .result(workflowResult.getResult())
                .status(parseWorkflowStatusSafely(workflowResult.getStatus()))
                .build();
    }


    private DifyCategoryMatchingFinalResultDto processWorkflowResult(String resultJson) {
        if (StringUtils.isBlank(resultJson)) {
            log.warn("{} workflow result is blank", LOG_PREFIX);
            return createEmptyResult();
        }

        try {
            DifyCategoryMatchingFinalResultDto result = deserializeResult(resultJson);
            
            return validateAndProcessCategoryId(result);
            
        } catch (Exception e) {
            log.error("{} failed to process workflow result", LOG_PREFIX, e);
            return createEmptyResult();
        }
    }


    private DifyCategoryMatchingFinalResultDto deserializeResult(String resultJson) throws Exception {
        log.info("{} deserializing result json", LOG_PREFIX);
        DifyCategoryMatchingOutputDto deserialize = SerializationUtils.deserialize(resultJson, DifyCategoryMatchingOutputDto.class);
        if (null == deserialize) {
            return null;
        }
        return deserialize.getOutput();
    }


    private DifyCategoryMatchingFinalResultDto validateAndProcessCategoryId(DifyCategoryMatchingFinalResultDto result) {
        if (result == null || result.getFinalResult() == null) {
            log.warn("{} result or finalResult is null", LOG_PREFIX);
            return createEmptyResult();
        }

        log.info("{} successfully matched category id: {}", LOG_PREFIX, result.getFinalResult());
        return result;
    }


    private UUID parseUUIDSafely(String uuidString) {
        if (StringUtils.isBlank(uuidString)) {
            return null;
        }
        
        try {
            return UUID.fromString(uuidString);
        } catch (IllegalArgumentException e) {
            log.warn("{} invalid UUID format: {}", LOG_PREFIX, uuidString);
            return null;
        }
    }


    private WorkflowStatus parseWorkflowStatusSafely(String statusString) {
        if (StringUtils.isBlank(statusString)) {
            return null;
        }
        
        try {
            return WorkflowStatus.valueOf(statusString.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("{} invalid workflow status: {}", LOG_PREFIX, statusString);
            return null;
        }
    }


    private DifyCategoryMatchingFinalResultDto createEmptyResult() {
        return DifyCategoryMatchingFinalResultDto.builder().build();
    }
}
