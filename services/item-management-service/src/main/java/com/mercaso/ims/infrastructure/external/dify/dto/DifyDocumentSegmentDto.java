package com.mercaso.ims.infrastructure.external.dify.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DifyDocumentSegmentDto {
    
    private String id;
    
    private Integer position;
    
    @JsonProperty("document_id")
    private String documentId;
    
    private String content;
    
    @JsonProperty("sign_content")
    private String signContent;
    
    private String answer;
    
    @JsonProperty("word_count")
    private Integer wordCount;
    
    private Integer tokens;
    
    private List<String> keywords;
    
    @JsonProperty("index_node_id")
    private String indexNodeId;
    
    @JsonProperty("index_node_hash")
    private String indexNodeHash;
    
    @JsonProperty("hit_count")
    private Integer hitCount;
    
    private Boolean enabled;
    
    @JsonProperty("disabled_at")
    private Long disabledAt;
    
    @JsonProperty("disabled_by")
    private String disabledBy;
    
    private String status;
    
    @JsonProperty("created_by")
    private String createdBy;
    
    @JsonProperty("created_at")
    private Long createdAt;
    
    @JsonProperty("updated_at")
    private Long updatedAt;
    
    @JsonProperty("updated_by")
    private String updatedBy;
    
    @JsonProperty("indexing_at")
    private Long indexingAt;
    
    @JsonProperty("completed_at")
    private Long completedAt;
    
    private String error;
    
    @JsonProperty("stopped_at")
    private Long stoppedAt;
    
    @JsonProperty("child_chunks")
    private List<DifyDocumentSegmentDto> childChunks;
}
