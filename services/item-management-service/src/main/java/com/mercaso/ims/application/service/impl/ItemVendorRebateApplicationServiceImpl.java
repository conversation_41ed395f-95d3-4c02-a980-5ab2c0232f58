package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.HAS_OVERLAPPING_REBATES;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_ITEM_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_REBATE_NOT_FOUND;

import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.mapper.itemvendorrebate.ItemVendorRebateDtoApplicationMapper;
import com.mercaso.ims.application.service.ItemVendorRebateApplicationService;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateFactory;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Application service for ItemVendorRebate operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ItemVendorRebateApplicationServiceImpl implements ItemVendorRebateApplicationService {

    private final ItemVendorRebateService itemVendorRebateService;
    private final ItemVendorRebateFactory itemVendorRebateFactory;
    private final VendorItemService vendorItemService;
    private final ItemVendorRebateDtoApplicationMapper itemVendorRebateDtoApplicationMapper;

    /**
     * Create a new item vendor rebate using command
     * @param command the create command
     * @return the created rebate DTO
     */
    @Override
    public ItemVendorRebateDto createRebate(CreateItemVendorRebateCommand command) {

        UUID vendorItemId = command.getVendorItemId();
        VendorItem vendorItem;
        if (null == vendorItemId) {
            vendorItem = vendorItemService.findByVendorIDAndItemId(command.getVendorId(), command.getItemId());
        } else {
            vendorItem = vendorItemService.findById(vendorItemId);
        }

        if (null == vendorItem) {
            log.error("Vendor item not foung vendorItemId:{}", vendorItemId);
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }

        // Validate consistency if command provides explicit vendorId or itemId
        if (command.getVendorId() != null && !command.getVendorId().equals(vendorItem.getVendorId())) {
            log.error("Command vendorId {} does not match vendorItem's vendorId {}", 
                     command.getVendorId(), vendorItem.getVendorId());
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }
        if (command.getItemId() != null && !command.getItemId().equals(vendorItem.getItemId())) {
            log.error("Command itemId {} does not match vendorItem's itemId {}", 
                     command.getItemId(), vendorItem.getItemId());
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }

        log.info("Creating new ItemVendorRebate for vendorItemId: {}", vendorItem.getId());

        // Get rebates for vendor item - implemented in memory for better performance
        List<ItemVendorRebate> itemVendorRebates = itemVendorRebateService.findByVendorItemId(vendorItem.getId());

        // Check for overlapping rebates using in-memory logic equivalent to the SQL query
        boolean hasOverlappingRebates = hasOverlappingRebates(
            itemVendorRebates,
            command.getStartDate(),
            command.getEndDate()
        );

        // Throw exception if overlapping rebates found
        if (hasOverlappingRebates) {
            log.error("Rebate schedule conflicts with existing rebates, vendorItemId:{}", vendorItem.getId());
            throw new ImsBusinessException(HAS_OVERLAPPING_REBATES);
        }

        ItemVendorRebate rebate = itemVendorRebateFactory.create(
                vendorItem.getId(),
                vendorItem.getVendorId(),
                vendorItem.getItemId(),
                command.getStartDate(),
                command.getEndDate(),
                command.getRebatePerUnit()
        );

        ItemVendorRebate savedRebate = itemVendorRebateService.save(rebate);


        return itemVendorRebateDtoApplicationMapper.domainToDto(savedRebate);
    }

    /**
     * Update an existing item vendor rebate using command
     * @param command the update command
     * @return the updated rebate DTO
     */
    @Override
    public ItemVendorRebateDto updateRebate(UpdateItemVendorRebateCommand command) {

        UUID rebateId = command.getId();

        // Find the existing rebate
        ItemVendorRebate existingRebate = itemVendorRebateService.findById(rebateId);
        if (null == existingRebate) {
            log.error("Vendor rebate not found, rebateId:{}", rebateId);
            throw new ImsBusinessException(VENDOR_REBATE_NOT_FOUND);
        }

        log.info("Updating ItemVendorRebate with id: {}", rebateId);

        // Validate vendor item exists
        UUID vendorItemId = command.getVendorItemId();
        VendorItem vendorItem = vendorItemService.findById(vendorItemId);
        if (null == vendorItem) {
            log.error("Vendor item not found vendorItemId:{}", vendorItemId);
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }

        // Get all rebates for vendor item and exclude the current one being updated
        List<ItemVendorRebate> allItemVendorRebates = itemVendorRebateService.findByVendorItemId(vendorItem.getId());
        List<ItemVendorRebate> itemVendorRebates = allItemVendorRebates
                .stream()
                .filter(rebate -> !rebate.getId().equals(command.getId()))
                .toList();


        // Check for overlapping rebates with other rebates (excluding current one)
        boolean hasOverlappingRebates = hasOverlappingRebates(
            itemVendorRebates,
            command.getStartDate(),
            command.getEndDate()
        );

        // Throw exception if overlapping rebates found
        if (hasOverlappingRebates) {
            log.error("Updated rebate schedule conflicts with existing rebates, vendorItemId:{}", vendorItem.getId());
            throw new ImsBusinessException(HAS_OVERLAPPING_REBATES);
        }

        // Update the rebate properties
        existingRebate.setStartDate(command.getStartDate());
        existingRebate.setEndDate(command.getEndDate());
        existingRebate.setRebatePerUnit(command.getRebatePerUnit());
        if (command.getItemVendorRebateStatus() != null) {
            existingRebate.setItemVendorRebateStatus(command.getItemVendorRebateStatus());
        }

        // Save the updated rebate
        ItemVendorRebate updatedRebate = itemVendorRebateService.update(existingRebate);

        return itemVendorRebateDtoApplicationMapper.domainToDto(updatedRebate);
    }

    @Override
    public void deleteRebate(UUID id) {
        itemVendorRebateService.delete(id);
    }

    /**
     * Check if two rebates overlap using optimized logic
     * Handles both continuous (null end date) and fixed period rebates
     * @param existingRebate the existing rebate
     * @param newStartDate the new rebate's start date
     * @param newEndDate the new rebate's end date (can be null for continuous)
     * @return true if rebates overlap
     */
    private boolean checkRebateOverlap(ItemVendorRebate existingRebate,
                                       Instant newStartDate, Instant newEndDate) {
        Instant existingStartDate = existingRebate.getStartDate();
        Instant existingEndDate = existingRebate.getEndDate();
        UUID vendorItemId = existingRebate.getVendorItemId();

        // Both rebates are continuous (no end date) - always overlap
        if (newEndDate == null && existingEndDate == null) {
            log.warn("Found overlapping continuous rebates for vendorItemId: {}", vendorItemId);
            return true;
        }

        // Determine effective end dates (use far future for continuous rebates)
        Instant newEffectiveEnd = newEndDate != null ? newEndDate : Instant.MAX;
        Instant existingEffectiveEnd = existingEndDate != null ? existingEndDate : Instant.MAX;

        // Check for overlap: two periods overlap if one starts before the other ends
        // Period 1: [newStartDate, newEffectiveEnd], Period 2: [existingStartDate, existingEffectiveEnd]
        // No overlap if: newEffectiveEnd < existingStartDate OR existingEffectiveEnd < newStartDate
        // Overlap if: NOT (no overlap) = newEffectiveEnd >= existingStartDate AND existingEffectiveEnd >= newStartDate
        boolean hasOverlap = newEffectiveEnd.isAfter(existingStartDate) &&
                            existingEffectiveEnd.isAfter(newStartDate);

        if (hasOverlap) {
            String rebateType = (newEndDate == null || existingEndDate == null) ? "continuous" : "fixed";
            log.warn("Found overlapping {} rebates for vendorItemId: {}, existing: [{}, {}], new: [{}, {}]", 
                    rebateType, vendorItemId, existingStartDate, existingEndDate, newStartDate, newEndDate);
        }

        return hasOverlap;
    }

    /**
     * Check for overlapping rebates in memory using stream operations
     * @param existingRebates list of existing rebates to check against
     * @param newStartDate the new rebate's start date
     * @param newEndDate the new rebate's end date (can be null for continuous)
     * @return true if any overlapping rebates found
     */
    private boolean hasOverlappingRebates(List<ItemVendorRebate> existingRebates, Instant newStartDate, Instant newEndDate) {
        return existingRebates.stream().anyMatch(existingRebate ->
            checkRebateOverlap(existingRebate, newStartDate, newEndDate));
    }
}
