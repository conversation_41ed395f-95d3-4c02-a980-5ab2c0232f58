package com.mercaso.ims.domain.vendor;

import com.mercaso.ims.domain.Specification;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
@Slf4j
public class VendorSpecification implements Specification<Vendor> {

    private final VendorRepository vendorRepository;

    public boolean isSatisfiedBackupVendor(String backupVendorName) {
        if (StringUtils.isBlank(backupVendorName)) {
            Vendor backupVendor = vendorRepository.findByVendorName(backupVendorName);
            return isSatisfiedBackupVendor(backupVendor);
        }
        return true;
    }

    public boolean isSatisfiedBackupVendor(UUID backupVendorId) {
        if (backupVendorId != null) {
            Vendor backupVendor = vendorRepository.findById(backupVendorId);
            return isSatisfiedBackupVendor(backupVendor);
        }
        return true;
    }


    public boolean isSatisfiedBackupVendor(Vendor backupVendor) {
        if (backupVendor == null) {
            log.warn("Backup vendor is null");
            return false;
        }
        if (backupVendor.getExternalPicking() == null || !backupVendor.getExternalPicking()) {
            log.warn("Backup vendor : {} is not enabled for ExternalPicking", backupVendor.getVendorName());
            return false;
        }
        return true;
    }


}
