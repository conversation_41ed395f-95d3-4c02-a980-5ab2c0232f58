package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VendorItemDto extends BaseDto {

    private UUID vendorItemId;

    private UUID vendorId;

    private UUID itemId;

    private String itemSkuNumber;

    private String vendorName;

    private String vendorFinaleId;

    private String vendorSkuNumber;

    private String vendorItemName;

    private String note;

    private String statusChangeReason;

    private String aisle;

    private BigDecimal lowestCost;

    private BigDecimal highestCost;

    private BigDecimal cost;

    @Deprecated
    private BigDecimal secondaryCost;

    private BigDecimal backupCost;

    private VendorItemStatus vendorItemStatus;

    private Instant costFreshnessTime;

    private Instant backupCostFreshnessTime;

    private Boolean availability;

    private String vendorItemType;

    private List<ItemVendorRebateDto> itemVendorRebateDtos;


    public boolean isJitVendorItem() {
        return vendorItemType != null &&
            (VendorItemType.JIT.getTypeName().equals(vendorItemType) ||
                VendorItemType.DIRECT_JIT.getTypeName().equals(vendorItemType));
    }

    public boolean isDirectVendorItem() {
        return vendorItemType != null &&
            (VendorItemType.DIRECT.getTypeName().equals(vendorItemType) ||
                VendorItemType.DIRECT_JIT.getTypeName().equals(vendorItemType));
    }

}
