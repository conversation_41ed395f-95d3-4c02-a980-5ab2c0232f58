package com.mercaso.ims.domain.webaddress.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum WebAddressType {
    WEBSITE("Website"),
    PORTAL("Portal"),
    ECOMMERCE("E-commerce"),
    SUPPORT("Support"),
    DOCUMENTATION("Documentation"),
    API("API"),
    SOCIAL_MEDIA("Social Media"),
    BLOG("Blog"),
    NEWS("News"),
    OTHER("Other"),
    UNKNOWN("Unknown"),
    ;

    private final String displayName;

    WebAddressType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JsonCreator
    public static WebAddressType fromString(String name) {
        return Arrays.stream(values())
            .filter(v -> v.name().equalsIgnoreCase(name) || v.displayName.equalsIgnoreCase(name))
            .findFirst()
            .orElse(UNKNOWN);
    }
}
