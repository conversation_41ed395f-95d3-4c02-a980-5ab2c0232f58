package com.mercaso.ims.application.query;

import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

@Getter
@Setter
@Data
@Builder
public class ItemCategoryQuery {

    private List<UUID> ids;
    private List<String> skus;


    public boolean hasIds() {
        return CollectionUtils.isNotEmpty(ids);
    }


    public boolean hasSkus() {
        return CollectionUtils.isNotEmpty(skus);
    }

}
