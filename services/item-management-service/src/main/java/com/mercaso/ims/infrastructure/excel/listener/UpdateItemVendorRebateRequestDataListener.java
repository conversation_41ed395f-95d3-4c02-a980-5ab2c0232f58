package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.UpdateItemVendorRebateRequestData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@Slf4j
public class UpdateItemVendorRebateRequestDataListener extends ItemAdjustmentRequestDataListener<UpdateItemVendorRebateRequestData> {

    private String timeZone;

    public UpdateItemVendorRebateRequestDataListener(UUID itemAdjustmentRequestId, String timeZone,
                                                     ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
                                                     ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
                                                     ItemRepository itemRepository,
                                                     VendorRepository vendorRepository,
                                                     VendorItemRepository vendorItemRepository,
                                                     CategoryApplicationService categoryApplicationService,
                                                     FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository, ItemVendorRebateService itemVendorRebateService) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository, itemVendorRebateService);

        this.timeZone = timeZone;
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(UpdateItemVendorRebateRequestData updateItemVendorRebateRequestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(updateItemVendorRebateRequestData.getSku())
            .vendor(updateItemVendorRebateRequestData.getVendor())
            .rebateStartDate(updateItemVendorRebateRequestData.getStartDate())
            .rebateEndDate(updateItemVendorRebateRequestData.getEndDate())
            .rebatePerSellingUnit(updateItemVendorRebateRequestData.getRebatePerSellingUnit())
            .timeZone(timeZone)
            .build();
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.UPDATE_REBATE;
    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(UpdateItemVendorRebateRequestData updateItemVendorRebateRequestData) {
        // Use common validation for SKU and vendor
        List<ItemAdjustmentFailureReason> failureReasons = validateCommonRebateInput(
            updateItemVendorRebateRequestData.getSku(),
            updateItemVendorRebateRequestData.getVendor(),
            ItemAdjustmentType.UPDATE_REBATE
        );

        // If common validation failed, return early
        if (!failureReasons.isEmpty()) {
            return failureReasons;
        }

        // Validate rebate date window
        String startDate = updateItemVendorRebateRequestData.getStartDate();
        String endDate = updateItemVendorRebateRequestData.getEndDate();
        ItemAdjustmentFailureReason validateRebateDateWindowResult = checkRebateDateWindow(startDate, endDate, timeZone);
        if (validateRebateDateWindowResult != null) {
            failureReasons.add(validateRebateDateWindowResult);
        }

        // Validate rebate per selling unit
        String rebatePerSellingUnit = updateItemVendorRebateRequestData.getRebatePerSellingUnit();
        ItemAdjustmentFailureReason validateRebatePerSellingUnitResult = checkRebatePerSellingUnit(rebatePerSellingUnit);
        if (validateRebatePerSellingUnitResult != null) {
            failureReasons.add(validateRebatePerSellingUnitResult);
        }

        return failureReasons;
    }
}
