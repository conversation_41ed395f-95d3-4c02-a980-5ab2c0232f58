package com.mercaso.ims.infrastructure.repository.webaddress.jpa.dataobject;

import com.mercaso.ims.domain.webaddress.enums.WebAddressType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@Entity
@Table(name = "web_address")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update web_address set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class WebAddressDo extends BaseDo {

    @Column(name = "entity_type")
    private String entityType;

    @Column(name = "entity_id")
    private UUID entityId;

    @Enumerated(EnumType.STRING)
    @Column(name = "web_address_type")
    private WebAddressType webAddressType;

    @Column(name = "web_address")
    private String webAddress;
}
