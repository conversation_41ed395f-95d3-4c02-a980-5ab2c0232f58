package com.mercaso.ims.domain.phone.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum PhoneType {
    MOBILE("Mobile"),
    HOME("Home"),
    <PERSON><PERSON>K("Work"),
    FAX("Fax"),
    <PERSON><PERSON>ER("Other"),
    UNKNOWN("Unknown"),
    ;

    private final String displayName;

    PhoneType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JsonCreator
    public static PhoneType fromString(String name) {
        return Arrays.stream(values())
            .filter(v -> v.name().equalsIgnoreCase(name) || v.displayName.equalsIgnoreCase(name))
            .findFirst()
            .orElse(UNKNOWN);
    }
}
