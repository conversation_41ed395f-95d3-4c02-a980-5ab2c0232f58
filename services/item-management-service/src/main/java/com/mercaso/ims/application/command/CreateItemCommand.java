package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemTagDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.domain.item.enums.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateItemCommand extends BaseCommand {

    private final UUID id;

    private UUID categoryId;

    private String brandId;

    private String name;

    private String title;

    private String skuNumber;

    private String description;

    private String note;

    private String photoName;

    private String photoUrl;

    private UUID primaryVendorId;

    private UUID backupVendorId;

    private String detail;

    private PackageType packageType;

    private Integer packageSize;

    private String shelfLife;

    private ItemType itemType;

    private SalesStatus salesStatus;

    private AvailabilityStatus availabilityStatus;

    private Long companyId;

    private Long locationId;

    private String handle;

    private UUID itemAdjustmentRequestDetailId;

    private String newDescription;

    private Boolean crvFlag;

    private String department;

    private String category;

    private String subCategory;

    private String clazz;

    private Double length;

    private Double height;

    private Double width;

    private BigDecimal regPrice;

    private MissingEachUpcReason missingEachUpcReason;

    private MissingCaseUpcReason missingCaseUpcReason;

    private ArchivedReason archivedReason;

    private List<ItemTagDto> itemTags;
    private List<ItemAttributeDto> itemAttributes;
    private List<ItemUPCDto> itemUPCs;
    private List<ItemImageDto> itemImages;
    private List<VendorItemDto> vendorItems;
    private List<ItemPromoPriceDto> itemPromoPrices;

    public Boolean getPromoFlag() {
        if (itemPromoPrices != null && !itemPromoPrices.isEmpty()) {
            return itemPromoPrices.getFirst()
                .getPromoFlag();
        }
        return null;
    }

    private boolean hasUpcType(ItemUpcType upcType) {
        return itemUPCs != null && itemUPCs.stream()
            .anyMatch(upc -> upc.getItemUpcType() == upcType);
    }

    public Boolean validateMissingEachUpcReason() {
        boolean hasEachUpc = hasUpcType(ItemUpcType.EACH_UPC);
        boolean hasReason = missingEachUpcReason != null;
        return hasEachUpc != hasReason;
    }

    public Boolean validateMissingCaseUpcReason() {
        boolean hasCaseUpc = hasUpcType(ItemUpcType.CASE_UPC);
        boolean hasReason = missingCaseUpcReason != null;
        return hasCaseUpc != hasReason;
    }

    public Boolean validateArchivedReason() {
        boolean isArchived = isArchived();
        boolean hasReason = archivedReason != null;
        if (isArchived && !hasReason) {
            return false;
        }
        return true;
    }

    public boolean isArchived() {
        return AvailabilityStatus.ARCHIVED.equals(availabilityStatus);
    }
}
