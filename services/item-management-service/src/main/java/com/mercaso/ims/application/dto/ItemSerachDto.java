package com.mercaso.ims.application.dto;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemSerachDto extends BaseDto {

    private UUID id;

    private String name;

    private String title;

    private String skuNumber;

    private String description;

    private String note;

    private String photoName;

    private String photoUrl;

    private UUID primaryVendorId;

    private String primaryVendorName;

    private UUID primaryVendorItemId;

    private String primaryVendorSkuNumber;

    private BigDecimal primaryVendorCost;

    private UUID backupVendorId;

    private UUID backupVendorItemId;

    private String backupVendorName;

    private BigDecimal backupVendorCost;

    private String backupVendorSkuNumber;

    private String detail;

    private String packageType;

    private Integer packageSize;

    private String itemType;

    private String availabilityStatus;

    private String handle;

    private String itemAttribute;

    private String itemTags;

    private String itemUPCs;

    private String vendorItem;

    @Deprecated
    private String categoryName;

    private UUID categoryId;

    private String brandName;

    private UUID brandId;

    private BigDecimal crv;

    private BigDecimal regPrice;

    private BigDecimal regIndividualPrice;

    private BigDecimal regPlusCrvPrice;

    private Double length;

    private Double height;

    private Double width;

    private String shelfLife;

    private Instant createdAt;

    private String createdBy;

    private String createdUserName;

    private Instant updatedAt;

    private String updatedBy;

    private String updatedUserName;

    private String department;

    private String category;

    private String subCategory;

    private String clazz;

    private String newDescription;

    private Boolean promoFlag;

    private UUID itemPriceGroupId;

    private String itemPriceGroupName;

    private UUID departmentId;

    private UUID subCategoryId;

    private UUID clazzId;

    private String grade;

    private Long wos1Week;

    private Long wos4Weeks;

    private Long companyId;

    private Long locationId;

    private String caseUpc;

    private String eachUpc;

    private String caseWeight;

    private String eachWeight;

    private String itemSize;

    private String bottleSize;

    private String cooler;

    private String highValue;

    private Boolean vendorItemAvailability;

    private Boolean crvFlag;

    private BigDecimal promoPrice;

    private BigDecimal promoPriceIndividual;

    private BigDecimal promoPricePlusCrv;

    private Instant promoBeginTime;

    private Instant promoEndTime;

    private String missingEachUpcReason;

    private String missingCaseUpcReason;

    private String archivedReason;

    private List<ItemVendorRebateDto> itemVendorRebateDtos;

}