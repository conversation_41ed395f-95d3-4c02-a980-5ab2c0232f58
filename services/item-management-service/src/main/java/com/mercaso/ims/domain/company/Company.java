package com.mercaso.ims.domain.company;

import com.mercaso.ims.domain.BaseDomain;
import java.util.UUID;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@ToString
@SuperBuilder
public class Company extends BaseDomain {
    private final UUID id;

    private Long companyId;

    private String name;

    public Company create(String name) {
        validateName(name);
        return Company.builder()
            .name(name)
            .build();
    }

    public void updateCompanyInfo(Long companyId, String name) {
        this.name = name;
        this.companyId = companyId;
    }

    public void updateName(String name) {
        validateName(name);
        this.name = name;
    }

    private static void validateName(String name) {
        if (name == null || name.isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }
    }
}
