package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateItemVendorRebateRequestData;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.UUID;

@Slf4j
public class CreateItemVendorRebateRequestDataListener extends ItemAdjustmentRequestDataListener<CreateItemVendorRebateRequestData> {

    private String timeZone;

    public CreateItemVendorRebateRequestDataListener(UUID itemAdjustmentRequestId, String timeZone,
                                                     ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
                                                     ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
                                                     ItemRepository itemRepository,
                                                     VendorRepository vendorRepository,
                                                     VendorItemRepository vendorItemRepository,
                                                     CategoryApplicationService categoryApplicationService,
                                                     FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository,
                                                     ItemVendorRebateService itemVendorRebateService) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository, itemVendorRebateService);

        this.timeZone = timeZone;
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(CreateItemVendorRebateRequestData createItemVendorRebateRequestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(createItemVendorRebateRequestData.getSku())
            .vendor(createItemVendorRebateRequestData.getVendor())
            .rebateStartDate(createItemVendorRebateRequestData.getStartDate())
            .rebateEndDate(createItemVendorRebateRequestData.getEndDate())
            .rebatePerSellingUnit(createItemVendorRebateRequestData.getRebatePerSellingUnit())
            .timeZone(timeZone)
            .build();
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.CREATE_REBATE;
    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(CreateItemVendorRebateRequestData createItemVendorRebateRequestData) {
        // Use common validation for SKU and vendor
        List<ItemAdjustmentFailureReason> failureReasons = validateCommonRebateInput(
            createItemVendorRebateRequestData.getSku(),
            createItemVendorRebateRequestData.getVendor(),
            ItemAdjustmentType.CREATE_REBATE
        );

        // If common validation failed, return early
        if (!failureReasons.isEmpty()) {
            return failureReasons;
        }

        // Additional validation specific to CREATE operation
        String startDate = createItemVendorRebateRequestData.getStartDate();
        String endDate = createItemVendorRebateRequestData.getEndDate();
        String rebatePerSellingUnit = createItemVendorRebateRequestData.getRebatePerSellingUnit();

        // Start date is required for CREATE operation
        if (StringUtils.isBlank(startDate)) {
            failureReasons.add(ItemAdjustmentFailureReason.REBATE_START_DATE_IS_REQUIRED);
        }

        // Rebate per selling unit is required for CREATE operation
        if (StringUtils.isBlank(rebatePerSellingUnit)) {
            failureReasons.add(ItemAdjustmentFailureReason.REBATE_PER_SELLING_UNIT_IS_REQUIRED);
        }

        // Validate rebate date window

        ItemAdjustmentFailureReason validateRebateDateWindowResult = checkRebateDateWindow(startDate, endDate, timeZone);
        if (validateRebateDateWindowResult != null) {
            failureReasons.add(validateRebateDateWindowResult);
        }

        // Validate rebate per selling unit format
        ItemAdjustmentFailureReason validateRebatePerSellingUnitResult = checkRebatePerSellingUnit(rebatePerSellingUnit);
        if (validateRebatePerSellingUnitResult != null) {
            failureReasons.add(validateRebatePerSellingUnitResult);
        }

        return failureReasons;
    }
}
