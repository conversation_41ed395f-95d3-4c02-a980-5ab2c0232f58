package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemUnboundFromPriceGroupPayloadDto extends BusinessEventPayloadDto<AmendDto<ItemDto>> {

    private UUID itemId;

    private UUID priceGroupId;

    private String priceGroupName;

    @Builder
    public ItemUnboundFromPriceGroupPayloadDto(ItemDto previous, ItemDto current, UUID priceGroupId,
        String priceGroupName,
        UUID itemId) {
        super(new AmendDto<>(previous, current));
        this.itemId = itemId;
        this.priceGroupId = priceGroupId;
        this.priceGroupName = priceGroupName;
    }
}