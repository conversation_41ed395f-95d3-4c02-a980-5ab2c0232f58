package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateItemPriceGroupCommand extends BaseCommand {

    private String groupName;

    @NotNull(message = "Price must not be null.")
    @DecimalMin(value = "0.01", message = "Price must be greater than zero.")
    private BigDecimal price;

    private List<UUID> itemIds;

}
