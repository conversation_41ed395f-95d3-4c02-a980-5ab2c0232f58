package com.mercaso.ims.interfaces.rest;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BatchUpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.BatchUpdateItemTargetCostChangeRequestCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemCostChangeRequestResultDto;
import com.mercaso.ims.application.service.ItemCostChangeRequestApplicationService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/item-cost-change-request", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class ItemCostChangeRequestRestApi {

    private final ItemCostChangeRequestApplicationService itemCostChangeRequestApplicationService;


    @PutMapping("/batch/approve")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public BatchUpdateItemCostChangeRequestResultDto batchApproveItemCostChangeRequest(
        @RequestBody BatchUpdateItemCostChangeRequestCommand command) {
        log.info("[approveItemCostChangeRequest] param command: {}.", command);
        return itemCostChangeRequestApplicationService.batchApproveItemCostChangeRequest(command);
    }

    @PutMapping("/batch/reject")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public BatchUpdateItemCostChangeRequestResultDto batchRejectItemCostChangeRequest(
        @RequestBody BatchUpdateItemCostChangeRequestCommand command) {
        log.info("[batchRejectItemCostChangeRequest] param command: {}.", command);
        return itemCostChangeRequestApplicationService.batchRejectItemCostChangeRequest(command);
    }

    @PutMapping("/batch/change-target-cost")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public BatchUpdateItemCostChangeRequestResultDto batchChangeTargetCostRequest(
        @RequestBody BatchUpdateItemTargetCostChangeRequestCommand command) {
        log.info("[batchChangeTargetCostRequest] param command: {}.", command);
        return itemCostChangeRequestApplicationService.batchChangeItemTargetCostChangeRequest(command);
    }

    @GetMapping("/{id}/file")
    @PreAuthorize("hasAuthority('ims:read:vendor-items')")
    public DocumentResponse getItemCostCollectionFileByChangeRequestId(@PathVariable UUID id) {
        log.info("[getItemCostCollectionFileByChangeRequestId] param itemCostChangeRequestId: {}.", id);
        return itemCostChangeRequestApplicationService.getItemCostCollectionFile(id);
    }

}
