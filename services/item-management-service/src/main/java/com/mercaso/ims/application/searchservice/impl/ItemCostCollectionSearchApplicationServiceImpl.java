package com.mercaso.ims.application.searchservice.impl;

import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.dto.ItemCostChangeRequestListDto;
import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.application.dto.ItemCostCollectionListDto;
import com.mercaso.ims.application.query.ItemCostChangeRequestQuery;
import com.mercaso.ims.application.query.ItemCostCollectionQuery;
import com.mercaso.ims.application.searchservice.ItemCostCollectionSearchApplicationService;
import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.CustomizedItemCostChangeRequestJpaDao;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.CustomizedItemCostCollectionJpaDao;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class ItemCostCollectionSearchApplicationServiceImpl implements ItemCostCollectionSearchApplicationService {

    private final CustomizedItemCostCollectionJpaDao customizedItemCostCollectionJpaDao;
    private final CustomizedItemCostChangeRequestJpaDao customizedItemCostChangeRequestJpaDao;


    @Override
    public ItemCostCollectionListDto searchItemCostCollectionList(ItemCostCollectionQuery query) {
        List<ItemCostCollectionDetailDto> itemCostCollections = fetchItemCostCollection(query);
        if (itemCostCollections.isEmpty()) {
            return ItemCostCollectionListDto.builder()
                .data(Collections.emptyList())
                .totalCount(0L)
                .build();
        }

        long totalCount = customizedItemCostCollectionJpaDao.countQuery(query);

        return ItemCostCollectionListDto.builder()
            .data(itemCostCollections)
            .totalCount(totalCount)
            .build();
    }

    @Override
    public ItemCostChangeRequestListDto searchItemCostChangeRequestList(ItemCostChangeRequestQuery query) {
        List<ItemCostChangeRequestDto> itemCostChangeRequestDtos = customizedItemCostChangeRequestJpaDao.fetchItemCostChangeRequestDtoList(
            query);
        if (itemCostChangeRequestDtos.isEmpty()) {
            return ItemCostChangeRequestListDto.builder()
                .data(Collections.emptyList())
                .totalCount(0L)
                .build();
        }

        long totalCount = customizedItemCostChangeRequestJpaDao.countQuery(query);

        return ItemCostChangeRequestListDto.builder()
            .data(itemCostChangeRequestDtos)
            .totalCount(totalCount)
            .build();
    }

    private List<ItemCostCollectionDetailDto> fetchItemCostCollection(ItemCostCollectionQuery query) {
        return customizedItemCostCollectionJpaDao.fetchItemCostCollectionDtoList(query);
    }
}
