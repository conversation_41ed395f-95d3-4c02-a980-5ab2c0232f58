package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.application.queryservice.ItemPriceGroupQueryApplicationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/query/item-price-group", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class QueryItemPriceGroupRestApi {

    private final ItemPriceGroupQueryApplicationService itemPriceGroupQueryApplicationService;

    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:items')")
    public List<ItemPriceGroupDto> searchItemPriceGroup (@RequestParam(value = "itemPriceGroupName", required = false) String itemPriceGroupName) {
        log.info("[searchItemPriceGroup] param name: {}.", itemPriceGroupName);
        return itemPriceGroupQueryApplicationService.queryOrFilterItemPriceGroups(itemPriceGroupName);
    }
}
