package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import lombok.*;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentRequestCreatedPayloadDto extends BusinessEventPayloadDto<ItemAdjustmentRequestDto> {

    private UUID itemAdjustmentRequestId;

    @Builder
    public ItemAdjustmentRequestCreatedPayloadDto(ItemAdjustmentRequestDto data, UUID itemAdjustmentRequestId) {
        super(data);
        this.itemAdjustmentRequestId = itemAdjustmentRequestId;
    }
}