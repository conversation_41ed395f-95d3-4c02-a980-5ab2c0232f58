package com.mercaso.ims.domain.itemsyncinfo.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum SyncShopifyStatus {
    SUCCESS,
    FAILURE,
    UNKNOWN,
    ;

    @JsonCreator
    public static SyncShopifyStatus fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equalsIgnoreCase(name)).findFirst().orElse(UNKNOWN);
    }
}
