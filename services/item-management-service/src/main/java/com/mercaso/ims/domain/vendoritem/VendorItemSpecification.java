package com.mercaso.ims.domain.vendoritem;

import com.mercaso.ims.domain.Specification;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceRepository;
import com.mercaso.ims.domain.margin.MarginCalculationService;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class VendorItemSpecification implements Specification<ItemRegPrice> {

    private final ItemRepository itemRepository;
    private final ItemRegPriceRepository itemRegPriceRepository;
    private final MarginCalculationService marginCalculationService;

    public boolean isReasonableMargin(VendorItem vendorItem, BigDecimal newCost) {
        if (vendorItem == null || vendorItem.getItemId() == null) {
            return true;
        }
        if (newCost == null || newCost.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        return isReasonableNewMargin(vendorItem, vendorItem.getPackPlusCrvCost(), newCost);
    }

    public boolean isReasonableBackupMargin(VendorItem vendorItem, BigDecimal newBacnkupCost) {
        if (vendorItem == null || vendorItem.getItemId() == null) {
            return true;
        }
        if (newBacnkupCost == null || newBacnkupCost.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        return isReasonableNewMargin(vendorItem, vendorItem.getBackupPackPlusCrvCost(), newBacnkupCost);
    }

    public boolean isReasonableNewMargin(VendorItem vendorItem, BigDecimal originalCost, BigDecimal newCost) {
        String exception = getExceptionMessage(vendorItem, originalCost, newCost);
        if (exception != null) {
            log.warn("Exception found for SKU :{}", vendorItem != null ? vendorItem.getItemId() : null);
            return false;
        }
        return true;
    }

    public String getExceptionMessage(VendorItem vendorItem, BigDecimal originalCost, BigDecimal newCost) {
        if (vendorItem == null || vendorItem.getItemId() == null) {
            log.error("VendorItem or itemId is null");
            return null;
        }

        Item item = itemRepository.findById(vendorItem.getItemId());
        if (item == null) {
            log.error("Item not found for id :{}", vendorItem.getItemId());
            return null;
        }

        ItemRegPrice price = itemRegPriceRepository.findByItemId(item.getId());
        if (price == null || originalCost == null || newCost == null) {
            log.warn("VendorItem cost or ItemRegPrice not found for SKU :{}", item.getSkuNumber());
            return null;
        }

        // Use the correct vendor ID from the VendorItem being evaluated
        UUID vendorId = vendorItem.getVendorId();
        
        BigDecimal originalMargin = marginCalculationService.calculateMarginWithAutoRebate(
            price.getRegPrice(), originalCost, vendorId, item.getId(), 4);
        BigDecimal newMargin = marginCalculationService.calculateMarginWithAutoRebate(
            price.getRegPrice(), newCost, vendorId, item.getId(), 4);

        BigDecimal marginChange = newMargin.subtract(originalMargin);
        if (marginChange.abs().compareTo(BigDecimal.valueOf(0.2)) > 0) {
            return MessageFormat.format(
                "New margin for SKU {0} is {1}, and the rate of change of the margin is higher than 20%.",
                item.getSkuNumber(), newMargin
            );
        }

        return null;
    }


}
