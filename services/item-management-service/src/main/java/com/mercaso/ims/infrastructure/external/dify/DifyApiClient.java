package com.mercaso.ims.infrastructure.external.dify;

import java.io.*;
import java.util.Objects;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.dify.dto.*;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class DifyApiClient {

  private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

  private final HttpClient client;

  @Value("${dify.api_key}")
  private String apiKey;

  @Value("${dify.base_url}")
  private String baseUrl;

  @Value("${dify.dataset_id}")
  private String datasetId;

  @Value("${dify.document_id}")
  private String documentId;

  @Value("${dify.knowledge_base_api_key}")
  private String knowledgeBaseApiKey;

  private static final String CONTENT_TYPE = "Content-Type";
  private static final String APPLICATION_JSON = "application/json";
  private static final String AUTHORIZATION = "Authorization";
  private static final String BEARER = "Bearer ";


  public DifyWorkflowResult callDifyWorkflow(String key, String value) throws IOException {
    ObjectNode payload = createWorkflowPayload(key, value);
    Request request = buildWorkflowRequest(payload);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      ObjectNode jsonNodes = extractWorkflowResult(response);
      return SerializationUtils.treeToValue(jsonNodes, DifyWorkflowResult.class);
    }
  }

  private ObjectNode createWorkflowPayload(String key, String value) {
    log.info("[createWorkflowPayload] key:{}, value:{}",key, value);
    ObjectNode payload = SerializationUtils.standardObjectMapper().createObjectNode();
    ObjectNode inputs = SerializationUtils.standardObjectMapper().createObjectNode();
    inputs.put(key, value);
    payload.set("inputs", inputs);
    payload.put("response_mode", "streaming");
    payload.put("user", SecurityUtil.getUserName());
    return payload;
  }

  private Request buildWorkflowRequest(ObjectNode payload) throws IOException {
    return new Request.Builder()
        .url(baseUrl + "/workflows/run")
        .addHeader(AUTHORIZATION, BEARER + apiKey)
        .addHeader(CONTENT_TYPE, APPLICATION_JSON)
        .post(RequestBody.create(SerializationUtils.standardObjectMapper().writeValueAsString(payload), JSON))
        .build();
  }

  private void validateResponse(Response response) {
    if (response == null || !response.isSuccessful()) {
      int statusCode = response != null ? response.code() : -1;
      log.error("DifyApiClient call failed with status code {}", statusCode);
      throw new ImsBusinessException(DIFY_API_CALL_FAILED, statusCode);
    }
  }

  private ObjectNode extractWorkflowResult(Response response) throws IOException {
    if (response.body() == null) {
      throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
    }

    WorkflowStreamProcessor processor = new WorkflowStreamProcessor();
    return processor.processStream(response.body().byteStream());
  }


  public DifyDocumentSegmentQueryResponseDto queryDocumentSegmentsByKeyword(String keyword) throws IOException {
    Request request = buildQueryDocumentSegmentationRequest(keyword);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      if (response.body() == null) {
        throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
      }
      String responseBody = response.body().string();
      return SerializationUtils.standardObjectMapper().readValue(responseBody, DifyDocumentSegmentQueryResponseDto.class);
    }
  }


  public DifyDocumentSegmentUpdateResponseDto updateDocumentSegment(String segmentId, DifyDocumentSegmentUpdateRequestBodyDto updateRequest) throws IOException {
    if (!StringUtils.hasText(segmentId)) {
      log.error("[updateDocumentSegment] No segment id provided");
      throw new ImsBusinessException(DIFY_SEGMENT_ID_IS_NULL);
    }
    Request request = buildUpdateDocumentSegmentRequest(segmentId, updateRequest);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      if (response.body() == null) {
        throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
      }
      String responseBody = response.body().string();
      return SerializationUtils.standardObjectMapper().readValue(responseBody, DifyDocumentSegmentUpdateResponseDto.class);
    }
  }


  public DifyDocumentSegmentQueryResponseDto createDocumentSegment(DifyDocumentSegmentCreateRequestBodyDto createRequest) throws IOException {
    Request request = buildCreateDocumentSegmentRequest(createRequest);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      if (response.body() == null) {
        throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
      }
      String responseBody = response.body().string();
      return SerializationUtils.standardObjectMapper().readValue(responseBody, DifyDocumentSegmentQueryResponseDto.class);
    }
  }


  private Request buildQueryDocumentSegmentationRequest(String keyword) {

    HttpUrl url = Objects.requireNonNull(HttpUrl.parse(baseUrl))
            .newBuilder()
            .addPathSegments(buildRequestUrl(null))
            .addQueryParameter("keyword", keyword)
            .build();

    return new Request.Builder()
            .url(url)
            .addHeader(AUTHORIZATION, BEARER + knowledgeBaseApiKey)
            .addHeader(CONTENT_TYPE, APPLICATION_JSON)
            .get()
            .build();
  }

  private Request buildUpdateDocumentSegmentRequest(String segmentId, DifyDocumentSegmentUpdateRequestBodyDto updateRequest) throws IOException {
    HttpUrl url = Objects.requireNonNull(HttpUrl.parse(baseUrl))
            .newBuilder()
            .addPathSegments(buildRequestUrl(segmentId))
            .build();

    String requestBody = SerializationUtils.standardObjectMapper().writeValueAsString(updateRequest);

    return new Request.Builder()
            .url(url)
            .addHeader(AUTHORIZATION, BEARER + knowledgeBaseApiKey)
            .addHeader(CONTENT_TYPE, APPLICATION_JSON)
            .post(RequestBody.create(requestBody, JSON))
            .build();
  }

  private Request buildCreateDocumentSegmentRequest(DifyDocumentSegmentCreateRequestBodyDto createRequest) throws IOException {
    HttpUrl url = Objects.requireNonNull(HttpUrl.parse(baseUrl))
            .newBuilder()
            .addPathSegments(buildRequestUrl(null))
            .build();

    String requestBody = SerializationUtils.standardObjectMapper().writeValueAsString(createRequest);

    log.info("[buildCreateDocumentSegmentRequest] requestBody: {}", requestBody);

    return new Request.Builder()
            .url(url)
            .addHeader(AUTHORIZATION, BEARER + knowledgeBaseApiKey)
            .addHeader(CONTENT_TYPE, APPLICATION_JSON)
            .post(RequestBody.create(requestBody, JSON))
            .build();
  }

  private String buildRequestUrl (String segmentId) {
    String url = "datasets/" + datasetId + "/documents/" + documentId + "/segments";
    if (StringUtils.hasText(segmentId)) {
      url += "/" + segmentId;
    }
    return url;
  }
}
