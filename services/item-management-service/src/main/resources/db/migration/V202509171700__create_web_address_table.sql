-- Create web_address table
CREATE TABLE web_address
(
    id                UUID PRIMARY KEY      DEFAULT gen_random_uuid(),
    entity_type       VARCHAR(100) NOT NULL,
    entity_id         UUID         NOT NULL,
    web_address_type  VARCHAR(50)  NOT NULL,
    web_address       VARCHAR(500) NOT NULL,
    created_at        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by        <PERSON><PERSON><PERSON><PERSON>(100),
    created_user_name VARCHAR(100),
    updated_at        TIMESTAMP,
    updated_by        VA<PERSON>HAR(100),
    updated_user_name VARCHAR(100),
    deleted_at        TIMESTAMP,
    deleted_by        <PERSON><PERSON><PERSON><PERSON>(100),
    deleted_user_name VARCHAR(100)
);

-- Add comments to table
COMMENT ON TABLE web_address IS 'Web address information for various entities';

-- Add comments to columns
COMMENT ON COLUMN web_address.id IS 'Primary key for web address';
COMMENT ON COLUMN web_address.entity_type IS 'Entity type (e.g., Vendor, Customer, etc.)';
COMMENT ON COLUMN web_address.entity_id IS 'Entity ID reference';
COMMENT ON COLUMN web_address.web_address_type IS 'Type of web address (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ORTAL, ECOMMERCE, SUPPORT, DOCUMENTATION, API, SOCIAL_MEDIA, BLOG, NEWS, OTHER, UNKNOWN)';
COMMENT ON COLUMN web_address.web_address IS 'The actual web address URL';
COMMENT ON COLUMN web_address.created_at IS 'Creation timestamp';
COMMENT ON COLUMN web_address.created_by IS 'User who created the record';
COMMENT ON COLUMN web_address.created_user_name IS 'Name of user who created the record';
COMMENT ON COLUMN web_address.updated_at IS 'Last update timestamp';
COMMENT ON COLUMN web_address.updated_by IS 'User who last updated the record';
COMMENT ON COLUMN web_address.updated_user_name IS 'Name of user who last updated the record';
COMMENT ON COLUMN web_address.deleted_at IS 'Soft delete timestamp';
COMMENT ON COLUMN web_address.deleted_by IS 'User who deleted the record';
COMMENT ON COLUMN web_address.deleted_user_name IS 'Name of user who deleted the record';

-- Create indexes for better query performance
CREATE INDEX idx_web_address_entity_type_entity_id ON web_address (entity_type, entity_id);
CREATE INDEX idx_web_address_web_address_type ON web_address (web_address_type);
CREATE INDEX idx_web_address_web_address ON web_address (web_address);
CREATE INDEX idx_web_address_deleted_at ON web_address (deleted_at);
CREATE UNIQUE INDEX idx_web_address_entity_type_entity_id_web_address_type ON web_address (entity_type, entity_id, web_address_type) WHERE deleted_at IS NULL;

ALTER TABLE address
    DROP CONSTRAINT IF EXISTS chk_address_purpose_valid;