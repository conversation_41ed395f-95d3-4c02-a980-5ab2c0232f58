package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.DeleteItemVendorRebateRequestData;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeleteItemVendorRebateRequestDataListenerTest {

    private static final UUID REQUEST_ID = UUID.randomUUID();
    private static final String SKU = "12345";
    private static final String VENDOR_NAME = "Test Vendor";

    @Mock
    private ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;

    @Mock
    private ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private VendorRepository vendorRepository;

    @Mock
    private VendorItemRepository vendorItemRepository;

    @Mock
    private CategoryApplicationService categoryApplicationService;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @Mock
    private BrandRepository brandRepository;

    @Mock
    private ItemVendorRebateService itemVendorRebateService;

    @InjectMocks
    private DeleteItemVendorRebateRequestDataListener listener;

    private DeleteItemVendorRebateRequestData data;

    @BeforeEach
    void setUp() {
        listener = new DeleteItemVendorRebateRequestDataListener(
            REQUEST_ID,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager,
            brandRepository,
            itemVendorRebateService
        );

        data = new DeleteItemVendorRebateRequestData();
        data.setSku(SKU);
        data.setVendor(VENDOR_NAME);
    }

    @Test
    void testGetItemAdjustmentType() {
        assertEquals(ItemAdjustmentType.DELETE_REBATE, listener.getItemAdjustmentType());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommand() {
        CreateItemAdjustmentRequestDetailCommand command = listener.convertToCreateItemAdjustmentRequestDetailCommand(data);

        assertNotNull(command);
        assertEquals(REQUEST_ID, command.getRequestId());
        assertEquals(ItemAdjustmentType.DELETE_REBATE, command.getType());
        assertEquals(SKU, command.getSku());
        assertEquals(VENDOR_NAME, command.getVendor());
        assertNull(command.getRebateStartDate());
        assertNull(command.getRebateEndDate());
        assertNull(command.getRebatePerSellingUnit());
    }

    @Test
    void testValidateInput_ValidData() {
        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);
        ItemVendorRebate existingRebate = mock(ItemVendorRebate.class);
        UUID itemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();

        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(item.getId()).thenReturn(itemId);
        when(vendor.getId()).thenReturn(vendorId);
        when(vendorItemRepository.findByVendorIDAndItemId(vendorId, itemId)).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(true);
        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId)).thenReturn(Collections.singletonList(existingRebate));

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertTrue(reasons.isEmpty());
    }

    @Test
    void testValidateInput_SkuRequired() {
        data.setSku("");

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.SKU_IS_REQUIRED, reasons.get(0));
    }

    @Test
    void testValidateInput_ItemNotFound() {
        when(itemRepository.findBySku(SKU)).thenReturn(null);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.ITEM_NOT_FOUND, reasons.get(0));
    }

    @Test
    void testValidateInput_VendorNameRequired() {
        data.setVendor("");

        Item item = mock(Item.class);
        when(itemRepository.findBySku(SKU)).thenReturn(item);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.VENDOR_NAME_IS_REQUIRED, reasons.get(0));
    }

    @Test
    void testValidateInput_VendorNotFound() {
        Item item = mock(Item.class);
        
        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(null);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.VENDOR_NOT_FOUND, reasons.get(0));
    }

    @Test
    void testValidateInput_VendorItemNotFound() {
        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);

        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(item.getId()).thenReturn(UUID.randomUUID());
        when(vendor.getId()).thenReturn(UUID.randomUUID());
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(null);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertTrue(reasons.contains(ItemAdjustmentFailureReason.VENDOR_ITEM_NOT_FOUND));
    }

    @Test
    void testValidateInput_VendorItemNotDirect() {
        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);

        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(item.getId()).thenReturn(UUID.randomUUID());
        when(vendor.getId()).thenReturn(UUID.randomUUID());
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(false);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertTrue(reasons.contains(ItemAdjustmentFailureReason.SUPPLIER_MUST_BE_DIRECT));
    }

    @Test
    void testValidateInput_VendorTrimsWhitespace() {
        data.setVendor("  " + VENDOR_NAME + "  ");

        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);
        ItemVendorRebate existingRebate = mock(ItemVendorRebate.class);
        UUID itemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();

        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor); // Should be called with trimmed name
        when(item.getId()).thenReturn(itemId);
        when(vendor.getId()).thenReturn(vendorId);
        when(vendorItemRepository.findByVendorIDAndItemId(vendorId, itemId)).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(true);
        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId)).thenReturn(Collections.singletonList(existingRebate));

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertTrue(reasons.isEmpty());
        verify(vendorRepository, times(1)).findByVendorName(VENDOR_NAME); // Verify trimmed name was used
    }

    @Test
    void testValidateInput_NullVendor() {
        data.setVendor(null);

        Item item = mock(Item.class);
        when(itemRepository.findBySku(SKU)).thenReturn(item);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.VENDOR_NAME_IS_REQUIRED, reasons.get(0));
    }

    @Test
    void testValidateInput_RebateNotFound() {
        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);
        UUID itemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();

        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(item.getId()).thenReturn(itemId);
        when(vendor.getId()).thenReturn(vendorId);
        when(vendorItemRepository.findByVendorIDAndItemId(vendorId, itemId)).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(true);
        when(itemVendorRebateService.findByVendorIdAndItemId(vendorId, itemId)).thenReturn(Collections.emptyList());

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertTrue(reasons.contains(ItemAdjustmentFailureReason.REBATE_NOT_FOUND));
    }
}
