package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateItemVendorRebateRequestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreateItemVendorRebateRequestDataListenerTest {

    private static final UUID REQUEST_ID = UUID.randomUUID();
    private static final String SKU = "12345";
    private static final String VENDOR_NAME = "Test Vendor";
    private static final String START_DATE = "2023-01-01";
    private static final String END_DATE = "2023-12-31";
    private static final String REBATE_PER_SELLING_UNIT = "10.00";

    @Mock
    private ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;

    @Mock
    private ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private VendorRepository vendorRepository;

    @Mock
    private VendorItemRepository vendorItemRepository;

    @Mock
    private CategoryApplicationService categoryApplicationService;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @Mock
    private BrandRepository brandRepository;

    @Mock
    private ItemVendorRebateService itemVendorRebateService;

    @InjectMocks
    private CreateItemVendorRebateRequestDataListener listener;

    private CreateItemVendorRebateRequestData data;

    @BeforeEach
    void setUp() {
        listener = new CreateItemVendorRebateRequestDataListener(
            REQUEST_ID,
            "UTC",
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager,
            brandRepository,
            itemVendorRebateService
        );

        data = new CreateItemVendorRebateRequestData();
        data.setSku(SKU);
        data.setVendor(VENDOR_NAME);
        data.setStartDate(START_DATE);
        data.setEndDate(END_DATE);
        data.setRebatePerSellingUnit(REBATE_PER_SELLING_UNIT);
    }

    @Test
    void testGetItemAdjustmentType() {
        assertEquals(ItemAdjustmentType.CREATE_REBATE, listener.getItemAdjustmentType());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommand() {
        CreateItemAdjustmentRequestDetailCommand command = listener.convertToCreateItemAdjustmentRequestDetailCommand(data);

        assertNotNull(command);
        assertEquals(REQUEST_ID, command.getRequestId());
        assertEquals(ItemAdjustmentType.CREATE_REBATE, command.getType());
        assertEquals(SKU, command.getSku());
        assertEquals(VENDOR_NAME, command.getVendor());
        assertEquals(START_DATE, command.getRebateStartDate());
        assertEquals(END_DATE, command.getRebateEndDate());
        assertEquals(REBATE_PER_SELLING_UNIT, command.getRebatePerSellingUnit());
    }

    @Test
    void testValidateInput_ItemNotFound() {
        when(itemRepository.findBySku(SKU)).thenReturn(null);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.ITEM_NOT_FOUND, reasons.get(0));
    }

    @Test
    void testValidateInput_VendorNotFound() {
        Item item = mock(Item.class);
        
        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(null);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.VENDOR_NOT_FOUND, reasons.getFirst());
    }

    @Test
    void testValidateInput_StartDateRequired() {
        data.setStartDate("");

        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);

        when(item.getId()).thenReturn(UUID.randomUUID());
        when(vendor.getId()).thenReturn(UUID.randomUUID());
        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(true);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertTrue(reasons.contains(ItemAdjustmentFailureReason.REBATE_START_DATE_IS_REQUIRED));
    }

    @Test
    void testValidateInput_RebatePerSellingUnitRequired() {
        data.setRebatePerSellingUnit("");

        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);

        when(item.getId()).thenReturn(UUID.randomUUID());
        when(vendor.getId()).thenReturn(UUID.randomUUID());
        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(true);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertTrue(reasons.contains(ItemAdjustmentFailureReason.REBATE_PER_SELLING_UNIT_IS_REQUIRED));
    }

    @Test
    void testValidateInput_VendorItemNotFound() {
        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);

        when(item.getId()).thenReturn(UUID.randomUUID());
        when(vendor.getId()).thenReturn(UUID.randomUUID());
        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(null);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.VENDOR_ITEM_NOT_FOUND, reasons.getFirst());
    }

    @Test
    void testValidateInput_SupplierMustBeDirect() {
        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);

        when(item.getId()).thenReturn(UUID.randomUUID());
        when(vendor.getId()).thenReturn(UUID.randomUUID());
        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(false);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertFalse(reasons.isEmpty());
        assertEquals(ItemAdjustmentFailureReason.SUPPLIER_MUST_BE_DIRECT, reasons.getFirst());
    }

    @Test
    void testValidateInput_SuccessfulValidation() {
        Item item = mock(Item.class);
        Vendor vendor = mock(Vendor.class);
        VendorItem vendorItem = mock(VendorItem.class);

        when(item.getId()).thenReturn(UUID.randomUUID());
        when(vendor.getId()).thenReturn(UUID.randomUUID());
        when(itemRepository.findBySku(SKU)).thenReturn(item);
        when(vendorRepository.findByVendorName(VENDOR_NAME)).thenReturn(vendor);
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(vendorItem.isDirectVendorItem()).thenReturn(true);

        List<ItemAdjustmentFailureReason> reasons = listener.validateInput(data);

        assertTrue(reasons.isEmpty());
    }
}
