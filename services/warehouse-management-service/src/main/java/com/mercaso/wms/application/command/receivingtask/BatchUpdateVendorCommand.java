package com.mercaso.wms.application.command.receivingtask;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.batch.enums.SourceEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class BatchUpdateVendorCommand extends BaseCommand {

    @NotEmpty(message = "Receiving task IDs cannot be empty")
    private List<UUID> receivingTaskIds;

    @NotNull(message = "Vendor cannot be null")
    private SourceEnum vendor;
}