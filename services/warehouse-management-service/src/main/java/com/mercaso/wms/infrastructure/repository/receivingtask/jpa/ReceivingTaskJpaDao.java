package com.mercaso.wms.infrastructure.repository.receivingtask.jpa;

import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.dataobject.ReceivingTaskDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ReceivingTaskJpaDao extends JpaRepository<ReceivingTaskDo, UUID> {

    List<ReceivingTaskDo> findByBatchId(UUID id);

    @Query("SELECT rt FROM ReceivingTaskDo rt " +
           "LEFT JOIN BatchDo b ON rt.batchId = b.id " +
           "WHERE b.tag = :deliveryDate " +
           "AND rt.status = :status")
    List<ReceivingTaskDo> findByDeliveryDateAndStatus(@Param("deliveryDate") String deliveryDate,
                                                     @Param("status") ReceivingTaskStatus status);

    List<ReceivingTaskDo> findByBatchIdAndStatusNotIn(@Param("batchId") UUID batchId,
        @Param("statuses") List<ReceivingTaskStatus> statuses);

    List<ReceivingTaskDo> findByIdIn(List<UUID> ids);
}
