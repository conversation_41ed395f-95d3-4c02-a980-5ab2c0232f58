package com.mercaso.wms.infrastructure.repository.consolidationlocation;

import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ConsolidationLocationSyncLogJpaDao extends JpaRepository<ConsolidationLocationSyncLog, UUID> {

    @Query(
        "select clsl from ConsolidationLocationSyncLog clsl where (:deliveryDate is null or clsl.deliveryDate = :deliveryDate) and (:createdUserName is null or clsl.createdUserName = :createdUserName) order by clsl.createdAt desc")
    Page<ConsolidationLocationSyncLog> searchBy(@Param("deliveryDate") String deliveryDate,
        @Param("createdUserName") String createdUserName,
        Pageable pageable);

}
