package com.mercaso.wms.infrastructure.external.finale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceivePurchaseOrderResponse {
    private String shipmentId;
    private String shipmentIdUser;
    private String shipmentUrl;
    private String shipmentTypeId;
    private String actionUrlCancel;
    private String actionUrlReceive;
    private String primaryOrderUrl;
    private String statusId;
    private List<ShipmentItem> shipmentItemList;
    private List<StatusHistory> statusIdHistoryList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShipmentItem {
        private String facilityUrl;
        private String productId;
        private String productUrl;
        private Integer quantity;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusHistory {
        private String statusId;
        private Long txStamp;
        private String userLoginUrl;
    }
}