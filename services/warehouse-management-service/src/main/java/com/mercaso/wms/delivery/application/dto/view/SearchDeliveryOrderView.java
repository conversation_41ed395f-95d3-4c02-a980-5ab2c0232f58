package com.mercaso.wms.delivery.application.dto.view;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.AddressDto;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchDeliveryOrderView extends BaseDto {

    private UUID id;

    private UUID deliveryTaskId;

    private String deliveryTaskNumber;

    private String orderNumber;

    private String deliveryDate;

    private String deliveryTimeWindow;

    private String status;

    private String driverUserName;

    private BigDecimal currentQty;

    private BigDecimal deliveredQty;

    private String truckNumber;

    private String paymentType;

    private String paymentStatus;

    private String fulfillmentStatus;

    private Integer sequence;

    private Instant planArriveAt;

    private Instant planDeliveryAt;

    private Instant arrivedAt;

    private Instant unloadedAt;

    private Instant deliveredAt;

    private String customerNotes;

    private String notes;

    private AddressDto address;

    private String rescheduleType;

    private BigDecimal originalTotalPrice;

    private BigDecimal totalPrice;
}
