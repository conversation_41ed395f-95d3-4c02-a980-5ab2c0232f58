package com.mercaso.wms.delivery.application.command;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class DeliveryUploadDocumentCommand extends BaseCommand {

    @NotNull
    private UUID entityId;
    @NotNull
    private EntityEnums entityName;
    @NotNull
    private DeliveryDocumentType documentType;

    private List<String> clientFileIds;
}
