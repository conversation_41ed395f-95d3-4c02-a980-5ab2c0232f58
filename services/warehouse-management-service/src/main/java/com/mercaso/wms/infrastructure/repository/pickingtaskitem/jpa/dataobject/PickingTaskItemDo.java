package com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.dataobject;

import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.dataobject.PickingTaskDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Table(name = "picking_task_items")
@SQLDelete(sql = "update picking_task_items set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class PickingTaskItemDo extends BaseDo {

    @ManyToOne
    @JoinColumn(name = "picking_task_id", nullable = false)
    private PickingTaskDo pickingTask;

    @Column(name = "batch_item_id", nullable = false)
    private UUID batchItemId;

    @Column(name = "order_number")
    private String orderNumber;

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "department")
    private String department;

    @Column(name = "category")
    private String category;

    @Column(name = "sku_number", length = 20)
    private String skuNumber;

    @Column(name = "title")
    private String title;

    @Column(name = "location_id")
    private UUID locationId;

    @Column(name = "location_name", length = 50)
    private String locationName;

    @Column(name = "aisle_number", length = 50)
    private String aisleNumber;

    @Column(name = "prep", length = 200)
    private String prep;

    @Column(name = "picking_sequence")
    private Integer pickingSequence;

    @Column(name = "expect_qty")
    private Integer expectQty;

    @Column(name = "picked_qty")
    private Integer pickedQty;

    @Column(name = "error_info")
    private String errorInfo;

    @Column(name = "breakdown_name", length = 20)
    private String breakdownName;

    @Column(name = "line")
    private Integer line;

    @Column(name = "shipping_order_id")
    private UUID shippingOrderId;

    @Column(name = "shipping_order_item_id")
    private UUID shippingOrderItemId;

    @Column(name = "picked_at")
    private Instant pickedAt;

}