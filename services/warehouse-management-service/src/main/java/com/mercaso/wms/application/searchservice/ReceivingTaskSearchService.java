package com.mercaso.wms.application.searchservice;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.mercaso.wms.application.dto.ReceivedItemsDto;
import com.mercaso.wms.application.dto.view.SearchReceivingTaskView;
import com.mercaso.wms.application.query.ReceivingTaskQuery;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.ReceivingTaskJdbcTemplate;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class ReceivingTaskSearchService {

    private final ReceivingTaskRepository receivingTaskRepository;

    private final ReceivingTaskJdbcTemplate receivingTaskJdbcTemplate;

    public Page<SearchReceivingTaskView> search(ReceivingTaskQuery receivingTaskQuery, Pageable pageable) {
        return receivingTaskRepository.search(receivingTaskQuery, pageable);
    }

    public ByteArrayOutputStream receivedItemsExport(String deliveryDate) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            List<ReceivedItemsDto> receivedItemsDtos = receivingTaskJdbcTemplate.fetchReceivedItemsByDeliveryDate(deliveryDate);

            return writeTemplate(outputStream, receivedItemsDtos);
        } catch (Exception e) {
            log.error("Failed to export picked items.", e);
        }
        return writeTemplate(new ByteArrayOutputStream(), List.of());

    }

    public ByteArrayOutputStream writeTemplate(ByteArrayOutputStream outputStream,
        List<ReceivedItemsDto> receivedItemsDtos) {
        Resource resource = new ClassPathResource("template/Received_Items_List_Template.xlsx");
        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream)
            .withTemplate(resource.getInputStream())
            .build()) {
            excelWriter.fill(receivedItemsDtos, EasyExcelFactory.writerSheet().sheetName("Received Items").build());
        } catch (IOException e) {
            throw new WmsBusinessException("Failed to write received items.", e);
        }
        return outputStream;
    }



}