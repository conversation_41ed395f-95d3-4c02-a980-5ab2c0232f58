package com.mercaso.wms.infrastructure.config;

import com.mercaso.user.client.ApiClient;
import com.mercaso.user.client.api.RolesApi;
import com.mercaso.user.client.api.UsersApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class UmsConfig {

    @Value("${mercaso.ums-url}")
    private String umsHost;

    @Bean
    public UsersApi usersApi(RestTemplate restTemplate) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(umsHost);
        return new UsersApi(apiClient);
    }

    @Bean
    public RolesApi rolesApi(RestTemplate restTemplate) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(umsHost);
        return new RolesApi(apiClient);
    }

}
