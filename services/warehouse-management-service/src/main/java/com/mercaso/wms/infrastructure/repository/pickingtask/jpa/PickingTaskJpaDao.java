package com.mercaso.wms.infrastructure.repository.pickingtask.jpa;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.criteria.PickingTaskSearchCriteria;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.dataobject.PickingTaskDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface PickingTaskJpaDao extends JpaRepository<PickingTaskDo, UUID> {

    List<PickingTaskDo> findByBatchId(UUID batchId);

    List<PickingTaskDo> findByBatchIdAndTypeAndSource(UUID batchId, PickingTaskType type, SourceEnum source);

    @Query("select distinct pt from PickingTaskDo pt "
        + "left join fetch pt.pickingTaskItems pti "
        + "where (:#{#criteria.pickerUserId} is null or pt.pickerUserId = :#{#criteria.pickerUserId}) "
        + "and (:#{#criteria.statuses} is null or pt.status IN :#{#criteria.statuses}) "
        + "and (:#{#criteria.type} is null or pt.type = :#{#criteria.type}) "
        + "and (:#{#criteria.source} is null or pt.source = :#{#criteria.source}) "
        + "and (:#{#criteria.numbers} is null or pt.number IN :#{#criteria.numbers}) "
        + "and (:#{#criteria.deliveryDate} is null or exists ("
        + " select 1 from BatchDo bd where pt.batchId = bd.id and bd.tag = :#{#criteria.deliveryDate})) "
        + "and (:#{#criteria.orderNumbers} is null or exists ("
        + "  select 1 from PickingTaskItemDo pti where pti.pickingTask.id = pt.id and pti.orderNumber IN :#{#criteria.orderNumbers})) "
        + "and (:#{#criteria.departments} is null or exists ("
        + "  select 1 from PickingTaskItemDo pti where pti.pickingTask.id = pt.id and pti.department IN :#{#criteria.departments}))"
        + "and (:#{#criteria.categories} is null or exists ("
        + "  select 1 from PickingTaskItemDo pti where pti.pickingTask.id = pt.id and pti.category IN :#{#criteria.categories}))"
        + "and (:#{#criteria.breakdownName} is null or exists ("
        + "  select 1 from PickingTaskItemDo pti where pti.pickingTask.id = pt.id and pti.breakdownName = :#{#criteria.breakdownName}))"
        + "and (:#{#criteria.aisleNumbers} is null or exists ("
        + "  select 1 from PickingTaskItemDo pti where pti.pickingTask.id = pt.id and pti.aisleNumber IN :#{#criteria.aisleNumbers}))"
        + "and (:#{#criteria.skuNumbers} is null or exists ("
        + "  select 1 from PickingTaskItemDo pti where pti.pickingTask.id = pt.id and pti.skuNumber IN :#{#criteria.skuNumbers}))"
        + "and (:#{#criteria.isSingleItemTask} is null or :#{#criteria.isSingleItemTask} = false or "
        + "  (:#{#criteria.isSingleItemTask} = true and not exists ("
        + "    select 1 from PickingTaskItemDo pti2 where pti2.pickingTask.id = pt.id and pti2.expectQty != 1)))")
    Page<PickingTaskDo> findPickingTaskList(@Param("criteria") PickingTaskSearchCriteria criteria, Pageable pageable);

    @Query(
        "select distinct pt from PickingTaskDo pt left join fetch pt.pickingTaskItems pti where pt.status != 'CANCELED' and pti.orderNumber IN :orderNumbers")
    List<PickingTaskDo> findByOrderNumbers(@Param("orderNumbers") List<String> orderNumbers);

    List<PickingTaskDo> findByBatchIdAndStatusNotIn(@Param("batchId") UUID batchId,
        @Param("statuses") List<PickingTaskStatus> statuses);
}