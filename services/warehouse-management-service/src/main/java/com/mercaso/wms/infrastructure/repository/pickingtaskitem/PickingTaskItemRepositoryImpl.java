package com.mercaso.wms.infrastructure.repository.pickingtaskitem;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.PickingTaskItemJpaDao;
import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.dataobject.PickingTaskItemDo;
import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.mapper.PickingTaskItemDoMapper;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class PickingTaskItemRepositoryImpl implements PickingTaskItemRepository {

    public final PickingTaskItemDoMapper mapper;
    private final PickingTaskItemJpaDao jpaDao;

    @Override
    public PickingTaskItem save(PickingTaskItem domain) {
        PickingTaskItemDo save = jpaDao.save(mapper.domainToDo(domain));
        return mapper.doToDomain(save);
    }

    @Override
    public PickingTaskItem findById(UUID id) {
        Optional<PickingTaskItemDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public PickingTaskItem update(PickingTaskItem domain) {
        PickingTaskItemDo pickingTaskItemsDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == pickingTaskItemsDo) {
            throw new WmsBusinessException("PickingTaskItems not found.");
        }
        PickingTaskItemDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(target, pickingTaskItemsDo, ignoreProperties.toArray(new String[0]));
        pickingTaskItemsDo = jpaDao.save(pickingTaskItemsDo);
        return mapper.doToDomain(pickingTaskItemsDo);
    }

    @Override
    public List<PickingTaskItem> findFiledPickingTaskItemBy(String deliveryDate, List<String> pickingTaskNumbers) {
        return mapper.doToDomains(jpaDao.findBy(deliveryDate, pickingTaskNumbers));
    }

    @Override
    public void deleteAll() {
        jpaDao.deleteAll();
    }

    @Override
    public List<PickingTaskItem> findByIds(List<UUID> pickingTaskItemIds) {
        return mapper.doToDomains(jpaDao.findAllById(pickingTaskItemIds));
    }

    @Override
    public void deleteByIds(List<UUID> pickingTaskItemIds) {
        List<PickingTaskItemDo> allById = jpaDao.findAllById(pickingTaskItemIds);
        if (allById.isEmpty()) {
            log.warn("No PickingTaskItems found for the provided IDs: {}", pickingTaskItemIds);
            return;
        }
        allById.forEach(pickingTaskItemDo -> {
            pickingTaskItemDo.setDeletedAt(Instant.now());
            pickingTaskItemDo.setDeletedBy(SecurityContextUtil.getLoginUserId());
        });
        jpaDao.saveAll(allById);
    }

    @Override
    public Integer getFailedQtyByShippingOrderItemId(UUID shippingOrderItemId) {
        return jpaDao.getFailedQtyByShippingOrderItemId(shippingOrderItemId);
    }

    @Override
    public List<PickingTaskItem> findValidItemsByShippingOrderItemId(UUID shippingOrderItemId) {
        return mapper.doToDomains(jpaDao.findValidItemsByShippingOrderItemId(shippingOrderItemId));
    }
}
