package com.mercaso.wms.application.service.pickingtask;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class CreatePickingTaskService {

    protected final BatchItemRepository batchItemRepository;
    protected final BatchItemQueryService batchItemQueryService;
    protected final PickingTaskRepository pickingTaskRepository;
    protected final LocationRepository locationRepository;
    protected final PickingTaskAssignmentConfig pickingTaskAssignmentConfig;
    protected final PickingTaskApplicationService pickingTaskApplicationService;
    protected final LocationCache locationCache;

    protected CreatePickingTaskService(
        BatchItemRepository batchItemRepository,
        BatchItemQueryService batchItemQueryService,
        PickingTaskRepository pickingTaskRepository,
        LocationRepository locationRepository,
        PickingTaskAssignmentConfig pickingTaskAssignmentConfig,
        PickingTaskApplicationService pickingTaskApplicationService,
        LocationCache locationCache) {
        this.batchItemRepository = batchItemRepository;
        this.batchItemQueryService = batchItemQueryService;
        this.pickingTaskRepository = pickingTaskRepository;
        this.locationRepository = locationRepository;
        this.pickingTaskAssignmentConfig = pickingTaskAssignmentConfig;
        this.pickingTaskApplicationService = pickingTaskApplicationService;
        this.locationCache = locationCache;
    }

    protected void removeNAAndCoolerDataFromMFCAndMDC(List<BatchItem> batchItems) {
        batchItems.removeIf(batchItem ->
            (batchItem.getSource().equals(SourceEnum.MDC.name()) || batchItem.getSource().equals(SourceEnum.MFC.name()))
                && (batchItem.getLocationName().equals("N/A") || batchItem.getLocationName().equals(".COOLER")
                || batchItem.getLocationName().equals("YATES") || batchItem.getLocationName().equals("PHOTO-STUDIO")));
    }

    protected List<BatchItem> filterLocationBatchItemsByLocationName(List<BatchItem> batchItems, String locationName) {
        return batchItems.stream()
            .filter(batchItem -> batchItem.getLocationName().contains(locationName))
            .sorted(Comparator.comparing(BatchItem::getLocationName, Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER))
                .thenComparing(BatchItem::getTitle, Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER)))
            .toList();
    }

    protected static List<List<BatchItem>> splitBatchItems(List<BatchItem> batchItems, int targetSum) {
        List<List<BatchItem>> result = new ArrayList<>();
        List<BatchItem> currentBatch = new ArrayList<>();
        int currentSum = 0;
        String previousSkuNumber = null;

        for (BatchItem item : batchItems) {
            int qty = item.getExpectQty();
            if (Objects.equals(item.getSkuNumber(), previousSkuNumber)) {
                currentBatch.add(item);
                currentSum += qty;
                continue;
            }

            if (currentSum + qty > targetSum) {
                result.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
                currentSum = 0;
            }
            currentBatch.add(item);
            currentSum += qty;
            previousSkuNumber = item.getSkuNumber();
        }
        if (!currentBatch.isEmpty()) {
            result.add(currentBatch);
        }
        return result;
    }


    public abstract List<PickingTask> createPickingTask(UUID batchId);

}
