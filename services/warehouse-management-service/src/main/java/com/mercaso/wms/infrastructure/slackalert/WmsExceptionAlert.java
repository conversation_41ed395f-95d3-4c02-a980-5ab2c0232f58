package com.mercaso.wms.infrastructure.slackalert;

import com.mercaso.wms.application.service.SlackService;
import com.mercaso.wms.infrastructure.alert.dto.WmsExceptionAlertDto;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class WmsExceptionAlert {

    private static final String ADMIN_BASE_URL_TEMPLATE = "%s/shipping-orders?deliveryDate=&orderNumber=%s";

    private final SlackService slackService;

    @Value("${mercaso.wms-portal-base-url}")
    private String wmsAdminPortalBaseUrl;

    public void sendWmsExceptionAlert(WmsExceptionAlertDto alertDto) {
        if (alertDto == null) {
            log.warn("No alert data provided for WMS exception alert");
            return;
        }

        log.info("Sending WMS exception alert: eventType={}, entityId={}, severity={}",
            alertDto.getAlertEventType(), alertDto.getEntityId(), alertDto.getSeverity());

        try {
            String message = buildSlackMessage(alertDto);
            log.info("Built Slack message for WMS exception alert: length={}", message.length());

            boolean success = slackService.sendWmsExceptionAlert(message);

            if (success) {
                log.info("WMS exception alert sent to Slack successfully for type: {}, entity: {}",
                    alertDto.getAlertEventType(), alertDto.getEntityId());
            } else {
                log.warn("Failed to send WMS exception alert to Slack for type: {}",
                    alertDto.getAlertEventType());
            }
        } catch (Exception e) {
            log.error("[sendWmsExceptionAlert] Failed to send WMS exception alert to Slack", e);
        }
    }

    private String buildSlackMessage(WmsExceptionAlertDto alertDto) {
        return String.join("\n",
            buildHeader(alertDto),
            "",
            buildExceptionDetails(alertDto),
            "",
            buildContextInfo(alertDto),
            "",
            ":pushpin: *Action Required:* " + alertDto.getActionRequired()
        );
    }

    private String buildHeader(WmsExceptionAlertDto alertDto) {
        return String.format(":white_medium_small_square:\n%s *%s* %s",
            alertDto.getSeverity().getEmoji(),
            alertDto.getTitle(),
            alertDto.getSeverity().getEmoji());
    }

    private String buildExceptionDetails(WmsExceptionAlertDto alertDto) {
        StringBuilder details = new StringBuilder();
        details.append("🔍 *Issue Details:*\n");

        if (alertDto.getDescription() != null && !alertDto.getDescription().isEmpty()) {
            details.append(String.format(" %s\n", alertDto.getDescription()));
        }

        return details.toString().trim();
    }

    private String buildContextInfo(WmsExceptionAlertDto alertDto) {
        StringBuilder context = new StringBuilder();
        context.append("📋 *Context Information:*\n");

        Map<String, Object> contextData = alertDto.getContextData();
        if (contextData != null && !contextData.isEmpty()) {

            // Special handling for different alert types
            if (alertDto.getAlertEventType() == WmsExceptionAlertDto.AlertEventType.PICKING_TASKS_INCOMPLETE_AFTER_8PM) {
                buildPickingTasksIncompleteContext(context, contextData);
            } else if (alertDto.getAlertEventType() == WmsExceptionAlertDto.AlertEventType.GHOST_INVENTORY_CLEANUP_FAILED) {
                buildGhostInventoryCleanupContext(context, contextData);
            } else {
                // Generic handling for other alert types
                buildGenericContext(context, contextData, alertDto);
            }
        }

        // Add entity information
        if (alertDto.getEntityType() != null) {
            context.append(String.format("• *Entity Type:* %s\n",
                alertDto.getEntityType().getValue()));
        }

        return context.toString().trim();
    }

    /**
     * Builds context for picking tasks incomplete alert with special formatting
     */
    private void buildPickingTasksIncompleteContext(StringBuilder context, Map<String, Object> contextData) {
        // Basic information
        if (contextData.containsKey("vendorName")) {
            context.append(String.format("• *Vendor:* %s\n", contextData.get("vendorName")));
        }
        if (contextData.containsKey("source")) {
            context.append(String.format("• *Source:* %s\n", contextData.get("source")));
        }
        if (contextData.containsKey("deliveryDate")) {
            context.append(String.format("• *Delivery Date:* %s\n", contextData.get("deliveryDate")));
        }
        if (contextData.containsKey("batchNumber")) {
            context.append(String.format("• *Batch Number:* %s\n", contextData.get("batchNumber")));
        }

        // Incomplete tasks statistics
        if (contextData.containsKey("totalIncompleteTasks")) {
            context.append(String.format("• *Total Incomplete Tasks:* %s\n", contextData.get("totalIncompleteTasks")));
        }

        // Detailed incomplete tasks list
        if (contextData.containsKey("incompleteTaskDetails")) {
            context.append("\n🔍 *Incomplete Tasks Details:*\n");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> taskDetails = (List<Map<String, Object>>) contextData.get("incompleteTaskDetails");

            for (int i = 0; i < Math.min(taskDetails.size(), 10); i++) { // Show max 10 tasks
                Map<String, Object> task = taskDetails.get(i);
                context.append(String.format("  %d. *Task:* %s | *Status:* %s | *Items:* %s/%s incomplete\n",
                    i + 1,
                    task.get("taskNumber"),
                    task.get("status"),
                    task.get("incompleteItems"),
                    task.get("totalItems")));
            }

            if (taskDetails.size() > 10) {
                context.append(String.format("  ... and %d more tasks\n", taskDetails.size() - 10));
            }
        }

        // Alert time
        if (contextData.containsKey("alertTime")) {
            context.append(String.format("• *Alert Time (LA):* %s\n", contextData.get("alertTime").toString()));
        }
    }

    /**
     * Builds context for ghost inventory cleanup failure alert with special formatting
     */
    private void buildGhostInventoryCleanupContext(StringBuilder context, Map<String, Object> contextData) {
        // Order information with link
        if (contextData.containsKey("orderNumber")) {
            String orderNumber = contextData.get("orderNumber").toString().replaceFirst("M-", "");
            String orderUrl = String.format(ADMIN_BASE_URL_TEMPLATE, wmsAdminPortalBaseUrl, orderNumber);
            context.append(String.format("• *Shipping Order:* <%s|%s>\n", orderUrl, orderNumber));
        }

        // Error message
        if (contextData.containsKey("errorMessage")) {
            context.append(String.format("• *Error:* %s\n", contextData.get("errorMessage")));
        }

        // Delivery items summary
        if (contextData.containsKey("deliveryItems")) {
            String items = contextData.get("deliveryItems").toString();
            if (!"NO_ITEMS".equals(items)) {
                context.append(String.format("• *Delivery Items:* %s\n", items));
            } else {
                context.append("• *Delivery Items:* No items found\n");
            }
        }

        // Recovery requirement
        if (contextData.containsKey("requiresRecovery")) {
            Boolean requiresRecovery = (Boolean) contextData.get("requiresRecovery");
            if (requiresRecovery) {
                context.append("• *Recovery Status:* ⚠️ Manual data recovery required\n");
            }
        }

        // Alert time
        if (contextData.containsKey("alertTime")) {
            context.append(String.format("• *Alert Time (LA):* %s\n", contextData.get("alertTime")));
        }
    }

    /**
     * Builds generic context for other alert types
     */
    private void buildGenericContext(StringBuilder context, Map<String, Object> contextData, WmsExceptionAlertDto alertDto) {
        // Handle order number with link
        if (contextData.containsKey("orderNumber")) {
            String orderNumber = contextData.get("orderNumber").toString().replaceFirst("M-", "");
            String orderUrl = String.format(ADMIN_BASE_URL_TEMPLATE, wmsAdminPortalBaseUrl, orderNumber);
            context.append(String.format("• *Shipping Order:* <%s|%s>\n", orderUrl, orderNumber));
        }

        // Handle batch number
        if (contextData.containsKey("batchNumber")) {
            context.append(String.format("• *Batch:* %s\n", contextData.get("batchNumber")));
        }

        // Handle delivery date
        if (contextData.containsKey("deliveryDate")) {
            context.append(String.format("• *Delivery Date:* %s\n", contextData.get("deliveryDate")));
        }

        // Handle other context data
        contextData.entrySet().stream()
            .filter(entry -> !entry.getKey().equals("orderNumber")
                && !entry.getKey().equals("batchNumber")
                && !entry.getKey().equals("deliveryDate")
                && !entry.getKey().equals("batchId"))
            .forEach(entry -> context.append(String.format("• *%s:* %s\n",
                formatContextKey(entry.getKey()), entry.getValue())));
    }

    private String buildActionRequired() {
        return "⚡ *Action Required:* Please investigate and take appropriate action.";
    }

    private String formatContextKey(String key) {
        // Convert camelCase to readable format
        return key.replaceAll("([a-z])([A-Z])", "$1 $2")
            .substring(0, 1).toUpperCase() +
            key.replaceAll("([a-z])([A-Z])", "$1 $2").substring(1);
    }
}
