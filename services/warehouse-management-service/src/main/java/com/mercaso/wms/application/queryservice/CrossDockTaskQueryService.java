package com.mercaso.wms.application.queryservice;

import com.mercaso.wms.application.dto.CrossDockTaskDto;
import com.mercaso.wms.application.mapper.crossdock.CrossDockTaskDtoApplicationMapper;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class CrossDockTaskQueryService {

    private final CrossDockTaskRepository crossDockTaskRepository;

    private final CrossDockTaskDtoApplicationMapper crossDockTaskDtoApplicationMapper;

    public CrossDockTaskDto findById(UUID id) {
        CrossDockTask crossDockTask = crossDockTaskRepository.findById(id);
        if (crossDockTask == null) {
            log.error("Cross dock task not found with id: {}", id);
            return null;
        }
        return crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask);
    }
}
