package com.mercaso.wms.delivery.infrastructure.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when a user attempts to login from multiple devices.
 * This occurs when the login timestamp in the token is older than the one in the database,
 * indicating that the user has logged in from another device since this token was issued.
 */
@ResponseStatus(HttpStatus.UNAUTHORIZED)
public class SingleDeviceLoginException extends RuntimeException {

    private final String userId;
    private final String tokenVersion;
    private final String dbVersion;

    public SingleDeviceLoginException(String userId, String tokenVersion, String dbVersion) {
        super("Login from multiple devices detected for user: " + userId);
        this.userId = userId;
        this.tokenVersion = tokenVersion;
        this.dbVersion = dbVersion;
    }

    public SingleDeviceLoginException(String userId, String message) {
        super(message);
        this.userId = userId;
        this.tokenVersion = null;
        this.dbVersion = null;
    }

    public String getUserId() {
        return userId;
    }

    public String getTokenVersion() {
        return tokenVersion;
    }

    public String getDbVersion() {
        return dbVersion;
    }
} 