package com.mercaso.wms.infrastructure.event.factory;

import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.application.dto.event.BusinessEventPayloadDto;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class BusinessEventPayloadFactory<P extends BusinessEventPayloadDto<?>> {

    protected abstract BusinessEventPayloadDto<? extends BaseDto> build(BusinessEventDto businessEvent);

    public abstract Class<P> payloadClass();

    public Optional<BusinessEventPayloadDto<? extends BaseDto>> buildPayloadDto(BusinessEventDto businessEvent) {
        BusinessEventPayloadDto<? extends BaseDto> payloadDto = this.build(businessEvent);

        if (payloadDto != null) {
            return Optional.of(payloadDto);
        }

        return Optional.empty();
    }

}
