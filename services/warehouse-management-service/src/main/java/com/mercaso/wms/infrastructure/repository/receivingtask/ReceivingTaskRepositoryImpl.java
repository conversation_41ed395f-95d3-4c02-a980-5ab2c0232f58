package com.mercaso.wms.infrastructure.repository.receivingtask;

import com.mercaso.wms.application.dto.view.SearchReceivingTaskView;
import com.mercaso.wms.application.query.ReceivingTaskQuery;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.ReceivingTaskJdbcTemplate;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.ReceivingTaskJpaDao;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.dataobject.ReceivingTaskDo;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.mapper.ReceivingTaskDoMapper;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class ReceivingTaskRepositoryImpl implements ReceivingTaskRepository {

    public final ReceivingTaskDoMapper mapper;
    private final ReceivingTaskJpaDao jpaDao;
    private final ReceivingTaskJdbcTemplate jdbcTemplate;

    @Override
    public ReceivingTask save(ReceivingTask domain) {
        ReceivingTaskDo receivingTaskDo = mapper.domainToDo(domain);
        receivingTaskDo.getReceivingTaskItems()
            .forEach(receivingTaskItemDo -> receivingTaskItemDo.setReceivingTask(receivingTaskDo));
        return mapper.doToDomain(jpaDao.save(receivingTaskDo));
    }

    @Override
    public ReceivingTask findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public ReceivingTask update(ReceivingTask domain) {
        ReceivingTaskDo receivingTaskDo = jpaDao.findById(domain.getId())
            .orElseThrow(() -> new WmsBusinessException("ReceivingTask not found."));
        ReceivingTaskDo target = mapper.domainToDo(domain);

        target.getReceivingTaskItems().forEach(receivingTaskItemDo -> receivingTaskItemDo.setReceivingTask(target));
        List<String> ignoreProperties = List.of("createdBy", "createdAt", "createdUserName");
        BeanUtils.copyProperties(target, receivingTaskDo, ignoreProperties.toArray(new String[0]));
        return mapper.doToDomain(jpaDao.save(receivingTaskDo));
    }

    @Override
    public List<ReceivingTask> findByBatchId(UUID id) {
        return mapper.doToDomains(jpaDao.findByBatchId(id));
    }

    @Override
    public Page<SearchReceivingTaskView> search(ReceivingTaskQuery receivingTaskQuery, Pageable pageable) {
        return jdbcTemplate.search(receivingTaskQuery, pageable);
    }

    @Override
    public List<ReceivingTask> saveAll(List<ReceivingTask> receivingTasks) {
        List<ReceivingTaskDo> receivingTaskDos = mapper.domainToDos(receivingTasks);
        receivingTaskDos.forEach(receivingTaskDo -> receivingTaskDo.getReceivingTaskItems()
            .forEach(receivingTaskItemDo -> receivingTaskItemDo.setReceivingTask(receivingTaskDo)));
        return jpaDao.saveAll(receivingTaskDos).stream()
            .map(mapper::doToDomain)
            .toList();
    }

    @Override
    public void deleteAll() {
        jpaDao.deleteAll();
    }

    @Override
    public List<ReceivingTask> findByDeliveryDateAndStatus(String deliveryDate, ReceivingTaskStatus status) {
        return mapper.doToDomains(jpaDao.findByDeliveryDateAndStatus(deliveryDate, status));
    }

    @Override
    public void deleteByIds(List<UUID> ids) {
        jpaDao.deleteAllById(ids);
    }

    @Override
    public List<ReceivingTask> findByBatchIdAndStatusNotIn(UUID batchId, List<ReceivingTaskStatus> statuses) {
        return mapper.doToDomains(jpaDao.findByBatchIdAndStatusNotIn(batchId, statuses));
    }

    @Override
    public List<ReceivingTask> findByIds(List<UUID> ids) {
        return mapper.doToDomains(jpaDao.findByIdIn(ids));
    }
}