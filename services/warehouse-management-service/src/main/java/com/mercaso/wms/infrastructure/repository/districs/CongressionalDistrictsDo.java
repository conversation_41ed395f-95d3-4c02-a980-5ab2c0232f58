package com.mercaso.wms.infrastructure.repository.districs;

import com.mercaso.wms.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Table(name = "congressional_districts")
@SQLDelete(sql = "update congressional_districts set deleted_at = current_timestamp where id = ? and updated_at = ?")
public class CongressionalDistrictsDo extends BaseDo {

    @Column(name = "zip_code")
    private String zipCode;

    @Column(name = "postal_area_name")
    private String postalAreaName;

    @Column(name = "district_name")
    private String districtName;

    @Column(name = "district_number")
    private Integer districtNumber;

}
