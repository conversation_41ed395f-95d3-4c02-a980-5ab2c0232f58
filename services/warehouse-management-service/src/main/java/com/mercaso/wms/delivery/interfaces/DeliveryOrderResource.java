package com.mercaso.wms.delivery.interfaces;

import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderUnloadCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.service.DeliveryOrderApplicationService;
import com.mercaso.wms.delivery.infrastructure.annotation.SingleDeviceLoginCheck;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Delivery Orders")
@Slf4j
@Validated
@RestController
@RequestMapping("/delivery/delivery-orders")
@RequiredArgsConstructor
public class DeliveryOrderResource {

    private final DeliveryOrderApplicationService deliveryOrderApplicationService;

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:write:delivery-orders')")
    @PutMapping("/{deliveryOrderId}/in-transit")
    public DeliveryOrderDto inTransit(@PathVariable UUID deliveryOrderId) {
        log.info("Update delivery order to in transit for {}", deliveryOrderId);
        return deliveryOrderApplicationService.inTransit(deliveryOrderId);
    }

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:write:delivery-orders')")
    @PutMapping("/{deliveryOrderId}/arrive")
    public DeliveryOrderDto arrive(@PathVariable UUID deliveryOrderId) {
        log.info("Update delivery order to arrived for {}", deliveryOrderId);
        return deliveryOrderApplicationService.arrive(deliveryOrderId);
    }

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:write:delivery-orders')")
    @PutMapping("/{deliveryOrderId}/delivery")
    public DeliveryOrderDto delivery(@PathVariable UUID deliveryOrderId, @RequestBody DeliveryOrderDeliveredCommand command) {
        log.info("Update delivery order to delivered for {}", deliveryOrderId);
        return deliveryOrderApplicationService.delivery(deliveryOrderId, command);
    }

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:write:delivery-orders')")
    @PutMapping("/{deliveryOrderId}/unload")
    public DeliveryOrderDto unload(@PathVariable UUID deliveryOrderId, @RequestBody DeliveryOrderUnloadCommand command) {
        log.info("Update delivery order to unload for {}, {}", deliveryOrderId, command);
        return deliveryOrderApplicationService.unload(deliveryOrderId, command);
    }

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:write:delivery-orders')")
    @PutMapping("/{deliveryOrderId}")
    public DeliveryOrderDto update(@PathVariable UUID deliveryOrderId, @RequestBody UpdateDeliveryOrderCommand command) {
        log.info("Update delivery order for {}, {}", deliveryOrderId, command);
        return deliveryOrderApplicationService.update(deliveryOrderId, command);
    }

    @PreAuthorize("hasAuthority('da:read:delivery-orders')")
    @PutMapping("/send-invoice/{deliveryOrderId}")
    public void sendInvoice(@PathVariable UUID deliveryOrderId) {
        log.info("Send invoice for delivery order {}", deliveryOrderId);
        deliveryOrderApplicationService.sendInvoice(deliveryOrderId);
    }

}
