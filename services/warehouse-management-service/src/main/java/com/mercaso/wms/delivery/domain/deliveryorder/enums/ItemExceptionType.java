package com.mercaso.wms.delivery.domain.deliveryorder.enums;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

@Getter
public enum ItemExceptionType {
    DAMAGED,
    EXPIRED,
    MISSING,
    RETURNS,
    <PERSON>ONG,
    UNPLANNED;

    public static Map<String, BigDecimal> initializeAllReasonCodeTotals() {
        Map<String, BigDecimal> reasonCodeTotals = new HashMap<>();
        Arrays.stream(ItemExceptionType.values())
            .map(Enum::name)
            .forEach(type -> reasonCodeTotals.put(type, BigDecimal.ZERO));
        return reasonCodeTotals;
    }
} 