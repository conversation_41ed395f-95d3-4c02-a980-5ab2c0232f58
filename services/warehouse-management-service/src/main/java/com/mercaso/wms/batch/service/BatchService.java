package com.mercaso.wms.batch.service;

import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.ItemSerachDto;
import com.mercaso.ims.client.dto.VendorItemDto;
import com.mercaso.wms.application.dto.BatchDto;
import com.mercaso.wms.application.service.BatchItemApplicationService;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.CreateBatchDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.IgnoredOrderDto;
import com.mercaso.wms.batch.dto.LookupDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.dto.StockDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.dto.response.Response;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.mapper.DtoMapper;
import com.mercaso.wms.batch.strategy.PopulateBreakdownStrategy;
import com.mercaso.wms.batch.writer.TemplateWriterService;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.inventorystock.StockAggregateProjection;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.applicationevent.listener.PickingTaskApplicationEventListener;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.utils.ResponseUtil;
import com.mercaso.wms.infrastructure.utils.StockUtil;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@AllArgsConstructor
public class BatchService {

    private final PopulateBreakdownStrategy fullBreakdownPopulateStrategy;

    private final PopulateBreakdownStrategy fullBreakDownSmallPopulateStrategy;

    private final ShippingOrderApplicationService shippingOrderApplicationService;

    private final PopulateStrategyService populateStrategyService;

    private final LocationRepository locationRepository;

    private final ImsAdaptor imsAdaptor;

    private final TemplateWriterService templateWriterService;

    private final BatchItemApplicationService batchItemApplicationService;

    private final BatchRepository batchRepository;

    private final ShippingOrderRepository shippingOrderRepository;

    private final PickingTaskApplicationEventListener pickingTaskApplicationEventListener;

    private final InventoryStockRepository inventoryStockRepository;

    private final WarehouseRepository warehouseRepository;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final PickingTaskRepository pickingTaskRepository;

    private final ReceivingTaskRepository receivingTaskRepository;

    private final BatchItemRepository batchItemRepository;

    @Transactional
    public Response<BatchDto> createBatch(CreateBatchDto createBatchDto) throws WmsBusinessException {
        log.info("[createBatch] createBatchDto: {}.", createBatchDto);
        String taggedWith = createBatchDto.getTaggedWith().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(("createBatch_" + taggedWith).hashCode());
        if (isAcquired == null || !isAcquired) {
            throw new WmsBusinessException("The batch is being created.");
        }
        List<ShippingOrder> shippingOrders = shippingOrderApplicationService.findActiveShippingOrdersByDeliveryDate(taggedWith);
        log.info("[createBatch] shopifyOrders size: {}.", shippingOrders.size());
        if (shippingOrders.isEmpty()) {
            throw new WmsBusinessException("No shopify orders or stock data found.");
        }
        List<Location> locations = locationRepository.findAll();

        try {
            List<IgnoredOrderDto> ignoredOrders = Lists.newArrayList();
            addExistingBreakdownToIgnoreOrders(taggedWith, ignoredOrders);

            List<String> skuList = shippingOrders.stream()
                .flatMap(order -> order.getShippingOrderItems().stream())
                .map(ShippingOrderItem::getSkuNumber)
                .distinct()
                .toList();
            if (skuList.isEmpty()) {
                throw new WmsBusinessException("No sku found in shopify orders.");
            }
            Map<String, ItemCategoryDto> itemMap = getItemMap(skuList);

            PopulateCondition populateCondition = processLookupData(shippingOrders, itemMap);
            List<ExcelBatchDto> excelBatchDtos = new ArrayList<>();
            populateCondition.setShippingOrders(shippingOrders);
            populateCondition.setExcelBatchDtoList(excelBatchDtos);
            populateCondition.setItemMap(itemMap);

            List<StockDto> mfcStocks = getAvailableStockDtos("MFC");
            populateCondition.setMfcStocks(mfcStocks);

            List<StockDto> mdcStocks = getAvailableStockDtos("MDC");
            log.info("[createBatch] mdcStocks size: {}.", mdcStocks.size());
            populateCondition.setMdcStocks(mdcStocks);

            populateCondition.setDeliveryDate(taggedWith);
            populateCondition.setIgnoredOrders(ignoredOrders);
            populateCondition.setLocations(locations);

            excelBatchDtos = populateStrategyService.populateBatchTemplate(populateCondition);
            log.info("[createBatch] batchDto populate size: {}.", excelBatchDtos.size());

            List<BreakdownDto> bigBreakdownDtos = fullBreakdownPopulateStrategy.populateBreakdownTemplate(excelBatchDtos);
            List<BreakdownDto> smallBreakdownDtos = fullBreakDownSmallPopulateStrategy.populateBreakdownTemplate(excelBatchDtos);

            Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
            for (ExcelBatchDto batchDto : excelBatchDtos) {
                if (StringUtils.isNotEmpty(batchDto.getSource())) {
                    if (batchDto.getSource().equals(SourceEnum.MDC.name())) {
                        sourceAndListMap.computeIfAbsent(SourceEnum.MFC.name(), k -> new ArrayList<>())
                            .add(batchDto);
                    } else {
                        sourceAndListMap.computeIfAbsent(batchDto.getSource(), k -> new ArrayList<>())
                            .add(batchDto);
                    }
                }
            }

            WriteTemplateCondition writeTemplateCondition = WriteTemplateCondition.builder()
                .excelBatchDtos(excelBatchDtos)
                .bigBreakdownDtos(bigBreakdownDtos)
                .smallBreakdownDtos(smallBreakdownDtos)
                .taggedWith(taggedWith)
                .fileNames(CollectionUtils.isEmpty(createBatchDto.getFileNames()) ? null
                    : List.of(createBatchDto.getFileNames().getFirst()))
                .sourceAndListMap(sourceAndListMap)
                .build();

            BatchDto batchDto = templateWriterService.writeTemplate(writeTemplateCondition);

            sourceAndListMap.clear();
            sourceAndListMap = null;

            saveBatchItems(writeTemplateCondition, batchDto, shippingOrders);

            Batch batch = batchRepository.findById(batchDto.getId());
            updateShippingOrders(excelBatchDtos, shippingOrders, batch, locations);
            pickingTaskApplicationEventListener.handleBatchCreatedEvent(batchDto.getId());

            return ResponseUtil.successResponse(List.of(batchDto));

        } catch (WmsBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[createBatch] create batch failed.", e);
            throw new WmsBusinessException("Create batch failed.", e);
        } finally {
            pgAdvisoryLock.unLock(("createBatch_" + taggedWith).hashCode());
        }
    }

    @Transactional
    public void cancelBatch(UUID batchId) {
        log.info("[cancelBatch] Cancelling batch with id: {}", batchId);

        // 1. Find the batch
        Batch batch = batchRepository.findById(batchId);
        if (batch == null) {
            throw new WmsBusinessException("Batch not found with id: " + batchId);
        }

        // 2. Validate batch can be cancelled
        validateBatchCanBeCancelled(batchId);

        // 3. Update batch status to CANCELED
        batch.setStatus(BatchStatus.CANCELED);
        batchRepository.update(batch);

        // 4. Delete batch items
        List<BatchItem> batchItems = batchItemRepository.findBatchItemsBy(batchId, "");
        if (!batchItems.isEmpty()) {
            batchItemRepository.deleteAll(batchItems);
        }

        // 5. Delete picking tasks
        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(batchId);
        if (!CollectionUtils.isEmpty(pickingTasks)) {
            pickingTaskRepository.deleteByIds(pickingTasks.stream().map(PickingTask::getId).collect(Collectors.toList()));
        }

        // 6. Delete receiving tasks
        List<ReceivingTask> receivingTasks = receivingTaskRepository.findByBatchId(batchId);
        if (!CollectionUtils.isEmpty(receivingTasks)) {
            receivingTaskRepository.deleteByIds(receivingTasks.stream()
                .map(ReceivingTask::getId)
                .collect(Collectors.toList()));
        }

        // 7. Update shipping orders status back to OPEN
        List<ShippingOrder> shippingOrders = shippingOrderRepository.findByBatchId(batchId);
        if (!shippingOrders.isEmpty()) {
            shippingOrders.forEach(order -> order.setState(ShippingOrderStatus.OPEN));
            shippingOrderRepository.updateAll(shippingOrders);
        }

        log.info("[cancelBatch] Successfully cancelled batch with id: {}", batchId);
    }

    private void validateBatchCanBeCancelled(UUID batchId) {
        // Check if there are any picking tasks with status other than CREATED or ASSIGNED
        List<PickingTask> invalidPickingTasks = pickingTaskRepository.findByBatchIdAndStatusNotIn(batchId,
            List.of(PickingTaskStatus.CREATED, PickingTaskStatus.ASSIGNED));
        if (!invalidPickingTasks.isEmpty()) {
            PickingTask invalidTask = invalidPickingTasks.getFirst();
            throw new WmsBusinessException("Cannot cancel batch: picking task " + invalidTask.getNumber() +
                " is in status " + invalidTask.getState() + ". Only CREATED or ASSIGNED status is allowed.");
        }

        // Check if there are any receiving tasks with status other than CREATED
        List<ReceivingTask> invalidReceivingTasks = receivingTaskRepository.findByBatchIdAndStatusNotIn(batchId,
            List.of(ReceivingTaskStatus.CREATED));
        if (!invalidReceivingTasks.isEmpty()) {
            ReceivingTask invalidTask = invalidReceivingTasks.getFirst();
            throw new WmsBusinessException("Cannot cancel batch: receiving task " + invalidTask.getNumber() +
                " is in status " + invalidTask.getStatus() + ". Only CREATED status is allowed.");
        }
    }

    public void updateShippingOrders(List<ExcelBatchDto> excelBatchDtos,
        List<ShippingOrder> shippingOrders,
        Batch batch,
        List<Location> locations) {
        Map<String, UUID> orderNumberAndBreakdownLocationIdMapping = excelBatchDtos.stream()
            .filter(dto -> dto.getBreakdownLocationId() != null)
            .collect(Collectors.toMap(ExcelBatchDto::getOrderNumber,
                ExcelBatchDto::getBreakdownLocationId,
                (existingValue, newValue) -> newValue));

        shippingOrders.forEach(shopifyOrder -> {
            shopifyOrder.setBatchId(batch.getId());
            UUID breakdownLocationId = orderNumberAndBreakdownLocationIdMapping.get(shopifyOrder.getOrderNumber());
            if (breakdownLocationId != null) {
                shopifyOrder.setBreakdownLocation(locations.stream()
                    .filter(location -> location.getId().equals(breakdownLocationId))
                    .findFirst()
                    .orElse(null));
            }
        });
        shippingOrderRepository.saveAll(shippingOrders);
    }

    public void saveBatchItems(WriteTemplateCondition writeTemplateCondition,
        BatchDto batchDto,
        List<ShippingOrder> shippingOrders) {
        if (!CollectionUtils.isEmpty(writeTemplateCondition.getExcelBatchDtos())) {
            batchItemApplicationService.createBatchItems(covertBatchDtoToBatchItems(
                writeTemplateCondition.getExcelBatchDtos(),
                shippingOrders,
                batchDto.getId()));
        }
    }

    private void addExistingBreakdownToIgnoreOrders(String deliveryDate, List<IgnoredOrderDto> ignoredOrders) {
        List<ShippingOrder> rescheduledShippingOrders = shippingOrderRepository.findRescheduledShippingOrders(deliveryDate);
        if (!CollectionUtils.isEmpty(rescheduledShippingOrders)) {
            for (ShippingOrder rescheduledShippingOrder : rescheduledShippingOrders) {
                IgnoredOrderDto ignoredOrderDto = new IgnoredOrderDto();
                ignoredOrderDto.setOrderNumber(rescheduledShippingOrder.getOrderNumber());
                ignoredOrderDto.setBreakdown(
                    rescheduledShippingOrder.getBreakdownLocation() != null ? rescheduledShippingOrder.getBreakdownLocation()
                        .getName() : null);
                if (!ignoredOrders.contains(ignoredOrderDto)) {
                    ignoredOrders.add(ignoredOrderDto);
                }
            }
        }
    }

    private List<BatchItem> covertBatchDtoToBatchItems(List<ExcelBatchDto> excelBatchDtos,
        List<ShippingOrder> shippingOrders, UUID batchId) {

        Map<String, ShippingOrder> orderMap = shippingOrders.stream()
            .collect(Collectors.toMap(ShippingOrder::getOrderNumber, order -> order));

        List<BatchItem> batchItems = new LinkedList<>();

        for (ExcelBatchDto excelBatchDto : excelBatchDtos) {
            BatchItem batchItem = DtoMapper.INSTANCE.toBatchItem(excelBatchDto);
            batchItem.setBatchId(batchId);
            batchItem.setPickingAppCovered(true);

            ShippingOrder shippingOrder = orderMap.get(excelBatchDto.getOrderNumber());
            if (shippingOrder != null) {
                batchItem.setShippingOrderId(shippingOrder.getId());
                batchItem.setShippingOrderItemId(shippingOrder.getShippingOrderItems().stream()
                    .filter(item -> item.getSkuNumber().equals(excelBatchDto.getItemNumber())
                        && item.getLine().equals(excelBatchDto.getLine()))
                    .map(ShippingOrderItem::getId)
                    .findFirst()
                    .orElse(null));
            } else {
                log.error("[covertBatchDtoToBatchItems] Shipping order not found for order number: {}",
                    excelBatchDto.getOrderNumber());
            }
            batchItems.add(batchItem);
        }
        return batchItems;
    }

    private PopulateCondition processLookupData(List<ShippingOrder> shippingOrders,
        Map<String, ItemCategoryDto> itemCategoryDtoMap) throws WmsBusinessException {
        Map<SourceEnum, List<LookupDto>> lookUpData = new EnumMap<>(SourceEnum.class);
        removeBlankShippingOrderItems(shippingOrders);
        populateSkuIfNeeded(shippingOrders);

        buildLookupData(itemCategoryDtoMap, lookUpData);

        for (ItemCategoryDto value : itemCategoryDtoMap.values()) {
            LookupDto lookupDto = DtoMapper.INSTANCE.toLookupDto(value);
            lookUpData.computeIfAbsent(SourceEnum.MDC, k -> new LinkedList<>()).add(lookupDto);
        }

        return PopulateCondition.builder()
            .lookUpData(lookUpData)
            .build();
    }

    private void buildLookupData(Map<String, ItemCategoryDto> itemCategoryDtoMap,
        Map<SourceEnum, List<LookupDto>> lookUpData) {
        for (Map.Entry<String, ItemCategoryDto> entry : itemCategoryDtoMap.entrySet()) {
            ItemCategoryDto value = entry.getValue();
            LookupDto lookupDto = buildLookupData(value, value.getBackupVendorId());
            if (lookupDto != null) {
                lookUpData.computeIfAbsent(lookupDto.getSourceEnum(), k -> new LinkedList<>()).add(lookupDto);
            }
        }
    }

    private LookupDto buildLookupData(ItemCategoryDto value, UUID vendorId) {
        if (vendorId == null || CollectionUtils.isEmpty(value.getVendorItemDtos())) {
            return null;
        }
        for (VendorItemDto vendorItemDto : value.getVendorItemDtos()) {
            if (vendorId.equals(vendorItemDto.getVendorId())) {
                SourceEnum sourceEnum = SourceEnum.fromVendorName(vendorItemDto.getVendorName());
                if (sourceEnum != null) {
                    LookupDto lookupDto = DtoMapper.INSTANCE.toLookupDto(value);
                    lookupDto.setVendorItemNumber(vendorItemDto.getVendorSkuNumber());
                    lookupDto.setAisle(
                        StringUtils.isNotEmpty(vendorItemDto.getAisle()) ? vendorItemDto.getAisle() : value.getDepartment());
                    lookupDto.setSourceEnum(sourceEnum);
                    return lookupDto;
                }
            }
        }
        return null;
    }

    private void populateSkuIfNeeded(List<ShippingOrder> shippingOrders) {
        shippingOrders.stream()
            .flatMap(order -> order.getShippingOrderItems().stream())
            .filter(lineItem -> StringUtils.isEmpty(lineItem.getSkuNumber()))
            .forEach(shippingOrderItem -> {
                ItemSerachDto itemSerachDto = imsAdaptor.searchItemsByTitle(shippingOrderItem.getTitle());
                if (itemSerachDto != null) {
                    shippingOrderItem.setSkuNumber(itemSerachDto.getSkuNumber());
                }
            });
    }

    private void removeBlankShippingOrderItems(List<ShippingOrder> shippingOrders) {
        shippingOrders.forEach(order -> order.getShippingOrderItems()
            .removeIf(shippingOrderItem -> StringUtils.isEmpty(shippingOrderItem.getSkuNumber())
                && StringUtils.isEmpty(shippingOrderItem.getTitle())));
    }

    private Map<String, ItemCategoryDto> getItemMap(List<String> skuList) {
        return imsAdaptor.getItemsBySkus(skuList).stream()
            .collect(Collectors.toMap(ItemCategoryDto::getSkuNumber, item -> item));
    }

    private List<StockDto> getAvailableStockDtos(String warehouseName) {
        Warehouse warehouse = warehouseRepository.findByName(warehouseName);
        if (warehouse == null) {
            throw new WmsBusinessException("Warehouse not found.");
        }

        List<StockAggregateProjection> projections = inventoryStockRepository
            .findAvailableStockAggregates(warehouse.getId(), List.of(LocationType.BIN, LocationType.STOCK));

        return projections.stream()
            .map(this::convertToStockDto)
            .collect(Collectors.toList());
    }

    private StockDto convertToStockDto(StockAggregateProjection proj) {
        Location tempLocation = Location.builder()
            .id(proj.getLocationId())
            .name(proj.getLocationName())
            .type(proj.getLocationType())
            .build();

        return StockDto.builder()
            .sku(proj.getSkuNumber())
            .name(proj.getItemTitle())
            .subLocation(proj.getLocationName())
            .locationId(proj.getLocationId())
            .type(StockUtil.getLocationTypeFromSubLocation(proj.getLocationName(), tempLocation))
            .totalQty(proj.getQuantity())
            .remainingQty(proj.getQuantity())
            .source(SourceEnum.fromName(proj.getWarehouseName()))
            .build();
    }

}
