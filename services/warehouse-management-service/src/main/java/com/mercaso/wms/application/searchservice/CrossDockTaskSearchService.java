package com.mercaso.wms.application.searchservice;

import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.application.query.CrossDockTaskQuery;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.criteria.CrossDockTaskSearchCriteria;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class CrossDockTaskSearchService {

    private final CrossDockTaskRepository crossDockTaskRepository;

    public Page<SearchCrossDockTaskView> searchCrossDockTasks(CrossDockTaskQuery crossDockTaskQuery, Pageable pageable) {
        log.info("[searchCrossDockTasks] crossDockTaskQuery: {}, pageable: {}", crossDockTaskQuery, pageable);
        CrossDockTaskSearchCriteria criteria = getCrossDockTaskSearchCriteria(crossDockTaskQuery);
        return crossDockTaskRepository.search(criteria, pageable);
    }

    private CrossDockTaskSearchCriteria getCrossDockTaskSearchCriteria(CrossDockTaskQuery crossDockTaskQuery) {
        CrossDockTaskSearchCriteria criteria = new CrossDockTaskSearchCriteria();
        BeanUtils.copyProperties(crossDockTaskQuery, criteria);
        return criteria;
    }

}