package com.mercaso.wms.delivery.application.search;

import com.mercaso.wms.delivery.application.dto.view.SearchFailedDeliveryOrderItemView;
import com.mercaso.wms.delivery.application.query.FailedDeliveryOrderItemQuery;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.FailedDeliveryOrderItemJdbcTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class FailedDeliveryOrderItemSearchService {

    private final FailedDeliveryOrderItemJdbcTemplate jdbcTemplate;

    public Page<SearchFailedDeliveryOrderItemView> search(FailedDeliveryOrderItemQuery itemQuery, Pageable pageable) {
        return jdbcTemplate.search(itemQuery, pageable);
    }

}
