package com.mercaso.wms.delivery.infrastructure.external.onesignal;

import com.mercaso.wms.delivery.infrastructure.external.onesignal.config.OnesignalProperties;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.dto.EmailRequestDto;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.dto.EmailResponseDto;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class OnesignalAdaptor {

    private final OnesignalProperties onesignalProperties;

    public void sendEmail(String emailTo, Map<String, String> customData) {
        log.info("Sending email using OneSignal for email: {}", emailTo);
        EmailRequestDto emailRequestDto = buildRequestDto(emailTo, customData);
        try {
            EmailResponseDto emailResponseDto = HttpClientUtils.executePostRequest(
                onesignalProperties.getSendEmailUrl(),
                emailRequestDto,
                buildHeaders(),
                EmailResponseDto.class
            );
            log.info("Email sent successfully: {}", emailResponseDto);
        } catch (Exception e) {
            log.error("Failed to send email: {}", emailRequestDto, e);
        }
    }

    private EmailRequestDto buildRequestDto(String emailTo, Map<String, String> customData) {
        return EmailRequestDto.builder()
            .appId(onesignalProperties.getAppId())
            .emailTo(List.of(emailTo))
            .templateId(onesignalProperties.getInvoiceTemplateId())
            .emailFromName("Mercaso")
            .customData(customData)
            .includeUnsubscribed(false)
            .build();
    }

    private Map<String, String> buildHeaders() {
        return Map.of(
            "Content-Type", "application/json",
            "Authorization", "Key " + onesignalProperties.getApiKey()
        );
    }

}
