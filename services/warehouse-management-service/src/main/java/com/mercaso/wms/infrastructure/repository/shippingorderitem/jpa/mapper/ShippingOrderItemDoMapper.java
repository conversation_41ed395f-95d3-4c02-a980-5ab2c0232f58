package com.mercaso.wms.infrastructure.repository.shippingorderitem.jpa.mapper;

import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import com.mercaso.wms.infrastructure.repository.shippingorderitem.jpa.dataobject.ShippingOrderItemDo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ShippingOrderItemDoMapper extends BaseDoMapper<ShippingOrderItemDo, ShippingOrderItem> {

    ShippingOrderItemDoMapper INSTANCE = org.mapstruct.factory.Mappers.getMapper(ShippingOrderItemDoMapper.class);

    @Mapping(target = "shippingOrderId", source = "shippingOrder.id")
    ShippingOrderItem doToDomain(ShippingOrderItemDo dataObject);

}
