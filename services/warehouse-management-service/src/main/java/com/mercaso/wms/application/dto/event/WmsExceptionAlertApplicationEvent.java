package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.infrastructure.event.applicationevent.BaseApplicationEvent;

/**
 * WMS Exception Alert Application Event
 * Published when a WMS exception alert needs to be processed
 */
public class WmsExceptionAlertApplicationEvent extends BaseApplicationEvent<WmsExceptionAlertPayloadDto> {

    /**
     * Create a new WMS Exception Alert Application Event
     *
     * @param source the object on which the event initially occurred or with which the event is associated (never {@code null})
     * @param payload the alert payload data
     */
    public WmsExceptionAlertApplicationEvent(Object source, WmsExceptionAlertPayloadDto payload) {
        super(source, payload);
    }
} 