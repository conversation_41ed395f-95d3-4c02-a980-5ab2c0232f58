package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import com.mercaso.wms.application.dto.PickedShippingOrderItemExportDto;
import com.mercaso.wms.application.dto.view.SearchShippingOrderView;
import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.batch.dto.SkuCountByDeliveryDate;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ShippingOrderJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public List<SkuCountByDeliveryDate> skuCountByDeliveryDate(String deliveryDate) {
        String sql = "SELECT soi.sku_number as sku, CAST(sum(soi.qty) as BIGINT) as count FROM shipping_order_items soi "
            + "left join public.shipping_order so on so.id = soi.shipping_order_id "
            + "WHERE so.status = 'OPEN' and so.fulfillment_status is null and so.delivery_date = :deliveryDate GROUP BY sku";
        Map<String, Object> params = Map.of("deliveryDate", deliveryDate);

        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, params);

        return results.stream()
            .filter(row -> row.get("sku") != null && row.get("count") != null)
            .map(row -> SkuCountByDeliveryDate.builder().sku((String) row.get("sku")).count((Long) row.get("count")).build())
            .toList();
    }

    public List<PickedShippingOrderItemExportDto> fetchPickedShippingOrderItemsByDeliveryDate(String deliveryDate) {
        String sql = """
                SELECT
                    soi.sku_number AS item,
                    so.order_number AS orderNumber,
                    soi.line AS line,
                    soi.title AS itemDescription,
                    soi.qty AS requestedQty,
                    soi.picked_qty AS pickedQty,
                    soi.fulfilled_qty AS fulfilledQty,
                    COALESCE(picking_info.breakdown, receiving_info.breakdown) AS breakdown,
                    COALESCE(picking_info.initialFrom, receiving_info.initialFrom) AS initialFrom,
                    COALESCE(picking_info.finalFrom, receiving_info.finalFrom) AS finalFrom
                FROM shipping_order_items soi
                LEFT JOIN shipping_order so ON so.id = soi.shipping_order_id
                LEFT JOIN (
                    SELECT DISTINCT ON (pti.shipping_order_item_id)
                        pti.shipping_order_item_id,
                        bi.breakdown_name AS breakdown,
                        bi.source AS initialFrom,
                        pt.source AS finalFrom
                    FROM picking_task_items pti
                    LEFT JOIN picking_task pt ON pt.id = pti.picking_task_id
                    LEFT JOIN batch_items bi ON bi.id = pti.batch_item_id
                    WHERE pti.shipping_order_item_id IS NOT NULL
                      AND pti.deleted_at IS NULL
                      AND pt.deleted_at IS NULL
                    ORDER BY pti.shipping_order_item_id, pti.created_at DESC
                ) picking_info ON picking_info.shipping_order_item_id = soi.id
                LEFT JOIN (
                    SELECT DISTINCT ON (rti.shipping_order_item_id)
                        rti.shipping_order_item_id,
                        bi.breakdown_name AS breakdown,
                        bi.source AS initialFrom,
                        rt.vendor AS finalFrom
                    FROM receiving_task_items rti
                    LEFT JOIN receiving_task rt ON rt.id = rti.receiving_task_id
                    LEFT JOIN batch_items bi ON bi.id = rti.batch_item_id
                    WHERE rti.shipping_order_item_id IS NOT NULL
                      AND rti.deleted_at IS NULL
                      AND rt.deleted_at IS NULL
                    ORDER BY rti.shipping_order_item_id, rti.created_at DESC
                ) receiving_info ON receiving_info.shipping_order_item_id = soi.id
                WHERE so.delivery_date = :deliveryDate
                  AND so.status = 'PICKED'
                  AND so.deleted_at IS NULL
                  AND soi.deleted_at IS NULL
                ORDER BY breakdown, so.order_number, soi.line
            """;
        Map<String, Object> params = Map.of("deliveryDate", deliveryDate);
        return jdbcTemplate.query(sql, params, new BeanPropertyRowMapper<>(PickedShippingOrderItemExportDto.class));
    }

    public Page<SearchShippingOrderView> search(ShippingOrderQuery query, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        String selectSql = sqlBuilder.buildSelectSql();
        String countSql = sqlBuilder.buildCountSql();

        log.debug("Shipping Order V2 executing SQL: {}, params: {}", selectSql, params.getValues());

        try {
            Long total = jdbcTemplate.queryForObject(countSql, params, Long.class);
            long effectiveTotal = total != null ? total : 0L;

            List<SearchShippingOrderView> content = jdbcTemplate.query(selectSql, params, this::convert);

            long executionTime = System.currentTimeMillis() - startTime;
            log.info("Shipping Order V2 query executed in {}ms, returned {} results", executionTime, content.size());

            // Log slow queries
            if (executionTime > 1000) {
                log.warn("Slow query detected: {}ms for query: {}", executionTime, selectSql);
            }

            return new PageImpl<>(content, pageable, effectiveTotal);
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("Shipping Order V2 query failed after {}ms: {}", executionTime, e.getMessage(), e);
            throw e;
        }
    }

    private SearchShippingOrderView convert(ResultSet rs, int rowNum) throws SQLException {
        try {
            return SearchShippingOrderView.builder()
                .id(rs.getObject("id", UUID.class))
                .orderNumber(rs.getString("order_number"))
                .shopifyOrderId(rs.getString("shopify_order_id"))
                .orderDate(getInstantOrNull(rs, "order_date"))
                .deliveryDate(rs.getString("delivery_date"))
                .shippedDate(getInstantOrNull(rs, "shipped_date"))
                .status(rs.getString("status") != null ? ShippingOrderStatus.valueOf(rs.getString("status")) : null)
                .breakdownName(rs.getString("name"))
                .palletCount(rs.getObject("pallet_count", Integer.class))
                .originalTotalQty(rs.getInt("original_total_qty"))
                .currentTotalQty(rs.getInt("current_total_qty"))
                .pickedTotalQty(rs.getInt("picked_total_qty"))
                .fulfilledTotalQty(rs.getInt("fulfilled_total_qty"))
                .validatedTotalQty(rs.getInt("validated_total_qty"))
                .deliveredTotalQty(rs.getInt("delivered_total_qty"))
                .driverUserName(rs.getString("driver_user_name"))
                .truckNumber(rs.getString("truck_number"))
                .hasHighValueItems(rs.getBoolean("has_high_value_items"))
                .build();
        } catch (Exception e) {
            log.error("Error converting query result: {}", e.getMessage(), e);
            throw e;
        }
    }

    private Instant getInstantOrNull(ResultSet rs, String columnName) throws SQLException {
        java.sql.Timestamp timestamp = rs.getTimestamp(columnName);
        return timestamp != null ? timestamp.toInstant() : null;
    }
}
