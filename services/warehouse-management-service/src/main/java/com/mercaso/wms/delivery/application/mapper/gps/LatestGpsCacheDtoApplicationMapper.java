package com.mercaso.wms.delivery.application.mapper.gps;

import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.delivery.application.dto.gps.LatestGpsCacheDto;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCache;
import org.locationtech.jts.geom.Point;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LatestGpsCacheDtoApplicationMapper extends BaseDtoApplicationMapper<LatestGpsCache, LatestGpsCacheDto> {

    @Mapping(target = "latitude", expression = "java(convert(latestGpsCache.getCoordinates(), true))")
    @Mapping(target = "longitude", expression = "java(convert(latestGpsCache.getCoordinates(), false))")
    LatestGpsCacheDto domainToDto(LatestGpsCache latestGpsCache);

    default double convert(Point coordinates, boolean isLatitude) {
        return isLatitude ? coordinates.getY() : coordinates.getX();
    }

}
