package com.mercaso.wms.delivery.domain.customer;

import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShopifyCustomerDto;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerService {

    private final PgAdvisoryLock pgAdvisoryLock;

    private final CustomerRepository customerRepository;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Customer findOrSaveCustomer(ShopifyCustomerDto shopifyCustomerDto) {
        if (shopifyCustomerDto == null || shopifyCustomerDto.getId() == null) {
            return null;
        }
        Customer customer = customerRepository.findByExternalId(shopifyCustomerDto.getId());
        if (customer != null) {
            return customer;
        }
        customer = customerRepository.findByEmail(shopifyCustomerDto.getEmail());
        if (customer != null) {
            customer.setExternalId(shopifyCustomerDto.getId());
            return customerRepository.update(customer);
        }
        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock(
            shopifyCustomerDto.getId().hashCode(), "CustomerService.saveCustomer"
        );
        return customerRepository.save(Customer.builder().build().create(shopifyCustomerDto));
    }

}
