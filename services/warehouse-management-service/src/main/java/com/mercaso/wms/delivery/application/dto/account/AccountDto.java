package com.mercaso.wms.delivery.application.dto.account;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for Account entity.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountDto extends BaseDto {

    private UUID id;
    private UUID warehouseId;
    private AccountStatus status;
    private UUID userId;
    private String userName;
    private String email;
    private String secretKey;
    private String createdBy;
    private Instant createdAt;
    private String updatedBy;
    private Instant updatedAt;
} 