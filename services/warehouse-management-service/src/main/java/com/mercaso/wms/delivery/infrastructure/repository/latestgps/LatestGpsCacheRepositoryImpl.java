package com.mercaso.wms.delivery.infrastructure.repository.latestgps;

import static com.mercaso.wms.infrastructure.utils.DateUtils.getNowInLA;

import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCache;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCacheRepository;
import com.mercaso.wms.delivery.infrastructure.repository.latestgps.jpa.LatestGpsCacheJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.latestgps.jpa.dataobject.LatestGpsCacheDo;
import com.mercaso.wms.delivery.infrastructure.repository.latestgps.jpa.mapper.LatestGpsCacheDoMapper;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class LatestGpsCacheRepositoryImpl implements LatestGpsCacheRepository {

    private final LatestGpsCacheDoMapper mapper;
    private final LatestGpsCacheJpaDao jpaDao;


    @Override
    public List<LatestGpsCache> findAllByRecentHour() {
        return mapper.doToDomains(jpaDao.findByReportAtBetween(getNowInLA().minus(1, ChronoUnit.HOURS), getNowInLA()));
    }

    @Override
    public Optional<LatestGpsCache> findByUserId(UUID userId) {
        return Optional.ofNullable(mapper.doToDomain(jpaDao.findByUserId(userId)));
    }

    @Override
    public Optional<LatestGpsCache> findByDeliveryOrderId(UUID deliveryOrderId) {
        return Optional.ofNullable(mapper.doToDomain(jpaDao.findByDeliveryOrderId(deliveryOrderId)));
    }

    @Override
    public List<LatestGpsCache> findAllByDeliveryOrderIdIn(List<UUID> deliveryOrderIds) {
        return mapper.doToDomains(jpaDao.findAllByDeliveryOrderIdIn(deliveryOrderIds));
    }

    @Override
    public LatestGpsCache save(LatestGpsCache domain) {
        return mapper.doToDomain(jpaDao.save(mapper.domainToDo(domain)));
    }

    @Override
    public LatestGpsCache findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public LatestGpsCache update(LatestGpsCache domain) {
        LatestGpsCacheDo latestGpsCacheDo = jpaDao.findByUserId(domain.getUserId());
        if (null == latestGpsCacheDo) {
            throw new WmsBusinessException("Latest GPS data not found.");
        }
        LatestGpsCacheDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(target, latestGpsCacheDo, ignoreProperties.toArray(new String[0]));
        return mapper.doToDomain(jpaDao.save(latestGpsCacheDo));
    }
}