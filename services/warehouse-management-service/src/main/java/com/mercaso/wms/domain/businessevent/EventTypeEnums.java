package com.mercaso.wms.domain.businessevent;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.application.dto.event.AccountPreferenceCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.AccountPreferenceUpdatedPayloadDto;
import com.mercaso.wms.application.dto.event.BatchCreatedApplicationEvent;
import com.mercaso.wms.application.dto.event.BatchCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.BusinessEventPayloadDto;
import com.mercaso.wms.application.dto.event.InventoryStockAvailablePayloadDto;
import com.mercaso.wms.application.dto.event.InventoryStockCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.InventoryStockUnavailablePayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskAssignedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskCanceledPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskCompletedApplicationEvent;
import com.mercaso.wms.application.dto.event.PickingTaskCompletedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskFailedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskPartiallyCompletedApplicationEvent;
import com.mercaso.wms.application.dto.event.PickingTaskPartiallyCompletedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskPickedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskReassignedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskStartedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskUnassignedPayloadDto;
import com.mercaso.wms.application.dto.event.ReceivingTaskCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.ReceivingTaskReceivedApplicationEvent;
import com.mercaso.wms.application.dto.event.ReceivingTaskReceivedPayloadDto;
import com.mercaso.wms.application.dto.event.ReceivingTaskStartedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderCanceledPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderDriverValidatedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderPickedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderStartedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderSyncedToShopifyPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderUpdatedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderValidatedApplicationEvent;
import com.mercaso.wms.application.dto.event.ShippingOrderValidatedPayloadDto;
import com.mercaso.wms.application.dto.event.TransferTaskActivatedPayloadDto;
import com.mercaso.wms.application.dto.event.TransferTaskCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.TransferTaskLoadedPayloadDto;
import com.mercaso.wms.application.dto.event.TransferTaskReceivedApplicationEvent;
import com.mercaso.wms.application.dto.event.TransferTaskReceivedPayloadDto;
import com.mercaso.wms.application.dto.event.WmsExceptionAlertApplicationEvent;
import com.mercaso.wms.application.dto.event.WmsExceptionAlertPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderArrivedPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredApplicationEvent;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderInTransitPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderUnloadedPayloadDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderUpdatedPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskBuildApplicationEvent;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskBuildPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskCompletedPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskInProgressPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskReassignedApplicationEvent;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskReassignedPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskUpdatedPayloadDto;
import com.mercaso.wms.infrastructure.event.applicationevent.BaseApplicationEvent;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnums {

    BATCH_CREATED(BatchCreatedPayloadDto.class, BatchCreatedApplicationEvent.class, List.of(EntityEnums.BATCH)),
    PICKING_TASK_ASSIGNED(PickingTaskAssignedPayloadDto.class, null, List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_UNASSIGNED(PickingTaskUnassignedPayloadDto.class, null, List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_REASSIGNED(PickingTaskReassignedPayloadDto.class, null, List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_STARTED(PickingTaskStartedPayloadDto.class, null, List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_PICKED(PickingTaskPickedPayloadDto.class, null, List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_COMPLETED(PickingTaskCompletedPayloadDto.class,
        PickingTaskCompletedApplicationEvent.class,
        List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_CANCELLED(PickingTaskCanceledPayloadDto.class, null, List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_FAILED(PickingTaskFailedPayloadDto.class, null, List.of(EntityEnums.PICKING_TASK)),
    PICKING_TASK_PARTIALLY_COMPLETED(PickingTaskPartiallyCompletedPayloadDto.class,
        PickingTaskPartiallyCompletedApplicationEvent.class,
        List.of(EntityEnums.PICKING_TASK)),
    INVENTORY_STOCK_CREATED(InventoryStockCreatedPayloadDto.class, null, List.of(EntityEnums.INVENTORY_STOCK)),
    INVENTORY_STOCK_AVAILABLE(InventoryStockAvailablePayloadDto.class, null, List.of(EntityEnums.INVENTORY_STOCK)),
    INVENTORY_STOCK_UNAVAILABLE(InventoryStockUnavailablePayloadDto.class, null, List.of(EntityEnums.INVENTORY_STOCK)),
    SHIPPING_ORDER_CREATED(ShippingOrderCreatedPayloadDto.class, null, List.of(EntityEnums.SHIPPING_ORDER)),
    SHIPPING_ORDER_CANCELED(ShippingOrderCanceledPayloadDto.class, null, List.of(EntityEnums.SHIPPING_ORDER)),
    SHIPPING_ORDER_UPDATED(ShippingOrderUpdatedPayloadDto.class, null, List.of(EntityEnums.SHIPPING_ORDER)),
    SHIPPING_ORDER_STARTED(ShippingOrderStartedPayloadDto.class, null, List.of(EntityEnums.SHIPPING_ORDER)),
    SHIPPING_ORDER_PICKED(ShippingOrderPickedPayloadDto.class, null, List.of(EntityEnums.SHIPPING_ORDER)),
    SHIPPING_ORDER_VALIDATED(ShippingOrderValidatedPayloadDto.class,
        ShippingOrderValidatedApplicationEvent.class,
        List.of(EntityEnums.SHIPPING_ORDER)),
    SHIPPING_ORDER_DRIVER_VALIDATED(ShippingOrderDriverValidatedPayloadDto.class, null, List.of(EntityEnums.SHIPPING_ORDER)),
    SHIPPING_ORDER_SYNCED_TO_SHOPIFY(ShippingOrderSyncedToShopifyPayloadDto.class, null, List.of(EntityEnums.SHIPPING_ORDER)),

    //receiving task
    RECEIVING_TASK_CREATED(ReceivingTaskCreatedPayloadDto.class, null, List.of(EntityEnums.RECEIVING_TASK)),
    RECEIVING_TASK_STARTED(ReceivingTaskStartedPayloadDto.class, null, List.of(EntityEnums.RECEIVING_TASK)),
    RECEIVING_TASK_RECEIVED(ReceivingTaskReceivedPayloadDto.class,
        ReceivingTaskReceivedApplicationEvent.class,
        List.of(EntityEnums.RECEIVING_TASK)),

    //picker preference
    PICKER_PREFERENCE_CREATED(AccountPreferenceCreatedPayloadDto.class, null, List.of(EntityEnums.ACCOUNT_PREFERENCE)),
    PICKER_PREFERENCE_UPDATED(AccountPreferenceUpdatedPayloadDto.class, null, List.of(EntityEnums.ACCOUNT_PREFERENCE)),

    //transfer task
    TRANSFER_TASK_CREATED(TransferTaskCreatedPayloadDto.class, null, List.of(EntityEnums.TRANSFER_TASK)),
    TRANSFER_TASK_ACTIVATED(TransferTaskActivatedPayloadDto.class, null, List.of(EntityEnums.TRANSFER_TASK)),
    TRANSFER_TASK_LOADED(TransferTaskLoadedPayloadDto.class, null, List.of(EntityEnums.TRANSFER_TASK)),
    TRANSFER_TASK_RECEIVED(TransferTaskReceivedPayloadDto.class,
        TransferTaskReceivedApplicationEvent.class,
        List.of(EntityEnums.TRANSFER_TASK)),

    //delivery order
    DELIVERY_ORDER_IN_TRANSIT(DeliveryOrderInTransitPayloadDto.class, null, List.of(EntityEnums.DELIVERY_ORDER)),
    DELIVERY_ORDER_ARRIVED(DeliveryOrderArrivedPayloadDto.class, null, List.of(EntityEnums.DELIVERY_ORDER)),
    DELIVERY_ORDER_UNLOADED(DeliveryOrderUnloadedPayloadDto.class, null, List.of(EntityEnums.DELIVERY_ORDER)),
    DELIVERY_ORDER_DELIVERED(DeliveryOrderDeliveredPayloadDto.class,
        DeliveryOrderDeliveredApplicationEvent.class,
        List.of(EntityEnums.DELIVERY_ORDER)),
    DELIVERY_ORDER_UPDATED(DeliveryOrderUpdatedPayloadDto.class, null, List.of(EntityEnums.DELIVERY_ORDER)),

    //delivery task
    DELIVERY_TASK_IN_PROGRESS(DeliveryTaskInProgressPayloadDto.class, null, List.of(EntityEnums.DELIVERY_TASK)),
    DELIVERY_TASK_COMPLETED(DeliveryTaskCompletedPayloadDto.class, null, List.of(EntityEnums.DELIVERY_TASK)),
    DELIVERY_TASK_REASSIGNED(DeliveryTaskReassignedPayloadDto.class,
        DeliveryTaskReassignedApplicationEvent.class,
        List.of(EntityEnums.DELIVERY_TASK)),
    DELIVERY_TASK_BUILD(DeliveryTaskBuildPayloadDto.class,
        DeliveryTaskBuildApplicationEvent.class,
        List.of(EntityEnums.DELIVERY_TASK)),
    DELIVERY_TASK_UPDATED(DeliveryTaskUpdatedPayloadDto.class, null, List.of(EntityEnums.DELIVERY_TASK)),

    //wms exception alert
    WMS_EXCEPTION_ALERT(WmsExceptionAlertPayloadDto.class,
        WmsExceptionAlertApplicationEvent.class,
        List.of(EntityEnums.SHIPPING_ORDER));

    private final Class<? extends BusinessEventPayloadDto<? extends BaseDto>> payloadClass;

    private final Class<? extends BaseApplicationEvent<? extends BusinessEventPayloadDto<? extends BaseDto>>> eventClass;

    private final List<EntityEnums> entities;

    public static EventTypeEnums forPayload(Class<?> payloadClass) {
        for (EventTypeEnums eventType : values()) {
            if (eventType.getPayloadClass() == payloadClass) {
                return eventType;
            }
        }

        return null;
    }

}
