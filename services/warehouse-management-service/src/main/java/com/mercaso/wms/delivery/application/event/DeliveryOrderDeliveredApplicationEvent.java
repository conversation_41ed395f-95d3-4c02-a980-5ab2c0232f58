package com.mercaso.wms.delivery.application.event;

import com.mercaso.wms.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class DeliveryOrderDeliveredApplicationEvent extends BaseApplicationEvent<DeliveryOrderDeliveredPayloadDto> {

    public DeliveryOrderDeliveredApplicationEvent(Object source, DeliveryOrderDeliveredPayloadDto payload) {
        super(source, payload);
    }
}
