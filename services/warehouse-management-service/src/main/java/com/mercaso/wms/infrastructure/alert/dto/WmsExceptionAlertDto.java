package com.mercaso.wms.infrastructure.alert.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * WMS Alert Event DTO for creating and managing WMS alert events
 * Currently focused on ORDER_AFTER_BATCH_CREATION scenario but designed for extensibility
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WmsExceptionAlertDto extends BaseDto {

    private AlertEventType alertEventType;
    private AlertSeverity severity;
    private String title;
    private String description;
    private UUID entityId;
    private EntityEnums entityType;
    private Map<String, Object> contextData;
    private Instant detectedAt;
    private String actionRequired;

    /**
     * Creates an alert for order created after batch scenario
     */
    public static WmsExceptionAlertDto createOrderAfterBatchAlert(
        String orderNumber,
        UUID orderId,
        String batchNumber,
        UUID batchId,
        LocalDate deliveryDate,
        String orderDetailUrl) {

        Map<String, Object> context = Map.of(
            "orderNumber", orderNumber,
            "batchNumber", batchNumber,
            "batchId", batchId.toString(),
            "deliveryDate", deliveryDate.toString()
        );

        return WmsExceptionAlertDto.builder()
            .alertEventType(AlertEventType.ORDER_AFTER_BATCH_CREATION)
            .severity(AlertSeverity.CRITICAL)
            .title("Order Created After Batch")
            .description(String.format(
                "Order *%s* was *created after batch* %s for delivery date *%s*",
                orderNumber, batchNumber, deliveryDate))
            .entityId(orderId)
            .entityType(EntityEnums.SHIPPING_ORDER)
            .contextData(context)
            .detectedAt(DateUtils.getNowInLA())
            .actionRequired(
                "Review order scheduling and batch assignment process. Order detail: " + orderDetailUrl)
            .build();
    }

    /**
     * Creates an alert for picking tasks incomplete after 8PM LA time
     */
    public static WmsExceptionAlertDto createPickingTasksIncompleteAlert(
        UUID batchId,
        String batchNumber,
        String vendorName,
        String source,
        String deliveryDate,
        String taskListUrl) {

        Map<String, Object> context = Map.of(
            "batchId", batchId.toString(),
            "batchNumber", batchNumber != null ? batchNumber : "N/A",
            "source", source,
            "deliveryDate", deliveryDate,
            "alertTime", DateUtils.getNowInLA().toString()
        );

        return WmsExceptionAlertDto.builder()
            .alertEventType(AlertEventType.PICKING_TASKS_INCOMPLETE_AFTER_8PM)
            .severity(AlertSeverity.HIGH)
            .title("Picking Tasks Incomplete After 8PM")
            .description(String.format(
                "Batch *%s* has incomplete picking tasks* for vendor %s after 8PM LA time",
                batchNumber != null ? batchNumber : batchId.toString(), vendorName))
            .entityId(batchId)
            .entityType(EntityEnums.BATCH)
            .contextData(context)
            .detectedAt(DateUtils.getNowInLA())
            .actionRequired(
                "Complete remaining picking tasks immediately or reschedule for next day. Task detail:" + taskListUrl)
            .build();
    }

    /**
     * Creates an alert for ghost inventory cleanup failure
     */
    public static WmsExceptionAlertDto createGhostInventoryCleanupFailureAlert(
        UUID deliveryOrderId,
        String orderNumber,
        String deliveryItemsSummary,
        String errorMessage) {

        Map<String, Object> context = new HashMap<>();
        context.put("deliveryOrderId", deliveryOrderId.toString());
        context.put("orderNumber", orderNumber != null ? orderNumber : "N/A");
        context.put("deliveryItems", deliveryItemsSummary != null ? deliveryItemsSummary : "NO_ITEMS");
        context.put("errorMessage", errorMessage != null ? errorMessage : "Unknown error");
        context.put("alertTime", DateUtils.getNowInLA().toString());
        context.put("requiresRecovery", true);

        return WmsExceptionAlertDto.builder()
            .alertEventType(AlertEventType.GHOST_INVENTORY_CLEANUP_FAILED)
            .severity(AlertSeverity.HIGH)
            .title("Ghost Inventory Cleanup Failed")
            .description(String.format(
                "Failed to clean ghost inventory for delivered order *%s* (ID: %s). Manual recovery required.",
                orderNumber != null ? orderNumber : "N/A",
                deliveryOrderId))
            .entityId(deliveryOrderId)
            .entityType(EntityEnums.DELIVERY_ORDER)
            .contextData(context)
            .detectedAt(DateUtils.getNowInLA())
            .actionRequired(
                "Manual inventory reconciliation required. Check delivery order status and update inventory records accordingly.")
            .build();
    }

    /**
     * Creates an alert for shipping order fulfilled but not delivered scenario
     */
    public static WmsExceptionAlertDto createShippingOrderFulfilledButNotDeliveredAlert(
        UUID shippingOrderId,
        String orderNumber,
        String fulfillmentStatus,
        String facilityName,
        String orderDetailUrl) {

        Map<String, Object> context = new HashMap<>();
        context.put("orderNumber", orderNumber != null ? orderNumber : "N/A");
        context.put("fulfillmentStatus", fulfillmentStatus != null ? fulfillmentStatus : "N/A");
        context.put("facilityName", facilityName != null ? facilityName : "N/A");

        return WmsExceptionAlertDto.builder()
            .alertEventType(AlertEventType.SHIPPING_ORDER_FULFILLED_BUT_NOT_DELIVERED)
            .entityId(shippingOrderId)
            .entityType(EntityEnums.SHIPPING_ORDER)
            .severity(AlertSeverity.HIGH)
            .title("Shipping Order Fulfilled but Not Delivered")
            .description(String.format(
                "Order *%s* is marked as (*%s*) at *%s* but *not delivered*.",
                orderNumber != null ? orderNumber : "N/A",
                fulfillmentStatus != null ? fulfillmentStatus : "N/A",
                "%s( LA)".formatted(DateUtils.getNowInLA())))
            .contextData(context)
            .detectedAt(DateUtils.getNowInLA())
            .actionRequired(
                "Confirm delivery status → Manual clean SHIP-SB to 0 for each sku picked in the order,order detail: %s".formatted(
                    orderDetailUrl))
            .build();
    }


    @Getter
    public enum AlertEventType {
        ORDER_AFTER_BATCH_CREATION("ORDER_AFTER_BATCH_CREATION", "Order Created After Batch"),
        PICKING_TASKS_INCOMPLETE_AFTER_8PM("PICKING_TASKS_INCOMPLETE_AFTER_8PM", "Picking Tasks Incomplete After 8PM"),
        GHOST_INVENTORY_CLEANUP_FAILED("GHOST_INVENTORY_CLEANUP_FAILED", "Ghost Inventory Cleanup Failed"),
        SHIPPING_ORDER_FULFILLED_BUT_NOT_DELIVERED("SHIPPING_ORDER_FULFILLED_BUT_NOT_DELIVERED",
            "Shipping Order Fulfilled but Not Delivered");
        // Future alert types can be added here:
        // BATCH_PROCESSING_FAILURE("BATCH_PROCESSING_FAILURE", "Batch Processing Failure"),
        // INVENTORY_SHORTAGE("INVENTORY_SHORTAGE", "Inventory Shortage");

        private final String value;
        private final String displayName;

        AlertEventType(String value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }
    }

    @Getter
    public enum AlertSeverity {
        CRITICAL("CRITICAL", "Critical", "🔴"),
        HIGH("HIGH", "High", "🟠"),
        MEDIUM("MEDIUM", "Medium", "🟡"),
        LOW("LOW", "Low", "🟢");

        private final String value;
        private final String displayName;
        private final String emoji;

        AlertSeverity(String value, String displayName, String emoji) {
            this.value = value;
            this.displayName = displayName;
            this.emoji = emoji;
        }
    }

    @Getter
    public enum AlertEntityType {
        SHIPPING_ORDER("SHIPPING_ORDER", "Shipping Order"),
        BATCH("BATCH", "Batch"),
        INVENTORY("INVENTORY", "Inventory"),
        WAREHOUSE("WAREHOUSE", "Warehouse");

        private final String value;
        private final String displayName;

        AlertEntityType(String value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }
    }
} 