package com.mercaso.wms.interfaces.query;

import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.application.queryservice.InventoryStockQueryService;
import com.mercaso.wms.application.command.inventorystock.QueryInventoryStockCommand;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/query/inventory-stocks")
@RequiredArgsConstructor
public class QueryInventoryStockResource {

    private final InventoryStockQueryService inventoryStockQueryService;

    @PreAuthorize("hasAuthority('wms:read:inventory-stocks')")
    @GetMapping("/{id}")
    public InventoryStockDto findById(@PathVariable UUID id) {
        return inventoryStockQueryService.findById(id);
    }

    @PreAuthorize("hasAuthority('wms:read:inventory-stocks')")
    @GetMapping("/{warehouseId}/by-sku-numbers")
    public Map<String, List<InventoryStockDto>> findByWarehouseIdAndSkuNumbers(@PathVariable UUID warehouseId,
        @RequestParam List<String> skuNumbers) {
        return inventoryStockQueryService.findByWarehouseIdAndSkuNumbers(warehouseId, skuNumbers);
    }

    @PreAuthorize("hasAuthority('wms:read:inventory-stocks')")
    @GetMapping("/{warehouseId}/by-picking-task-items")
    public Map<String, List<InventoryStockDto>> findByWarehouseIdAndPickingTaskItemIds(@PathVariable UUID warehouseId,
        @RequestParam List<UUID> pickingTaskItemIds) {
        return inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(warehouseId, pickingTaskItemIds);
    }

    @PreAuthorize("hasAuthority('wms:read:inventory-stocks')")
    @PostMapping("/{warehouseId}/by-picking-task-items")
    public Map<String, List<InventoryStockDto>> findByWarehouseIdAndPickingTaskItemIdsPost(
        @PathVariable UUID warehouseId,
        @Valid @RequestBody QueryInventoryStockCommand command) {
        return inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId, command.getPickingTaskItemIds());
    }

}
