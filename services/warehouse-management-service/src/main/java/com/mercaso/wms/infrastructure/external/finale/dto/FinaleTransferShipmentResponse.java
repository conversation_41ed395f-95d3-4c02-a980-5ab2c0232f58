package com.mercaso.wms.infrastructure.external.finale.dto;

import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FinaleTransferShipmentResponse {

    private String shipmentId;

    private String shipmentIdUser;

    private String shipmentUrl;

    private String shipmentTypeId;

    private String actionUrlCancel;

    private String actionUrlPack;

    private String actionUrlReceive;

    private String actionUrlShip;

    private String statusId;

    private String lastUpdatedDate;

    private String createdDate;

    private String receiveDate;

    private String shipDate;

    private List<ShipmentItemResponse> shipmentItemList;

    private List<StatusHistory> statusIdHistoryList;

    private List<String> invalidations;

    @Data
    public static class ShipmentItemResponse {
        private String facilityUrl;

        private String facilityUrlReceive;

        private String productId;

        private String productUrl;

        private int quantity;
    }

    @Data
    public static class StatusHistory {
        private String statusId;

        private long txStamp;

        private String userLoginUrl;

    }

}
