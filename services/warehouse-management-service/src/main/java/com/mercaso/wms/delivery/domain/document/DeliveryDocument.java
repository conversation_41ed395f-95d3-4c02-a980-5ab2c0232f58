package com.mercaso.wms.delivery.domain.document;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.delivery.application.command.DeliveryUploadDocumentCommand;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class DeliveryDocument extends BaseDomain {

    private final UUID id;

    private UUID entityId;

    private String entityName;

    private DeliveryDocumentType documentType;

    private String createdUserName;

    private String fileName;

    public DeliveryDocument createDeliveryOrderDocument(DeliveryUploadDocumentCommand command, String fileName) {
        this.entityId = command.getEntityId();
        this.entityName = command.getEntityName() != null ? command.getEntityName().name() : EntityEnums.DELIVERY_ORDER.name();
        this.documentType = command.getDocumentType();
        this.fileName = fileName;
        this.createdUserName = SecurityContextUtil.getUsername();
        return this;
    }

}
