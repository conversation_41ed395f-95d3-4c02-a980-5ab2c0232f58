package com.mercaso.wms.application.service;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.application.command.batch.CreateBatchCommand;
import com.mercaso.wms.application.dto.BatchDto;
import com.mercaso.wms.application.dto.event.BatchCreatedPayloadDto;
import com.mercaso.wms.application.mapper.batch.BatchDtoApplicationMapper;
import com.mercaso.wms.application.mapper.batch.CreateBatchCommandApplicationMapper;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class BatchApplicationService {

    private final BatchRepository batchRepository;

    private final BusinessEventDispatcher businessEventDispatcher;

    public BatchDto createBatch(CreateBatchCommand command) {
        Batch batch = CreateBatchCommandApplicationMapper.INSTANCE.commandToDomain(command);
        batch.create();
        Batch saved = batchRepository.save(batch);

        BatchDto batchDto = BatchDtoApplicationMapper.INSTANCE.domainToDto(saved);
        businessEventDispatcher.dispatch(BusinessEventFactory.build(BatchCreatedPayloadDto.builder()
            .batchId(batchDto.getId())
            .data(batchDto)
            .build()));
        return batchDto;
    }

}
