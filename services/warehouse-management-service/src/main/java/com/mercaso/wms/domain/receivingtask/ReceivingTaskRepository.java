package com.mercaso.wms.domain.receivingtask;

import com.mercaso.wms.application.dto.view.SearchReceivingTaskView;
import com.mercaso.wms.application.query.ReceivingTaskQuery;
import com.mercaso.wms.domain.BaseDomainRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ReceivingTaskRepository extends BaseDomainRepository<ReceivingTask, UUID> {

    List<ReceivingTask> findByBatchId(UUID id);

    Page<SearchReceivingTaskView> search(ReceivingTaskQuery receivingTaskQuery, Pageable pageable);

    List<ReceivingTask> saveAll(List<ReceivingTask> receivingTasks);

    void deleteAll();

    List<ReceivingTask> findByDeliveryDateAndStatus(String deliveryDate, ReceivingTaskStatus status);

    void deleteByIds(List<UUID> ids);

    List<ReceivingTask> findByBatchIdAndStatusNotIn(UUID batchId, List<ReceivingTaskStatus> statuses);

    List<ReceivingTask> findByIds(List<UUID> ids);
}
