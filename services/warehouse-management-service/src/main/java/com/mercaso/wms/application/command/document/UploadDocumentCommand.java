package com.mercaso.wms.application.command.document;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.document.enums.DocumentType;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class UploadDocumentCommand extends BaseCommand {

    @NotNull
    private UUID entityId;
    @NotNull
    private EntityEnums entityName;
    @NotNull
    private DocumentType documentType;

    private List<String> clientFileIds;
} 