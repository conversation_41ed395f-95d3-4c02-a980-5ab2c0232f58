package com.mercaso.wms.delivery.infrastructure.external.routemanage.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.Data;

@Data
public class Route {

    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @JsonProperty("id")
    private String id;

    @JsonProperty("date")
    private String date;

    @JsonProperty("vehicleId")
    private String vehicleId;

    @JsonProperty("driverId")
    private String driverId;

    @JsonProperty("steps")
    private List<Step> steps;

    /**
     * Override the default getter to return date in yyyy-MM-dd format
     * If the date is in yyyyMMdd format (like 20250403), it will be converted to 2025-04-03
     *
     * @return formatted date string or original date if conversion fails
     */
    public String getDate() {
        if (date == null || date.isEmpty()) {
            return date;
        }

        try {
            LocalDate parsedDate = LocalDate.parse(date, INPUT_FORMATTER);
            return parsedDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        } catch (Exception e) {
            // Return original date if parsing fails
            return date;
        }
    }

    /**
     * Get the original unformatted date
     *
     * @return the original date string
     */
    public String getOriginalDate() {
        return date;
    }
}
