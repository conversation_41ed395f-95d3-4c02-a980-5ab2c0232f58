package com.mercaso.wms.infrastructure.config;

import com.mercaso.ims.client.ApiClient;
import com.mercaso.ims.client.api.QueryItemRestApiApi;
import com.mercaso.ims.client.api.QueryVendorRestApiApi;
import com.mercaso.ims.client.api.SearchItemV2RestApiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class ImsConfig {

    @Value("${mercaso.ims-url}")
    private String imsHost;

    @Bean
    public QueryItemRestApiApi queryImsControllerApi(RestTemplate restTemplate) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(imsHost);
        return new QueryItemRestApiApi(apiClient);
    }

    @Bean
    public SearchItemV2RestApiApi searchImsItemControllerApi(RestTemplate restTemplate) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(imsHost);
        return new SearchItemV2RestApiApi(apiClient);
    }

    @Bean
    public QueryVendorRestApiApi queryVendorRestApi(RestTemplate restTemplate) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(imsHost);
        return new QueryVendorRestApiApi(apiClient);
    }

}
