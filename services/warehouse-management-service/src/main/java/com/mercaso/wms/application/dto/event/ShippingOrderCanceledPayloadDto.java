package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ShippingOrderCanceledPayloadDto extends BusinessEventPayloadDto<ShippingOrderDto> {

    private UUID shippingOrderId;

    @Builder
    public ShippingOrderCanceledPayloadDto(ShippingOrderDto data, UUID shippingOrderId) {
        super(data);
        this.shippingOrderId = shippingOrderId;
    }

}
