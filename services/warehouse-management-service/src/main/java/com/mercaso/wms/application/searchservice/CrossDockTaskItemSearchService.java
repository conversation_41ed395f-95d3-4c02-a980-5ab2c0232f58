package com.mercaso.wms.application.searchservice;

import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.application.query.CrossDockTaskItemQuery;
import com.mercaso.wms.infrastructure.repository.crossdockitem.CrossDockTaskItemJdbcTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class CrossDockTaskItemSearchService {

    private final CrossDockTaskItemJdbcTemplate crossDockTaskItemJdbcTemplate;

    public Page<SearchCrossDockTaskItemView> searchCrossDockTaskItems(CrossDockTaskItemQuery query, Sort sort) {
        return crossDockTaskItemJdbcTemplate.search(query, sort);
    }
} 