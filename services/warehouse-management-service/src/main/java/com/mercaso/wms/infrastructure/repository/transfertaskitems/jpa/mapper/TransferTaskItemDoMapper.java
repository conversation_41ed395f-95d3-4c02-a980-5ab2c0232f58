package com.mercaso.wms.infrastructure.repository.transfertaskitems.jpa.mapper;

import com.mercaso.wms.domain.transfertaskitem.TransferTaskItem;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import com.mercaso.wms.infrastructure.repository.transfertaskitems.jpa.dataobject.TransferTaskItemDo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TransferTaskItemDoMapper extends BaseDoMapper<TransferTaskItemDo, TransferTaskItem> {

    @Mapping(target = "transferTaskId", source = "transferTask.id")
    TransferTaskItem doToDomain(TransferTaskItemDo dataObject);

    @Mapping(target = "transferTask.id", source = "transferTaskId")
    TransferTaskItemDo domainToDo(TransferTaskItem domain);

}
