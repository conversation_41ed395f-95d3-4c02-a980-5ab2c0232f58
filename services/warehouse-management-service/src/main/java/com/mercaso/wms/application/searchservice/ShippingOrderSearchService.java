package com.mercaso.wms.application.searchservice;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.mercaso.wms.application.dto.PickedShippingOrderItemExportDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.view.SearchShippingOrderView;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.shippingorder.criteria.ShippingOrderSearchCriteria;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.ShippingOrderJdbcTemplate;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class ShippingOrderSearchService {

    private final ShippingOrderRepository shippingOrderRepository;

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper;

    private final ShippingOrderJdbcTemplate shippingOrderJdbcTemplate;


    public Page<ShippingOrderDto> searchShippingOrders(ShippingOrderQuery shippingOrderQuery, Pageable pageable) {
        ShippingOrderSearchCriteria criteria = new ShippingOrderSearchCriteria();
        BeanUtils.copyProperties(shippingOrderQuery, criteria);

        if (!CollectionUtils.isEmpty(shippingOrderQuery.getStatuses())) {
            criteria.setStatuses(shippingOrderQuery.getStatuses().stream()
                .map(ShippingOrderStatus::valueOf)
                .toList());
        }

        return shippingOrderRepository.findShippingOrderList(criteria, pageable)
            .map(shippingOrderDtoApplicationMapper::domainToDto);
    }

    public ByteArrayOutputStream pickedShippingOrderItemsExport(String deliveryDate) {
        List<PickedShippingOrderItemExportDto> items = shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(
            deliveryDate);

        if (items != null && !items.isEmpty()) {
            items.forEach(item -> item.setFilled(item.getFulfilledQty()));
        }

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            return writeTemplate(outputStream, items);
        } catch (Exception e) {
            log.error("[pickedShippingOrderItemsExport] Failed to export picked shipping order items.", e);
            throw new WmsBusinessException("[pickedShippingOrderItemsExport] Failed to export picked shipping order items.", e);
        }
    }

    public ByteArrayOutputStream writeTemplate(ByteArrayOutputStream outputStream, List<PickedShippingOrderItemExportDto> items) {
        Resource resource = new ClassPathResource("template/Picked_Shipping_Order_Items_Template.xlsx");
        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream)
            .withTemplate(resource.getInputStream())
            .build()) {
            excelWriter.fill(items, EasyExcelFactory.writerSheet().sheetName("Picked Shipping Order Items").build());
        } catch (IOException e) {
            throw new WmsBusinessException("[pickedShippingOrderItemsExport] Failed to write picked shipping order items.", e);
        }
        return outputStream;
    }

    public Page<SearchShippingOrderView> search(ShippingOrderQuery query, Pageable pageable) {
        log.debug("Searching shipping orders V2 with query: {}", query);
        return shippingOrderJdbcTemplate.search(query, pageable);
    }
}
