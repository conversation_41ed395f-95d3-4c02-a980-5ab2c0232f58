package com.mercaso.wms.domain.document;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.document.UploadDocumentCommand;
import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.document.enums.DocumentType;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class Document extends BaseDomain {

    private final UUID id;

    private UUID entityId;

    private String entityName;

    private DocumentType documentType;

    private String createdUserName;

    private String fileName;

    public Document createDocument(UploadDocumentCommand command, String fileName) {
        this.entityId = command.getEntityId();
        this.entityName = command.getEntityName() != null ? command.getEntityName().name() : EntityEnums.SHIPPING_ORDER.name();
        this.documentType = command.getDocumentType();
        this.fileName = fileName;
        this.createdUserName = SecurityContextUtil.getUsername();
        return this;
    }
} 