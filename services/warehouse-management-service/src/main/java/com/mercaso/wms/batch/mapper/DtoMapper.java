package com.mercaso.wms.batch.mapper;

import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.LookupDto;
import com.mercaso.wms.domain.batchitem.BatchItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DtoMapper {

    DtoMapper INSTANCE = Mappers.getMapper(DtoMapper.class);

    @Mapping(target = "orderNumber", ignore = true)
    @Mapping(target = "pack", ignore = true)
    @Mapping(target = "brand", ignore = true)
    @Mapping(target = "aisle", ignore = true)
    @Mapping(target = "from", ignore = true)
    @Mapping(target = "sourceEnum", ignore = true)
    @Mapping(target = "itemNumber", source = "skuNumber")
    @Mapping(target = "itemDescription", source = "title")
    LookupDto toLookupDto(ItemCategoryDto itemCategoryDto);

    @Mapping(source = "itemNumber", target = "skuNumber")
    @Mapping(source = "itemDescription", target = "title")
    @Mapping(source = "quantity", target = "expectQty")
    @Mapping(source = "pos", target = "breakdownName")
    @Mapping(source = "from", target = "locationName")
    BatchItem toBatchItem(ExcelBatchDto excelBatchDto);
}