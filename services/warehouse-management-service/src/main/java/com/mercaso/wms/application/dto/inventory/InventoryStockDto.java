package com.mercaso.wms.application.dto.inventory;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.application.dto.LocationDto;
import com.mercaso.wms.application.dto.WarehouseDto;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InventoryStockDto extends BaseDto {

    private UUID id;

    private WarehouseDto warehouse;

    private String skuNumber;

    private String title;

    private UUID itemId;

    private LocationDto location;

    private String lotNumber;

    private LocalDate productionDate;

    private LocalDate expirationDate;

    private BigDecimal qty;

    private BigDecimal reservedQty;

    private BigDecimal availableQty;

    private String status;

    private String lpnNumber;

    private UUID vendorId;

    private String createdBy;

    private Instant createdAt;

    private String updatedBy;

    private Instant updatedAt;

    private String locationType;

    private List<String> detail;

    private Integer qoh;
}
