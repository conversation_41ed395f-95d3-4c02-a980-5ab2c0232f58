package com.mercaso.wms.interfaces;

import com.mercaso.wms.application.command.receivingtask.BatchUpdateReceivedQtyCommand;
import com.mercaso.wms.application.command.receivingtask.BatchUpdateVendorCommand;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.application.searchservice.ReceivingTaskSearchService;
import com.mercaso.wms.application.service.ReceivingTaskApplicationService;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/receiving-tasks")
@RequiredArgsConstructor
public class ReceivingTaskResource {

    private final ReceivingTaskApplicationService receivingTaskApplicationService;

    private final ReceivingTaskSearchService receivingTaskSearchService;

    @PreAuthorize("hasAuthority('wms:write:receiving-tasks')")
    @PutMapping("/scan-receive/{receivingTaskItemId}")
    public ReceivingTaskItemDto scanReceiveItem(@PathVariable UUID receivingTaskItemId) {
        return receivingTaskApplicationService.scanReceiveItem(receivingTaskItemId);
    }

    @PreAuthorize("hasAuthority('wms:write:receiving-tasks')")
    @PutMapping("/{receivingTaskId}/receive")
    public ReceivingTaskDto receivedTask(@PathVariable UUID receivingTaskId) {
        return receivingTaskApplicationService.receive(receivingTaskId);
    }

    @PreAuthorize("hasAuthority('wms:write:receiving-tasks')")
    @PutMapping
    public ReceivingTaskDto updateReceivingTask(@RequestBody UpdateReceivingTaskCommand command) {
        return receivingTaskApplicationService.updateReceivingTask(command);
    }

    @PreAuthorize("hasAuthority('wms:write:receiving-tasks')")
    @PutMapping("/batch-update-received-qty")
    public ReceivingTaskDto batchUpdateReceivedQty(@RequestBody BatchUpdateReceivedQtyCommand command) {
        return receivingTaskApplicationService.batchUpdateReceivedQty(command);
    }

    @PreAuthorize("hasAuthority('wms:write:receiving-tasks')")
    @PutMapping("/batch/update-vendor")
    public List<ReceivingTaskDto> batchUpdateVendor(@RequestBody BatchUpdateVendorCommand command) {
        return receivingTaskApplicationService.batchUpdateVendor(command);
    }

    @PreAuthorize("hasAuthority('wms:read:receiving-tasks')")
    @GetMapping("/{deliveryDate}/received-items-export")
    public void pickedItemsExport(@PathVariable(value = "deliveryDate") String deliveryDate, HttpServletResponse response)
        throws IOException {
        String fileName = URLEncoder.encode(deliveryDate + "-received-items-export", StandardCharsets.UTF_8);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.getOutputStream().write(receivingTaskSearchService.receivedItemsExport(deliveryDate).toByteArray());
    }

}
