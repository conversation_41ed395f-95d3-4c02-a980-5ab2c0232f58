package com.mercaso.wms.domain.crossdock;

import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.domain.BaseDomainRepository;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.criteria.CrossDockTaskSearchCriteria;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CrossDockTaskRepository extends BaseDomainRepository<CrossDockTask, UUID> {

    Page<SearchCrossDockTaskView> search(CrossDockTaskSearchCriteria criteria, Pageable pageable);

    List<CrossDockTask> saveAll(List<CrossDockTask> crossDockTasks);

    void deleteAll();

    CrossDockTask findByDeliveryDateAndPickerUserId(String deliveryDate, UUID pickerUserId);

}