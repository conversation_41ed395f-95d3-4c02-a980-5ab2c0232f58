package com.mercaso.wms.application.mapper.accountpreference;

import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.infrastructure.utils.RSAEncryptor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AccountPreferenceDtoApplicationMapper {

    AccountPreferenceDtoApplicationMapper INSTANCE = Mappers.getMapper(AccountPreferenceDtoApplicationMapper.class);

    @Mapping(target = "secretKey", expression = "java(buildNewSecretKey(domain, publicKey))")
    AccountPreferenceDto domainToDto(AccountPreference domain, String publicKey);

    default String buildNewSecretKey(AccountPreference domain, String publicKey) {
        if (domain != null && publicKey != null && domain.getEmail() != null && domain.getSecretKey() != null) {
            try {
                return RSAEncryptor.encrypt(domain.getEmail().concat("&&").concat(domain.getSecretKey()), publicKey);
            } catch (Exception e) {
                return null;
            }
        } else {
            return null;
        }
    }

}
