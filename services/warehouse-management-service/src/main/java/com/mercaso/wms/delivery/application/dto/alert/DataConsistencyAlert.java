package com.mercaso.wms.delivery.application.dto.alert;

import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataConsistencyAlert {

    private String alertType;
    private String title;
    private String description;
    private UUID taskId;
    private String routeId;
    private String orderNumber;
    private Integer expectedValue;
    private Integer actualValue;
    private String additionalInfo;
    private Instant detectedAt;

    @Getter
    public enum AlertType {
        TASK_COUNT_MISMATCH("TASK_COUNT_MISMATCH", "Task Count Mismatch"),
        ORDER_COUNT_MISMATCH("ORDER_COUNT_MISMATCH", "Order Count Mismatch"),
        SEQUENCE_MISMATCH("SEQUENCE_MISMATCH", "Sequence Issues"),
        MISSING_TASK("MISSING_TASK", "Missing Tasks"),
        MISSING_ROUTE("MISSING_ROUTE", "Missing Routes"),
        ORPHANED_TASK("ORPHANED_TASK", "Orphaned Tasks"),
        SYSTEM_ERROR("SYSTEM_ERROR", "System Errors");

        private final String value;
        private final String displayName;

        AlertType(String value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        public static String getDisplayName(String value) {
            for (AlertType type : AlertType.values()) {
                if (type.getValue().equals(value)) {
                    return type.getDisplayName();
                }
            }
            return value.replace("_", " ");
        }
    }
} 