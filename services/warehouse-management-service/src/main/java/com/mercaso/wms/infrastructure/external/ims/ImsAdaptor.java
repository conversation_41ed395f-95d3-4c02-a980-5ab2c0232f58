package com.mercaso.wms.infrastructure.external.ims;

import com.mercaso.ims.client.api.QueryItemRestApiApi;
import com.mercaso.ims.client.api.QueryVendorRestApiApi;
import com.mercaso.ims.client.api.SearchItemV2RestApiApi;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.ItemListDto;
import com.mercaso.ims.client.dto.ItemSerachDto;
import com.mercaso.ims.client.dto.VendorDto;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class ImsAdaptor {

    private final QueryItemRestApiApi queryItemRestApi;

    private final SearchItemV2RestApiApi searchItemRestApi;

    private final QueryVendorRestApiApi queryVendorRestApi;

    public List<ItemCategoryDto> getItemsBySkus(List<String> skuNumbers) {
        List<ItemCategoryDto> items = new LinkedList<>();
        ListUtils.partition(skuNumbers, 50).forEach(skus -> {
            List<ItemCategoryDto> itemsBySkuIn = queryItemRestApi.findItemsBySkuIn(skus);
            if (itemsBySkuIn != null) {
                items.addAll(itemsBySkuIn);
            }
        });
        return items;
    }

    public List<ItemCategoryDto> getItemsByIds(List<UUID> itemIds) {
        List<ItemCategoryDto> items = new LinkedList<>();
        ListUtils.partition(itemIds, 40).forEach(skus -> {
            List<ItemCategoryDto> itemsBySkuIn = queryItemRestApi.findItemsByIdIn(skus);
            if (itemsBySkuIn != null) {
                items.addAll(itemsBySkuIn);
            }
        });
        return items;
    }

    public ItemSerachDto searchItemsByTitle(String title) {
        if (StringUtils.isBlank(title)) {
            return null;
        }
        ItemListDto itemListDto = searchItemRestApi.searchItems(1, 20, null, "{\"title\":\"" + title + "\"}");
        if (itemListDto != null && CollectionUtils.isNotEmpty(itemListDto.getData())) {
            return itemListDto.getData().getFirst();
        }
        return null;
    }

    public VendorDto getVendorByName(String vendorName) {
        try {
            List<VendorDto> vendorDtos = queryVendorRestApi.searchVendors(vendorName);
            if (CollectionUtils.isNotEmpty(vendorDtos)) {
                return vendorDtos.getFirst();
            }
        } catch (Exception e) {
            log.error("Error searching vendor by name: {}", vendorName, e);
        }
        return null;
    }

}
