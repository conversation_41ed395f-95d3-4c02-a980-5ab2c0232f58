package com.mercaso.wms.delivery.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Configuration
@ConfigurationProperties(prefix = "wms.delivery.payment-summary-export")
@Getter
public class PaymentSummaryExportConfig {

    private int maxPageSize = 10000;

    private String templatePath = "template/Mercaso_Payment_Summary_Automation.xlsx";

    private String fileNamePrefix = "payment-summary";
    /**
     * Timeout (seconds)
     */
    private long timeoutSeconds = 300;

}