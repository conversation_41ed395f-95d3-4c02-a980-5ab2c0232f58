package com.mercaso.wms.interfaces.query;

import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.application.queryservice.AccountPreferenceQueryService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/query/account-preferences")
@RequiredArgsConstructor
public class QueryAccountPreferenceResource {

    private final AccountPreferenceQueryService accountPreferenceQueryService;

    @PreAuthorize("hasAuthority('wms:read:account-preference')")
    @GetMapping("/{id}")
    public AccountPreferenceDto findById(@PathVariable UUID id) {
        return accountPreferenceQueryService.findById(id);
    }

    @PreAuthorize("hasAuthority('wms:read:account-preference')")
    @GetMapping
    public List<AccountPreferenceDto> findBy(@RequestParam List<UUID> ids) {
        return accountPreferenceQueryService.findBy(ids);
    }

    @PreAuthorize("hasAuthority('wms:read:account-preference')")
    @GetMapping("/validate")
    public boolean findBy(@RequestParam(required = false) String userName, @RequestParam(required = false) String email) {
        return !accountPreferenceQueryService.findBy(userName, email).isEmpty();
    }

}
