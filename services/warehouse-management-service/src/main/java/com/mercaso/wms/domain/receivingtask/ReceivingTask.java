package com.mercaso.wms.domain.receivingtask;


import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.receivingtask.BatchUpdateReceivedQtyCommand;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class ReceivingTask extends BaseDomain {

    private final UUID id;

    private UUID batchId;

    private String number;

    private ReceivingTaskStatus status;

    private Instant receiveStartTime;

    private Instant receivedTime;

    private UUID receiveUserId;

    private String receiveUserName;

    private ReceivingTaskType type;

    private String vendor;

    private String createdUserName;

    private List<ReceivingTaskItem> receivingTaskItems;

    public ReceivingTask create(UUID batchId, List<BatchItem> batchItems, SourceEnum source) {
        this.batchId = batchId;
        this.status = ReceivingTaskStatus.CREATED;
        this.type = ReceivingTaskType.ONLINE_RECEIVING;
        this.vendor = source.name();
        this.createdUserName = SecurityContextUtil.getUsername();
        this.receivingTaskItems = createReceivingTaskItems(batchItems);
        return this;
    }

    private List<ReceivingTaskItem> createReceivingTaskItems(List<BatchItem> batchItems) {
        List<ReceivingTaskItem> items = new ArrayList<>();
        for (int i = 0; i < batchItems.size(); i++) {
            BatchItem batchItem = batchItems.get(i);
            ReceivingTaskItem receivingTaskItem = ReceivingTaskItem.builder()
                .receivingTaskId(this.id)
                .orderNumber(batchItem.getOrderNumber())
                .line(batchItem.getLine())
                .batchItemId(batchItem.getId())
                .itemId(batchItem.getItemId())
                .department(batchItem.getDepartment())
                .category(batchItem.getCategory())
                .skuNumber(batchItem.getSkuNumber())
                .title(batchItem.getTitle())
                .locationName(batchItem.getLocationName())
                .locationId(batchItem.getLocationId())
                .receivingSequence(i + 1)
                .expectQty(batchItem.getExpectQty())
                .receivedQty(0)
                .errorInfo(null)
                .breakdownName(batchItem.getBreakdownName())
                .shippingOrderId(batchItem.getShippingOrderId())
                .shippingOrderItemId(batchItem.getShippingOrderItemId())
                .build();
            items.add(receivingTaskItem);
        }
        return items;
    }

    public void start() {
        this.status = ReceivingTaskStatus.RECEIVING;
        this.receiveStartTime = Instant.now();
        this.receiveUserId =
            SecurityContextUtil.getLoginUserId() != null ? UUID.fromString(SecurityContextUtil.getLoginUserId()) : null;
        this.receiveUserName = SecurityContextUtil.getUsername();
    }

    public ReceivingTaskItem scanReceive(UUID receivingTaskItemId) {
        Optional<ReceivingTaskItem> receivingTaskItem = this.receivingTaskItems.stream()
            .filter(item -> item.getId().equals(receivingTaskItemId))
            .findFirst();
        receivingTaskItem.ifPresent(ReceivingTaskItem::receive);
        if (this.receivingTaskItems.stream().allMatch(item -> item.getReceivedQty() >= item.getExpectQty())) {
            this.status = ReceivingTaskStatus.RECEIVED;
            this.receivedTime = Instant.now();
        }
        return receivingTaskItem.orElse(null);
    }

    public ReceivingTask received() {
        this.status = ReceivingTaskStatus.RECEIVED;
        this.receivingTaskItems.forEach(item -> {
            if (item.getReceivedQty() == null || item.getReceivedQty() == 0) {
                item.setErrorInfo("Not received");
            } else if (item.getReceivedQty() < item.getExpectQty()) {
                item.setErrorInfo("Not fully received");
            } else {
                item.setErrorInfo(null);
            }
        });
        this.receivedTime = Instant.now();
        return this;
    }

    public ReceivingTask update(UpdateReceivingTaskCommand command) {
        this.receivingTaskItems.forEach(item -> command.getReceivingTaskItems().forEach(updateItem -> {
            if (item.getId().equals(updateItem.getReceivingTaskItemId())) {
                item.setExpectQty(updateItem.getExpectQty());
                item.setErrorInfo("Not received");
            }
        }));
        return this;
    }

    public void batchUpdateReceivedQty(BatchUpdateReceivedQtyCommand command) {
        this.receivingTaskItems.forEach(item -> command.getReceivingTaskItems().forEach(updateItem -> {
            if (item.getId().equals(updateItem.getReceivingTaskItemId())) {
                item.setReceivedQty(updateItem.getReceivedQty());
            }
        }));
        if (this.receivingTaskItems.stream().allMatch(item -> item.getReceivedQty() >= item.getExpectQty())) {
            this.status = ReceivingTaskStatus.RECEIVED;
            this.receivedTime = Instant.now();
        }
    }

    public void updateVendor(SourceEnum vendor) {
        this.vendor = vendor.name();
    }

}
