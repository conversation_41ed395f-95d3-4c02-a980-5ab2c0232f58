package com.mercaso.wms.delivery.infrastructure.repository.gps.jpa;

import com.mercaso.wms.delivery.infrastructure.repository.gps.jpa.dataobject.GpsRawDo;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface GpsRawJpaDao extends JpaRepository<GpsRawDo, UUID> {

    Optional<GpsRawDo> findByUserId(UUID userId);

    List<GpsRawDo> findByDeliveryTaskIdOrderByReportAtAsc(UUID deliveryTaskId);

    void deleteByDeliveryTaskIdIn(List<UUID> deliveryTaskIds);
} 