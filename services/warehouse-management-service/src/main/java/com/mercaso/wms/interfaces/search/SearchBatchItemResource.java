package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.application.dto.BatchItemDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.mapper.batchitem.BatchItemDtoApplicationMapper;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.domain.batchitem.BatchItem;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/search/batch-items")
@RequiredArgsConstructor
public class SearchBatchItemResource {

    private final BatchItemQueryService batchItemQueryService;

    @PreAuthorize("hasAuthority('wms:read:batch-items')")
    @GetMapping
    public Result<BatchItemDto> searchVernonBatchItems(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        @Max(value = 1000, message = "Page size must be less than or equal to 1000")
        int pageSize,
        @RequestParam(value = "source", required = false) String source,
        @RequestParam(value = "deliveryDate", required = false) String deliveryDate) {
        Page<BatchItem> batchItems = batchItemQueryService.searchBatchItems(source,
            deliveryDate,
            PageRequest.of(page - 1, pageSize));
        return new Result<>(BatchItemDtoApplicationMapper.INSTANCE.domainToDtos(batchItems.getContent()),
            batchItems.getTotalElements());
    }

}
