package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ShippingOrderValidatedApplicationEvent extends BaseApplicationEvent<ShippingOrderValidatedPayloadDto> {

    public ShippingOrderValidatedApplicationEvent(Object source, ShippingOrderValidatedPayloadDto payload) {
        super(source, payload);
    }
}
