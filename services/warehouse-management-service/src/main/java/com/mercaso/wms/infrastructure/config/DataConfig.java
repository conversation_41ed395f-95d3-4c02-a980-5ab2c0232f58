package com.mercaso.wms.infrastructure.config;

import com.mercaso.data.client.ApiClient;
import com.mercaso.data.client.api.FinaleProductControllerApi;
import com.mercaso.data.client.api.ShopifyControllerApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class DataConfig {

    @Value("${mercaso.data-url}")
    private String dataHost;

    @Bean
    public FinaleProductControllerApi finaleProductControllerApi(RestTemplate restTemplate) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(dataHost);
        return new FinaleProductControllerApi(apiClient);
    }

    @Bean
    public ShopifyControllerApi shopifyOrderControllerApi(RestTemplate restTemplate) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(dataHost);
        return new Shopify<PERSON>ontroller<PERSON>pi(apiClient);
    }

}
