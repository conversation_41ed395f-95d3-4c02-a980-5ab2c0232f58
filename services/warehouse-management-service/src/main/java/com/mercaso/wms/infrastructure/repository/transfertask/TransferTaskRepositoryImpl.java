package com.mercaso.wms.infrastructure.repository.transfertask;

import com.mercaso.wms.application.dto.view.SearchTransferTaskView;
import com.mercaso.wms.application.query.TransferTaskQuery;
import com.mercaso.wms.domain.transfertask.TransferTask;
import com.mercaso.wms.domain.transfertask.TransferTaskRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.transfertask.jpa.TransferTaskJdbcTemplate;
import com.mercaso.wms.infrastructure.repository.transfertask.jpa.TransferTaskJpaDao;
import com.mercaso.wms.infrastructure.repository.transfertask.jpa.criteria.TransferTaskSearchCriteria;
import com.mercaso.wms.infrastructure.repository.transfertask.jpa.dataobject.TransferTaskDo;
import com.mercaso.wms.infrastructure.repository.transfertask.jpa.mapper.TransferTaskDoMapper;
import com.mercaso.wms.infrastructure.repository.transfertaskitems.jpa.TransferTaskItemJpaDao;
import com.mercaso.wms.infrastructure.repository.transfertaskitems.jpa.dataobject.TransferTaskItemDo;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class TransferTaskRepositoryImpl implements TransferTaskRepository {

    private final TransferTaskJpaDao jpaDao;
    private final TransferTaskItemJpaDao transferTaskItemJpaDao;
    private final TransferTaskJdbcTemplate jdbcTemplate;
    private final TransferTaskDoMapper mapper;


    @Override
    public TransferTask save(TransferTask domain) {
        TransferTaskDo transferTaskDo = mapper.domainToDo(domain);
        transferTaskDo.getTransferTaskItems().forEach(item -> item.setTransferTask(transferTaskDo));
        return mapper.doToDomain(jpaDao.saveAndFlush(transferTaskDo));
    }

    @Override
    public TransferTask findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public TransferTask update(TransferTask domain) {
        TransferTaskDo transferTaskDo = jpaDao.findById(domain.getId())
            .orElseThrow(() -> new WmsBusinessException("transfer task not found."));
        TransferTaskDo target = mapper.domainToDo(domain);

        List<TransferTaskItemDo> needRemoveItems = new LinkedList<>();
        transferTaskDo.getTransferTaskItems().forEach(item -> {
            if (target.getTransferTaskItems().stream().noneMatch(t -> t.getId().equals(item.getId()))) {
                needRemoveItems.add(item);
            }
        });

        target.getTransferTaskItems().forEach(item -> item.setTransferTask(target));
        BeanUtils.copyProperties(target, transferTaskDo, "createdBy", "createdAt", "createdUserName");

        transferTaskItemJpaDao.deleteAll(needRemoveItems);
        return mapper.doToDomain(jpaDao.save(transferTaskDo));
    }

    @Override
    public Page<SearchTransferTaskView> search(TransferTaskQuery transferTaskQuery, Pageable pageable) {
        TransferTaskSearchCriteria transferTaskSearchCriteria = new TransferTaskSearchCriteria();
        BeanUtils.copyProperties(transferTaskQuery, transferTaskSearchCriteria);
        return jdbcTemplate.search(transferTaskSearchCriteria, pageable);
    }

    @Override
    public void deleteAll() {
        jpaDao.deleteAll();
    }

    @Override
    public List<TransferTask> saveAll(List<TransferTask> transferTasks) {
        List<TransferTaskDo> transferTaskDoList = mapper.domainToDos(transferTasks);
        transferTaskDoList.forEach(transferTaskDo ->
            transferTaskDo.getTransferTaskItems()
                .forEach(transferTaskItemDo -> transferTaskItemDo.setTransferTask(transferTaskDo)));
        return mapper.doToDomains(jpaDao.saveAll(transferTaskDoList));
    }

    @Override
    public List<TransferTask> findByPickingTaskItemId(UUID pickingTaskItemId) {
        List<TransferTaskItemDo> items = transferTaskItemJpaDao.findByPickingTaskItemId(pickingTaskItemId);
        if (CollectionUtils.isEmpty(items)) {
            return List.of();
        }
        return mapper.doToDomains(items.stream()
            .map(TransferTaskItemDo::getTransferTask)
            .toList());
    }
}