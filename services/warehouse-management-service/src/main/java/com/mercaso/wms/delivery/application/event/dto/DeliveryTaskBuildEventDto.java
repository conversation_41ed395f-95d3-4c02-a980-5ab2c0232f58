package com.mercaso.wms.delivery.application.event.dto;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTaskBuildEventDto extends BaseDto {

    private String deliveryTaskNumber;
    private String deliveryDate;
    private String truckNumber;
    private UUID driverUserId;
    private String driverUserName;
    private List<OrderInfo> orders;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderInfo {

        private UUID orderId;
        private String orderNumber;
        private String deliveryDate;
        private DeliveryOrderStatus status;
    }
}
