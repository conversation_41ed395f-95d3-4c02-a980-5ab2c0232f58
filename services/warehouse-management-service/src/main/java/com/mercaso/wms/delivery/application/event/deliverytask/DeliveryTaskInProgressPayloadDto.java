package com.mercaso.wms.delivery.application.event.deliverytask;

import com.mercaso.wms.application.dto.event.BusinessEventPayloadDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeliveryTaskInProgressPayloadDto extends BusinessEventPayloadDto<DeliveryTaskDto> {

    private UUID deliveryTaskId;

    @Builder
    public DeliveryTaskInProgressPayloadDto(DeliveryTaskDto data, UUID deliveryTaskId) {
        super(data);
        this.deliveryTaskId = deliveryTaskId;
    }

}
