package com.mercaso.wms.application.service.pickingtask;


import static com.mercaso.wms.domain.pickingtask.enums.CategoryType.CANDY_AND_SNACKS;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.CategoryType;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class CreateBatchLevelPickingTaskService extends CreatePickingTaskService {

    private final FeatureFlagsManager featureFlagsManager;

    protected CreateBatchLevelPickingTaskService(BatchItemRepository batchItemRepository,
        BatchItemQueryService batchItemQueryService,
        PickingTaskRepository pickingTaskRepository,
        LocationRepository locationRepository,
        PickingTaskAssignmentConfig pickingTaskAssignmentConfig,
        PickingTaskApplicationService pickingTaskApplicationService,
        LocationCache locationCache,
        FeatureFlagsManager featureFlagsManager) {
        super(batchItemRepository,
            batchItemQueryService,
            pickingTaskRepository,
            locationRepository,
            pickingTaskAssignmentConfig,
            pickingTaskApplicationService,
            locationCache);
        this.featureFlagsManager = featureFlagsManager;
    }

    @Override
    public List<PickingTask> createPickingTask(UUID batchId) {
        log.info("Creating batch level picking task for batchId: {}", batchId);
        List<PickingTask> pickingTasks = Lists.newArrayList();
        // MFC batch
        if (featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_MFC_BATCH_LEVEL_TASK_LOGIC)) {
            createMfcBatchPickingTaskByBreakdownAisle(batchId, pickingTasks);
        } else {
            createMfcBatchPickingTask(batchId, pickingTasks);
        }
        // MDC batch
        createSmallBeveragePickingTask(batchId, pickingTasks);
        createCandyPickingTask(batchId, pickingTasks);
        createThreePlPickingTask(batchId, pickingTasks);
        createCostcoPickingTask(batchId, pickingTasks);
        createJetroPickingTask(batchId, pickingTasks);
        if (CollectionUtils.isEmpty(pickingTasks)) {
            return pickingTasks;
        }
        pickingTasks.forEach(task -> task.setType(PickingTaskType.BATCH));
        return pickingTaskRepository.saveAll(pickingTasks);
    }

    private void createMfcBatchPickingTask(UUID batchId, List<PickingTask> pickingTasks) {
        List<BatchItem> mfcBatchItems = batchItemQueryService.findBy(batchId, SourceEnum.MFC.name());
        if (mfcBatchItems.isEmpty()) {
            return;
        }
        removeNAAndCoolerDataFromMFCAndMDC(mfcBatchItems);
        log.info("[createMfcBatchPickingTask] Found mfc {} batch items.", mfcBatchItems.size());
        mfcBatchItems.stream()
            .collect(Collectors.groupingBy(item -> Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
            .forEach((department, batchItems) ->
                createTaskByLocationType(batchItems,
                    CategoryType.from(department),
                    pickingTasks,
                    batchId,
                    SourceEnum.MFC)
            );
    }

    private void createMfcBatchPickingTaskByBreakdownAisle(UUID batchId, List<PickingTask> pickingTasks) {
        List<BatchItem> mfcBatchItems = batchItemQueryService.findBy(batchId, SourceEnum.MFC.name());
        if (mfcBatchItems.isEmpty()) {
            return;
        }
        removeNAAndCoolerDataFromMFCAndMDC(mfcBatchItems);
        log.info("[createMfcBatchPickingTaskByBreakdownAisle] Found mfc {} batch items.", mfcBatchItems.size());
        Map<String, List<BatchItem>> groupedByAisle = mfcBatchItems.stream()
            .collect(Collectors.groupingBy(item ->
                StringUtils.isEmpty(item.getBreakdownName()) ? CategoryType.OTHER.getKey()
                    : item.getBreakdownName().split("-")[0]));
        log.info("[createMfcBatchPickingTaskByBreakdownAisle] groupedByAisle : {}", groupedByAisle);

        groupedByAisle.forEach((breakdownAisle, batchItemsByAisle) ->
            batchItemsByAisle.stream()
                .collect(Collectors.groupingBy(item ->
                    Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
                .forEach((department, batchItems) ->
                    createTaskByLocationType(batchItems, CategoryType.from(department), pickingTasks, batchId, SourceEnum.MFC)
                )
        );
    }

    private void createSmallBeveragePickingTask(UUID batchId, List<PickingTask> pickingTasks) {
        List<BatchItem> smallBeverageBatchItems = batchItemQueryService.findSmallBeverageBatchItemsByBatchId(batchId,
            SourceEnum.MDC.name());
        if (smallBeverageBatchItems.isEmpty()) {
            return;
        }
        removeNAAndCoolerDataFromMFCAndMDC(smallBeverageBatchItems);
        log.info("Found {} {} small beverage batch items.", SourceEnum.MDC.name(), smallBeverageBatchItems.size());
        createTaskByLocationType(smallBeverageBatchItems,
            CategoryType.MFC_BEVERAGE_CLEANING,
            pickingTasks,
            batchId,
            SourceEnum.MDC);
    }

    private void createCandyPickingTask(UUID batchId, List<PickingTask> pickingTasks) {
        List<BatchItem> candyBatchItems = batchItemQueryService.findCandyBatchItemsByBatchId(batchId, SourceEnum.MDC.name());
        if (candyBatchItems.isEmpty()) {
            return;
        }
        candyBatchItems = candyBatchItems.stream()
            .filter(batchItem -> !batchItem.isBigOrder())
            .collect(Collectors.toList());

        removeNAAndCoolerDataFromMFCAndMDC(candyBatchItems);
        log.info("Found {} {} mfc candy batch items.", SourceEnum.MDC.name(), candyBatchItems.size());
        createTaskByLocationType(candyBatchItems, CategoryType.MFC_CANDY_OTHER, pickingTasks, batchId, SourceEnum.MDC);
    }

    private void createTaskByLocationType(List<BatchItem> batchItems,
        CategoryType categoryType,
        List<PickingTask> pickingTasks,
        UUID batchId,
        SourceEnum source) {
        batchItems.sort(Comparator.comparing(BatchItem::getLocationName,
                Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder())));
        Map<String, List<BatchItem>> groupedByLocations = batchItems.stream()
            .collect(Collectors.groupingBy(batchItem -> {
                if (batchItem.getLocationName() == null) {
                    return "OTHER_GROUP";
                }
                if (batchItem.getLocationName().startsWith(".RD")) {
                    return "RD_GROUP";
                } else if (batchItem.getLocationName().startsWith(".")) {
                    return batchItem.getLocationName().replace(".", "") + "_GROUP";
                } else if (batchItem.getLocationName().contains("-")) {
                    return "AISLE_" + batchItem.getLocationName().substring(0, 3);
                } else {
                    return "OTHER_GROUP";
                }
            }));
        groupedByLocations.forEach((location, items) -> createTaskByTargetQty(items,
            categoryType.getValue(),
            pickingTasks,
            batchId,
            source));
    }

    private void createThreePlPickingTask(UUID batchId, List<PickingTask> pickingTasks) {
        List<BatchItem> downeyBatchItems = batchItemQueryService.findBy(batchId, SourceEnum.DOWNEY.name());
        if (downeyBatchItems.isEmpty()) {
            return;
        }
        removeNAAndCoolerDataFromMFCAndMDC(downeyBatchItems);
        log.info("Found {} downey batch items.", downeyBatchItems.size());
        downeyBatchItems.stream()
            .collect(Collectors.groupingBy(item -> Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
            .forEach((department, batchItems) -> {
                if (Objects.equals(BatchConstants.CANDY_AND_SNACKS, department)) {
                    batchItems.stream()
                        .collect(Collectors.groupingBy(item -> item.getCategory() == null ? "NO_CATEGORY" : item.getCategory()))
                        .forEach((category, items) -> {
                            sortBatchItems(SourceEnum.DOWNEY.name(), items);
                            createTaskByTargetQty(items, CANDY_AND_SNACKS.getValue(), pickingTasks, batchId, SourceEnum.DOWNEY);
                        });
                } else {
                    sortBatchItems(SourceEnum.DOWNEY.name(), batchItems);
                    createTaskByTargetQty(batchItems,
                        CategoryType.fromKey(department),
                        pickingTasks,
                        batchId,
                        SourceEnum.DOWNEY);
                }
            });
    }

    private void createCostcoPickingTask(UUID batchId, List<PickingTask> pickingTasks) {
        List<BatchItem> costcoBatchItems = batchItemQueryService.findBy(batchId, SourceEnum.COSTCO.name());
        if (costcoBatchItems.isEmpty()) {
            return;
        }
        removeNAAndCoolerDataFromMFCAndMDC(costcoBatchItems);
        log.info("Found {} costco batch items.", costcoBatchItems.size());
        costcoBatchItems.stream()
            .collect(Collectors.groupingBy(item -> Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
            .forEach((department, batchItems) -> {
                if (Objects.equals(BatchConstants.CANDY_AND_SNACKS, department)) {
                    batchItems.stream()
                        .collect(Collectors.groupingBy(item -> item.getCategory() == null ? "NO_CATEGORY" : item.getCategory()))
                        .forEach((category, items) -> {
                            sortBatchItems(SourceEnum.COSTCO.name(), items);
                            createTaskByTargetQty(items, CANDY_AND_SNACKS.getValue(), pickingTasks, batchId, SourceEnum.COSTCO);
                        });
                } else {
                    sortBatchItems(SourceEnum.COSTCO.name(), batchItems);
                    createTaskByTargetQty(batchItems, CategoryType.fromKey(department), pickingTasks, batchId, SourceEnum.COSTCO);
                }
            });
    }

    private void sortBatchItems(String source, List<BatchItem> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        Comparator<BatchItem> fromComparator = Comparator.nullsLast(Comparator.comparing(BatchItem::getLocationName,
            Comparator.nullsLast(Comparator.naturalOrder())));
        Comparator<BatchItem> prepComparator = Comparator
            .nullsLast(Comparator.comparingInt((BatchItem item) -> item.getPrep() != null ? item.getPrep().length() : 0)
                .thenComparing(BatchItem::getPrep, Comparator.nullsLast(Comparator.naturalOrder()))).reversed();

        Comparator<BatchItem> commonComparator = Comparator.nullsLast(Comparator.comparing(BatchItem::getDepartment,
                Comparator.nullsLast(Comparator.naturalOrder())))
            .thenComparing(Comparator.nullsLast(Comparator.comparing(batchItem -> batchItem != null ? batchItem.getSubCategory()
                    : null,
                Comparator.nullsLast(Comparator.naturalOrder()))))
            .thenComparing(Comparator.nullsLast(Comparator.comparing(batchItem -> batchItem != null ? batchItem.getTitle()
                    : null,
                Comparator.nullsLast(Comparator.naturalOrder()))));
        if (source.equals(SourceEnum.DOWNEY.name())) {
            list.sort(fromComparator.thenComparing(prepComparator).thenComparing(commonComparator));
        }
        if (source.equals(SourceEnum.COSTCO.name())) {
            list.sort(fromComparator.thenComparing(commonComparator));
        }
    }

    private void createTaskByTargetQty(List<BatchItem> batchItemList,
        int targetSum,
        List<PickingTask> pickingTasks,
        UUID batchId,
        SourceEnum source) {
        Map<UUID, Location> locationMap = locationCache.getLocationMap();
        splitBatchItems(batchItemList, targetSum).forEach(batchItems -> {
            if (CollectionUtils.isEmpty(batchItems)) {
                return;
            }
            pickingTasks.add(PickingTask.builder().build().createTask(batchId, source, batchItems, locationMap));
        });
    }

    private void createJetroPickingTask(UUID batchId, List<PickingTask> pickingTasks) {
        List<BatchItem> jetroBatchItems = batchItemQueryService.findBy(batchId, SourceEnum.JETRO.name());
        if (jetroBatchItems.isEmpty()) {
            return;
        }

        removeNAAndCoolerDataFromMFCAndMDC(jetroBatchItems);
        log.info("Found {} jetro batch items.", jetroBatchItems.size());

        jetroBatchItems.forEach(item -> {
            item.setAisle(extractAisle(item.getLocationName()));
            item.setBin(extractBin(item.getLocationName()));
        });

        jetroBatchItems.sort(Comparator.comparing(BatchItem::getAisle, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getBin, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getDepartment, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getSkuNumber, Comparator.nullsLast(Comparator.naturalOrder())));

        Map<String, List<BatchItem>> itemsByAisle = jetroBatchItems.stream()
            .collect(Collectors.groupingBy(item -> Optional.ofNullable(item.getAisle()).orElse("N/A")));

        Map<UUID, Location> locationMap = locationCache.getLocationMap();
        itemsByAisle.forEach((aisle, items) ->
            pickingTasks.add(PickingTask.builder().build().createTask(batchId, SourceEnum.JETRO, items, locationMap))
        );
    }

    private String extractAisle(String locationCode) {
        if (StringUtils.isEmpty(locationCode) || !locationCode.matches("\\d+")) {
            return locationCode;
        }
        return locationCode.length() == 3
            ? locationCode.substring(0, 1)
            : locationCode.substring(0, Math.min(2, locationCode.length()));
    }

    private String extractBin(String locationCode) {
        if (StringUtils.isEmpty(locationCode) || !locationCode.matches("\\d+")) {
            return locationCode;
        }
        return locationCode.length() <= 2 ? locationCode : locationCode.substring(locationCode.length() == 3 ? 1 : 2);
    }

}
