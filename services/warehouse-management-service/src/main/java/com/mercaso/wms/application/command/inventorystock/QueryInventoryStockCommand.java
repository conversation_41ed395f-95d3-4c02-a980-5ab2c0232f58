package com.mercaso.wms.application.command.inventorystock;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryInventoryStockCommand {

    @NotEmpty(message = "Picking task item IDs cannot be empty")
    private List<UUID> pickingTaskItemIds;
}