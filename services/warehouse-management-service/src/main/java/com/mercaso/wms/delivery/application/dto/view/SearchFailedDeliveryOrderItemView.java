package com.mercaso.wms.delivery.application.dto.view;

import com.mercaso.wms.application.dto.BaseDto;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchFailedDeliveryOrderItemView extends BaseDto {

    private UUID id;
    private UUID deliveryOrderId;
    private String deliveryDate;
    private String orderNumber;
    private UUID driverUserId;
    private String driverUserName;
    private String reasonCode;
    private Integer line;
    private String skuNumber;
    private String title;
    private BigDecimal reasonQty;
    private String updatedAt;

}
