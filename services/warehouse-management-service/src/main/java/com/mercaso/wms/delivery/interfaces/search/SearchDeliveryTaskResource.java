package com.mercaso.wms.delivery.interfaces.search;

import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryTaskView;
import com.mercaso.wms.delivery.application.query.DeliveryTaskQuery;
import com.mercaso.wms.delivery.application.search.DeliveryTaskSearchService;
import com.mercaso.wms.delivery.infrastructure.annotation.SingleDeviceLoginCheck;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.interfaces.util.SortByParseUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Delivery Tasks")
@Slf4j
@Validated
@RestController
@RequestMapping("/delivery/search")
@RequiredArgsConstructor
public class SearchDeliveryTaskResource {

    private final DeliveryTaskSearchService deliveryTaskSearchService;

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:read:delivery-tasks')")
    @GetMapping("/delivery-tasks")
    public Result<SearchDeliveryTaskView> search(@ModelAttribute @Valid DeliveryTaskQuery query) {
        log.info("Received search request for delivery tasks: {}", query);

        List<SortType> defaultSortTypes = List.of(SortType.DELIVERY_DATE_DESC, SortType.UPDATED_AT_DESC);
        List<SortType> sortTypes = CollectionUtils.isEmpty(query.getSortTypes()) ? defaultSortTypes : query.getSortTypes();

        Sort sort = SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.DELIVERY_TASK);
        PageRequest pageable = PageRequest.of(query.getPage() - 1, query.getPageSize(), Objects.requireNonNull(sort));

        Page<SearchDeliveryTaskView> deliveryTaskPage = deliveryTaskSearchService.search(query, pageable);

        log.info("Found {} delivery tasks", deliveryTaskPage.getTotalElements());

        return new Result<>(deliveryTaskPage.getContent(), deliveryTaskPage.getTotalElements());
    }

}
