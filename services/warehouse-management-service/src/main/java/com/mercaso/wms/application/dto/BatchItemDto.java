package com.mercaso.wms.application.dto;

import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchItemDto extends BaseDto {

    private UUID id;

    private UUID batchId;

    private String orderNumber;

    private UUID itemId;

    private String skuNumber;

    private String title;

    private Integer expectQty;

    private Integer allocatedQty;

    private Integer line;

    private UUID locationId;

    private String locationName;

    private String breakdownName;

    private String source;

    private String department;

    private String prep;

    private String category;

    private String subCategory;

    private String clazz;
}
