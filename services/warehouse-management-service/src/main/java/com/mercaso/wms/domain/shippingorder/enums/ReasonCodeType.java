package com.mercaso.wms.domain.shippingorder.enums;

/**
 * Enum for shipping order item reason code types
 */
public enum ReasonCodeType {
    DAMAGED,
    MISSING,
    WRONG,
    NOT_CROSS_DOCKED;

    /**
     * Determines if this reason code type should subtract quantity
     *
     * @return true if quantity should be subtracted, false if quantity should be added
     */
    public boolean shouldSubtract() {
        return this == DAMAGED || this == MISSING || this == WRONG;
    }

    /**
     * Determines if this reason code type should add quantity
     *
     * @return true if quantity should be added, false if quantity should be subtracted
     */
    public boolean shouldAdd() {
        return this == NOT_CROSS_DOCKED;
    }

    public static ReasonCodeType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        try {
            return ReasonCodeType.valueOf(value.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
