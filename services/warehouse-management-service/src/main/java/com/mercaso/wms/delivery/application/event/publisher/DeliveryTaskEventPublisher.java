package com.mercaso.wms.delivery.application.event.publisher;

import com.mercaso.businessevents.client.BusinessEventClient;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskBuildPayloadDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskBuildEventDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskBuildEventDto.OrderInfo;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryTaskEventPublisher {

    private final BusinessEventClient businessEventClient;

    private final ApplicationEventDispatcher applicationEventDispatcher;

    public void publishDeliveryTaskBuildEvent(DeliveryTask task, List<DeliveryOrder> allOrdersToUpdate) {
        if (task == null) {
            log.warn("Delivery task is null, cannot publish event.");
            return;
        }

        if (CollectionUtils.isEmpty(allOrdersToUpdate)) {
            log.info("No orders to update for task ID: {}, skipping event publication.", task.getId());
            return;
        }

        UUID taskId = task.getId();

        List<DeliveryOrder> ordersBelongToTask = allOrdersToUpdate.stream()
            .filter(order -> taskId.equals(order.getDeliveryTaskId()))
            .toList();

        if (CollectionUtils.isEmpty(ordersBelongToTask)) {
            log.info("No orders belong to the delivery task with ID: {}, skipping event publication.", taskId);
            return;
        }

        DeliveryTaskBuildPayloadDto payload = buildDeliveryTaskBuildPayloadDto(task, ordersBelongToTask);

        log.info("Publishing delivery task build event: taskId={}, payload={}", taskId, payload);
        DispatchResponseDto responseDto = businessEventClient.dispatch(BusinessEventFactory.build(payload));
        if (responseDto != null) {
            applicationEventDispatcher.publishEvent(responseDto.getEvent());
            log.info("Published delivery task build event: task number {}", task.getNumber());
        } else {
            log.warn("Failed to publish delivery task build event: task number {}", task.getNumber());
        }
    }

    private static DeliveryTaskBuildPayloadDto buildDeliveryTaskBuildPayloadDto(DeliveryTask task,
        List<DeliveryOrder> ordersBelongToTask) {

        return DeliveryTaskBuildPayloadDto.builder()
            .deliveryTaskId(task.getId())
            .data(DeliveryTaskBuildEventDto.builder()
                .deliveryTaskNumber(task.getNumber())
                .deliveryDate(task.getDeliveryDate())
                .truckNumber(task.getTruckNumber())
                .driverUserId(task.getDriverUserId())
                .driverUserName(task.getDriverUserName())
                .orders(ordersBelongToTask.stream()
                    .map(order -> OrderInfo.builder()
                        .orderId(order.getId())
                        .orderNumber(order.getOrderNumber())
                        .deliveryDate(order.getDeliveryDate())
                        .status(order.getStatus())
                        .build())
                    .toList())
                .build())
            .build();
    }
}
