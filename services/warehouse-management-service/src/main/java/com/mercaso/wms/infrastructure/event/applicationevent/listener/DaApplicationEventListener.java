package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import static com.mercaso.wms.infrastructure.contant.FeatureFlagKeys.CLEAN_SHIP_SB_V1;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.service.InventoryStockApplicationService;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderRouteInfoDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredApplicationEvent;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskBuildApplicationEvent;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskReassignedApplicationEvent;
import com.mercaso.wms.delivery.config.DemoDataRefreshConfig;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.delivery.DeliveryAdaptor;
import com.mercaso.wms.infrastructure.slackalert.WmsExceptionAlertService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class DaApplicationEventListener {

    private final InventoryStockApplicationService inventoryStockApplicationService;
    private final ShippingOrderApplicationService shippingOrderApplicationService;
    private final WmsExceptionAlertService wmsExceptionAlertService;
    private final FeatureFlagsManager featureFlagsManager;
    private final DeliveryAdaptor deliveryAdaptor;
    private final DemoDataRefreshConfig demoDataRefreshConfig;

    @TransactionalEventListener
    public void handleDeliveredAppEvent(DeliveryOrderDeliveredApplicationEvent applicationEvent) {

        if (!featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)) {
            log.info("The feature flag CLEAN_SHIP_SB_V1 is off, skip clean ship sb v1.");
            return;
        }

        if (!isValidApplicationEvent(applicationEvent)) {
            log.warn("[handleDeliveredAppEvent] Invalid application event received");
            return;
        }

        UUID deliveryOrderId = applicationEvent.getPayload().getDeliveryOrderId();
        DeliveryOrderDto deliveryOrderDto = applicationEvent.getPayload().getData();
        if (demoDataRefreshConfig.isEnabled() && CollectionUtils.isNotEmpty(demoDataRefreshConfig.getOrderNumbers())
            && demoDataRefreshConfig.getOrderNumbers().contains(deliveryOrderDto.getOrderNumber())) {
            log.info("[handleDeliveredAppEvent] Demo data refresh is enabled, skip processing for delivery order: {}",
                deliveryOrderId);
            return;
        }

        log.info("[handleDeliveredAppEvent] Processing delivery order delivered event for order: {}", deliveryOrderId);

        // Update delivery quantity - this has its own transaction
        try {
            shippingOrderApplicationService.updateDeliveryQty(deliveryOrderDto);
            log.info("[handleDeliveredAppEvent] Successfully updated delivery quantity for order: {}", deliveryOrderId);

        } catch (Exception e) {
            log.error("[handleDeliveredAppEvent] Failed to update delivery quantity for order: {}",
                deliveryOrderId, e);
            throw new WmsBusinessException("Failed to update delivery quantity", e);
        }

        // Clean up virtual inventory - handle separately to allow data recovery
        // This is now completely independent and won't affect the delivery quantity update
        try {
            inventoryStockApplicationService.cleanGhostInventory(deliveryOrderDto);
            log.info("[handleDeliveredAppEvent] Successfully cleaned ghost inventory for order: {}", deliveryOrderId);

        } catch (Exception e) {

            sendGhostInventoryCleanupFailureAlert(deliveryOrderId, deliveryOrderDto, e);

            // Don't throw exception to avoid rolling back the delivery quantity update
            log.warn("[handleDeliveredAppEvent] Continuing processing despite ghost inventory cleanup failure for order: {}",
                deliveryOrderId);
        }

        log.info("[handleDeliveredAppEvent] Completed processing delivery order delivered event for order: {}", deliveryOrderId);
    }

    /**
     * Validates the application event structure
     */
    private boolean isValidApplicationEvent(DeliveryOrderDeliveredApplicationEvent applicationEvent) {
        if (applicationEvent == null) {
            log.warn("[handleDeliveredAppEvent] Application event is null");
            return false;
        }

        if (applicationEvent.getPayload() == null) {
            log.warn("[handleDeliveredAppEvent] Application event payload is null");
            return false;
        }

        if (applicationEvent.getPayload().getDeliveryOrderId() == null) {
            log.warn("[handleDeliveredAppEvent] Delivery order ID is null in payload");
            return false;
        }

        if (applicationEvent.getPayload().getData() == null) {
            log.warn("[handleDeliveredAppEvent] Delivery order data is null in payload");
            return false;
        }

        return true;
    }

    /**
     * Sends WMS alert for ghost inventory cleanup failure
     */
    private void sendGhostInventoryCleanupFailureAlert(UUID deliveryOrderId, DeliveryOrderDto deliveryOrderDto,
        Exception exception) {

        String shippingOrderNumber = deliveryOrderDto.getOrderNumber().replaceFirst("M-", "");

        try {
            String deliveryItemsSummary = getDeliveryItemsSummary(deliveryOrderDto);
            String errorMessage = exception.getMessage() != null ? exception.getMessage() : exception.getClass().getSimpleName();

            wmsExceptionAlertService.alertGhostInventoryCleanupFailure(
                deliveryOrderId,
                shippingOrderNumber,
                deliveryItemsSummary,
                errorMessage
            );

            log.info(
                "[sendGhostInventoryCleanupFailureAlert] Successfully triggered WMS alert for ghost inventory cleanup failure: orderId={}, orderNumber={}",
                deliveryOrderId,
                shippingOrderNumber);

        } catch (Exception e) {
            log.error(
                "[sendGhostInventoryCleanupFailureAlert] Failed to trigger WMS alert for ghost inventory cleanup failure: orderId={}, orderNumber={}",
                deliveryOrderId,
                shippingOrderNumber,
                e);
        }
    }

    /**
     * Creates a summary of delivery items for error reporting
     */
    private String getDeliveryItemsSummary(DeliveryOrderDto deliveryOrderDto) {
        if (deliveryOrderDto == null || deliveryOrderDto.getDeliveryOrderItems() == null) {
            return "NO_ITEMS";
        }

        StringBuilder summary = new StringBuilder();
        summary.append("[");

        deliveryOrderDto.getDeliveryOrderItems().forEach(item -> {
            if (item != null) {
                summary.append("{sku=").append(item.getSkuNumber())
                    .append(",qty=").append(item.getDeliveredQty())
                    .append("},");
            }
        });

        if (summary.length() > 1) {
            summary.setLength(summary.length() - 1); // Remove trailing comma
        }
        summary.append("]");

        return summary.toString();
    }

    /**
     * Handles delivery task reassigned events. Validates the event, retrieves delivery task and orders, then reassigns shipping
     * orders.
     *
     * @param applicationEvent the delivery task reassigned event
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    public void handleDeliveryTaskReassignedEvent(DeliveryTaskReassignedApplicationEvent applicationEvent) {
        if (!isValidDeliveryTaskReassignedEvent(applicationEvent)) {
            log.warn("[handleDeliveryTaskReassignedEvent] Invalid application event received");
            return;
        }

        UUID deliveryTaskId = applicationEvent.getPayload().getDeliveryTaskId();
        log.info("[handleDeliveryTaskReassignedEvent] Handling delivery task reassigned event for task ID: {}", deliveryTaskId);

        try {
            DeliveryTask deliveryTask = getDeliveryTaskOrThrow(deliveryTaskId);
            validateDeliveryOrders(deliveryTaskId);

            ReassignmentData reassignmentData = extractReassignmentData(applicationEvent);

            shippingOrderApplicationService.reassignShippingOrders(
                deliveryTask.getDeliveryDate(),
                reassignmentData.previousUserId(),
                reassignmentData.currentUserId(),
                reassignmentData.currentUserName()
            );

            log.info(
                "[handleDeliveryTaskReassignedEvent] Successfully reassigned shipping orders from user {} to user {} for delivery date {}",
                reassignmentData.previousUserId(),
                reassignmentData.currentUserId(),
                deliveryTask.getDeliveryDate());

        } catch (Exception e) {
            log.error("[handleDeliveryTaskReassignedEvent] Failed to process delivery task reassignment for task ID: {}",
                deliveryTaskId,
                e);
            throw new WmsBusinessException("Failed to process delivery task reassignment", e);
        }
    }

    /**
     * Record to hold reassignment data
     */
    private record ReassignmentData(UUID previousUserId, UUID currentUserId, String currentUserName) {

    }

    /**
     * Validates the delivery task reassigned application event structure
     */
    private boolean isValidDeliveryTaskReassignedEvent(DeliveryTaskReassignedApplicationEvent applicationEvent) {
        if (applicationEvent == null) {
            log.warn("[handleDeliveryTaskReassignedEvent] Application event is null");
            return false;
        }

        if (applicationEvent.getPayload() == null) {
            log.warn("[handleDeliveryTaskReassignedEvent] Application event payload is null");
            return false;
        }

        if (applicationEvent.getPayload().getDeliveryTaskId() == null) {
            log.warn("[handleDeliveryTaskReassignedEvent] Delivery task ID is null in payload");
            return false;
        }

        if (applicationEvent.getPayload().getData() == null) {
            log.warn("[handleDeliveryTaskReassignedEvent] Reassignment data is null in payload");
            return false;
        }

        var data = applicationEvent.getPayload().getData();
        if (data.getPreviousUserId() == null || data.getCurrentUserId() == null) {
            log.warn("[handleDeliveryTaskReassignedEvent] User IDs are null in reassignment data");
            return false;
        }

        return true;
    }

    /**
     * Retrieves delivery task by ID or throws exception if not found
     */
    private DeliveryTask getDeliveryTaskOrThrow(UUID deliveryTaskId) {
        DeliveryTask deliveryTask = deliveryAdaptor.findDeliveryTaskById(deliveryTaskId);
        if (deliveryTask == null) {
            throw new WmsBusinessException("No delivery task found for ID: " + deliveryTaskId);
        }
        return deliveryTask;
    }

    /**
     * Validates that delivery orders exist for the given task ID
     */
    private void validateDeliveryOrders(UUID deliveryTaskId) {
        List<DeliveryOrder> deliveryOrders = deliveryAdaptor.findDeliveryOrderByTaskId(deliveryTaskId);
        if (deliveryOrders.isEmpty()) {
            throw new WmsBusinessException("No delivery orders found for task ID: " + deliveryTaskId);
        }
    }

    /**
     * Extracts reassignment data from the application event
     */
    private ReassignmentData extractReassignmentData(DeliveryTaskReassignedApplicationEvent applicationEvent) {
        var data = applicationEvent.getPayload().getData();
        return new ReassignmentData(
            data.getPreviousUserId(),
            data.getCurrentUserId(),
            data.getCurrentUserName()
        );
    }

    /**
     * Validates the delivery task build application event structure
     */
    private boolean isValidDeliveryTaskBuildEvent(DeliveryTaskBuildApplicationEvent applicationEvent) {
        if (applicationEvent == null) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Application event is null");
            return false;
        }

        if (applicationEvent.getPayload() == null) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Application event payload is null");
            return false;
        }

        if (applicationEvent.getPayload().getData() == null) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Delivery task build data is null in payload");
            return false;
        }

        var data = applicationEvent.getPayload().getData();

        // Validate driver information
        if (data.getDriverUserId() == null) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Driver user ID is null in data");
            return false;
        }

        if (data.getDriverUserName() == null || data.getDriverUserName().trim().isEmpty()) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Driver user name is null or empty in data");
            return false;
        }

        // Validate truck number
        if (data.getTruckNumber() == null || data.getTruckNumber().trim().isEmpty()) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Truck number is null or empty in data");
            return false;
        }

        // Validate orders list
        if (data.getOrders() == null || data.getOrders().isEmpty()) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Orders list is null or empty in data");
            return false;
        }
        return true;
    }

    @TransactionalEventListener
    public void handleDeliveryTaskBuildAppEvent(DeliveryTaskBuildApplicationEvent applicationEvent) {
        if (!isValidDeliveryTaskBuildEvent(applicationEvent)) {
            log.warn("[handleDeliveryTaskBuildAppEvent] Invalid application event received");
            return;
        }

        log.info("[handleDeliveryTaskBuildAppEvent] Processing delivery task build event for task ID: {}",
            applicationEvent.getPayload().getDeliveryTaskId());

        List<DeliveryOrderRouteInfoDto> deliveryOrderRouteInfoDtos = Lists.newArrayList();
        applicationEvent.getPayload().getData().getOrders().forEach(order ->
            deliveryOrderRouteInfoDtos.add(DeliveryOrderRouteInfoDto.builder()
                .id(order.getOrderId())
                .driverUserId(applicationEvent.getPayload().getData().getDriverUserId())
                .driverUserName(applicationEvent.getPayload().getData().getDriverUserName())
                .status(order.getStatus())
                .orderNumber(order.getOrderNumber())
                .deliveryDate(order.getDeliveryDate())
                .truckNumber(applicationEvent.getPayload().getData().getTruckNumber())
                .build())
        );

        try {
            shippingOrderApplicationService.syncRouteInfoForDeliveryTaskBuild(deliveryOrderRouteInfoDtos);
            log.info("[handleDeliveryTaskBuildAppEvent] Successfully synced route info for delivery task build, task ID: {}",
                applicationEvent.getPayload().getDeliveryTaskId());
        } catch (Exception e) {
            log.error("[handleDeliveryTaskBuildAppEvent] Failed to sync route info for delivery task build, task ID: {}",
                applicationEvent.getPayload().getDeliveryTaskId(), e);
        }
    }
}
