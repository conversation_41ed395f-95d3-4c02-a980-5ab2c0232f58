package com.mercaso.wms.application.service;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleFacilityDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocationApplicationService {

    private final FinaleProductService finaleProductService;

    private final LocationRepository locationRepository;

    private final WarehouseRepository warehouseRepository;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final FeatureFlagsManager featureFlagsManager;

    private final FinaleConfigProperties finaleConfigProperties;

    private static final Pattern MFC_LOCATION_PATTERN = Pattern.compile("(\\d{2})-(\\d{2})-([A-Za-z])-(\\d{1,2})(-.*)?");

    private static final Pattern MDC_LOCATION_PATTERN = Pattern.compile("(\\d{3})-(\\d{2})-([A-Za-z])-(\\d{1,2})(-.*)?");

    @Scheduled(fixedRate = 60000 * 60 * 24) // 24 hours
    public void syncLocations() {
        if (!featureFlagsManager.isFeatureOn(FeatureFlagKeys.SYNC_SUBLOCATIONS_FROM_FINALE)) {
            return;
        }
        Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel("LocationApplicationService.syncLocations".hashCode());
        if (isAcquired != null && !isAcquired) {
            return;
        }
        log.info("Start syncing locations");
        List<FinaleFacilityDto> finaleFacilityData = finaleProductService.getFinaleFacilityData();
        if (finaleFacilityData.isEmpty()) {
            log.warn("No locations found in Finale");
            return;
        }

        Map<String, Warehouse> warehouses = getWarehouses();
        if (warehouses.isEmpty()) {
            log.warn("No warehouses found");
            return;
        }

        Map<String, Location> existingLocations = locationRepository.findAll().stream()
            .collect(Collectors.toMap(Location::getName, Function.identity()));

        List<Location> locationsToSave = new ArrayList<>();

        for (FinaleFacilityDto facilityDto : finaleFacilityData) {
            processLocation(facilityDto, existingLocations, warehouses, locationsToSave);
        }

        if (!locationsToSave.isEmpty()) {
            locationRepository.saveAll(locationsToSave);
            log.info("Saved {} new locations", locationsToSave.size());
        }
        log.info("Finished syncing locations");
    }

    private Map<String, Warehouse> getWarehouses() {
        Map<String, Warehouse> warehouses = new HashMap<>();
        Warehouse mfcWarehouse = warehouseRepository.findByName("MFC");
        Warehouse mdcWarehouse = warehouseRepository.findByName("MDC");

        if (mfcWarehouse != null) {
            warehouses.put("MFC", mfcWarehouse);
        }
        if (mdcWarehouse != null) {
            warehouses.put("MDC", mdcWarehouse);
        }

        return warehouses;
    }

    private void processLocation(FinaleFacilityDto facilityDto,
        Map<String, Location> existingLocations,
        Map<String, Warehouse> warehouses,
        List<Location> locationsToSave) {
        String facilityName = facilityDto.getFacilityName();
        Location location = existingLocations.get(facilityName);

        if (location == null) {
            createNewLocation(facilityDto, warehouses, locationsToSave);
        } else {
            updateExistingLocation(location, facilityDto);
        }
    }

    private void createNewLocation(FinaleFacilityDto facilityDto,
        Map<String, Warehouse> warehouses,
        List<Location> locationsToSave) {
        String finaleId = facilityDto.getFacilityId();
        String facilityName = facilityDto.getFacilityName();
        LocationType locationType = determineLocationType(facilityDto.getShippingDisabled());

        if (isMatchingWarehouse(facilityName, MFC_LOCATION_PATTERN, "MFC")) {
            createLocationForWarehouse(warehouses.get("MFC"), facilityName, locationType, locationsToSave, finaleId);
        } else if (isMatchingWarehouse(facilityName, MDC_LOCATION_PATTERN, "MDC")) {
            createLocationForWarehouse(warehouses.get("MDC"), facilityName, locationType, locationsToSave, finaleId);
        }
    }

    private boolean isMatchingWarehouse(String facilityName, Pattern pattern, String warehouseCode) {
        if (warehouseCode.equals("MDC")) {
            return pattern.matcher(facilityName).matches() || facilityName.contains(warehouseCode)
                || finaleConfigProperties.getNeedToBeSyncedLocations().stream().anyMatch(facilityName::contains);
        } else {
            return pattern.matcher(facilityName).matches() || facilityName.contains(warehouseCode);
        }
    }

    private void createLocationForWarehouse(Warehouse warehouse,
        String facilityName,
        LocationType locationType,
        List<Location> locationsToSave,
        String finaleId) {
        if (warehouse != null) {
            Location location = Location.builder()
                .build()
                .createLocation(warehouse, facilityName, locationType, finaleId);
            if (!locationsToSave.contains(location)) {
                locationsToSave.add(location);
            } else {
                log.warn("Location {} already exists", facilityName);
            }
        }
    }

    private void updateExistingLocation(Location location, FinaleFacilityDto facilityDto) {
        LocationType newType = determineLocationType(facilityDto.getShippingDisabled());
        if ((location.getType() != newType && location.getType() != LocationType.STAGING_AREA)
            || location.getFinaleId() == null) {
            location.setType(newType);
            location.setFinaleId(facilityDto.getFacilityId());
            locationRepository.update(location);
        }
    }

    private LocationType determineLocationType(Boolean shippingDisabled) {
        return Boolean.TRUE.equals(shippingDisabled) ? LocationType.STOCK : LocationType.BIN;
    }

}
