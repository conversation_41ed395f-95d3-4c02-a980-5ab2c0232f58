package com.mercaso.wms.domain.document;

import com.mercaso.wms.domain.BaseDomainRepository;
import com.mercaso.wms.domain.document.enums.DocumentType;
import java.util.List;
import java.util.UUID;

public interface DocumentRepository extends BaseDomainRepository<Document, UUID> {

    List<Document> findByEntityIdAndEntityNameAndDocumentTypes(UUID entityId,
        String entityName,
        List<DocumentType> documentTypes);

    List<Document> findByEntityIds(List<UUID> entityIds);
} 