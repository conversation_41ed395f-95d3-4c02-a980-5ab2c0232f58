package com.mercaso.wms.infrastructure.slackalert;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.businessevents.client.BusinessEventClient;
import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.businessevents.dto.BusinessEventPageDto;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.dto.event.WmsExceptionAlertPayloadDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.businessevent.EventTypeEnums;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.infrastructure.alert.dto.WmsExceptionAlertDto;
import com.mercaso.wms.infrastructure.alert.dto.WmsExceptionAlertDto.AlertEventType;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * WMS Exception Alert Service
 * Detects WMS exception scenarios and publishes alert events
 * Currently focused on ORDER_AFTER_BATCH_CREATION but designed for easy extension
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WmsExceptionAlertService {

    private final BatchRepository batchRepository;
    private final BusinessEventClient businessEventClient;
    private final ApplicationEventDispatcher applicationEventDispatcher;
    private final FinaleConfigProperties finaleConfigProperties;
    private final FeatureFlagsManager featureFlagsManager;

    @Value("${mercaso.wms-portal-base-url}")
    private String wmsAdminPortalBaseUrl;

    private static final String SHIPPING_ORDER_DETAIL = "%s/shipping-orders/%s";
    private static final String PICKING_TASK_LIST = "%s/picking-tasks?deliveryDate=&%s";

    /**
     * Checks if a new order is created after batch was created for the same delivery date
     * This scenario is considered an exception as it may require batch re-planning
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void checkOrderAfterBatchCreation(ShippingOrder shippingOrder) {
        if (shippingOrder.getBatchId() != null) {
            return; // Only check for newly created orders
        }

        LocalDate orderDeliveryDate = shippingOrder.getDeliveryDate();
        if (orderDeliveryDate == null) {
            return; // Cannot determine delivery date
        }

        try {
            String orderDetailUrl = String.format(SHIPPING_ORDER_DETAIL, wmsAdminPortalBaseUrl, shippingOrder.getId());

            List<Batch> existingBatches = batchRepository.findActiveByDeliveryDate(orderDeliveryDate);
            if (!existingBatches.isEmpty()) {
                Batch batch = existingBatches.getFirst();
                WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createOrderAfterBatchAlert(
                    shippingOrder.getOrderNumber().replaceFirst("M-", ""),
                    shippingOrder.getId(),
                    batch.getNumber(),
                    batch.getId(),
                    orderDeliveryDate,
                    orderDetailUrl
                );

                publishAlert(alertDto);

                log.warn("Order after batch creation detected: Order {} for delivery date {} (Batch: {})",
                    shippingOrder.getOrderNumber(), orderDeliveryDate, batch.getNumber());
            }
        } catch (Exception e) {
            log.error("Error checking order after batch creation for order: {}",
                shippingOrder.getOrderNumber(), e);
        }
    }

    /**
     * Publishes WMS exception alert as business event and application event
     * Business event: for external systems and audit trail
     * Application event: for internal processing (Slack notification)
     */
    private void publishAlert(WmsExceptionAlertDto alertDto) {
        log.info("Publishing WMS exception alert: eventType={}, entityId={}",
            alertDto.getAlertEventType(), alertDto.getEntityId());

        try {
            // Check for duplicate alerts to prevent spam
            if (isDuplicateAlert(alertDto)) {
                log.warn("Duplicate WMS exception alert detected, skipping: eventType={}, entityId={}",
                    alertDto.getAlertEventType(), alertDto.getEntityId());
                return;
            }
            // Create event payload
            WmsExceptionAlertPayloadDto payload = WmsExceptionAlertPayloadDto.create(alertDto);
            log.info("Publish wms exception alert with payload: {}, data: {}", payload, payload.getData());
            // Build event instance once and reuse it
            var event = BusinessEventFactory.build(payload);
            if (event == null) {
                log.error("Failed to create business event for WMS exception alert: eventType={}, entityId={}",
                    alertDto.getAlertEventType(), alertDto.getEntityId());
                return;
            }

            // Publish business event for external systems and audit trail
            businessEventClient.dispatch(event);

            // Publish application event for internal processing (Slack notification)
            applicationEventDispatcher.publishEvent(event);

            log.info("Published WMS exception alert: alertId={}, eventType={}, entityId={}",
                payload.getAlertId(),
                alertDto.getAlertEventType(),
                alertDto.getEntityId());

        } catch (Exception e) {
            log.error("Failed to publish WMS exception alert: eventType={}, entityId={}",
                alertDto.getAlertEventType(), alertDto.getEntityId(), e);
        }
    }

    /**
     * Checks for incomplete picking tasks after 8PM LA time
     * Sends alert if picking tasks are not completed and current time is after 8PM LA
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void checkPickingTasksIncompleteAfter8PM(UUID batchId, String batchNumber,
        String vendorName, String source,
        String deliveryDate, List<PickingTask> incompleteTasks) {
        try {
            // Check if current LA time is after 8PM
            if (!isAfter8PMLosAngeles()) {
                log.info("Current LA time is before 8PM, no alert needed for batch: {}", batchId);
                return;
            }

            if (incompleteTasks == null || incompleteTasks.isEmpty()) {
                log.info("No incomplete picking tasks found for batch: {}", batchId);
                return;
            }

            String number = incompleteTasks.stream()
                .map(PickingTask::getNumber)
                .collect(Collectors.joining(","));

            String taskListUrl = String.format(PICKING_TASK_LIST, wmsAdminPortalBaseUrl, "number=" + number);

            WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createPickingTasksIncompleteAlert(
                batchId,
                batchNumber,
                vendorName,
                source,
                deliveryDate,
                taskListUrl
            );

            publishAlert(alertDto);

            log.warn("Picking tasks incomplete after 8PM alert sent: Batch {} has {} incomplete tasks for vendor {}",
                batchNumber != null ? batchNumber : batchId, incompleteTasks.size(), vendorName);

        } catch (Exception e) {
            log.error("Error sending picking tasks incomplete alert for batch: {}", batchId, e);
        }
    }

    /**
     * Sends alert for ghost inventory cleanup failure
     * This method directly publishes the alert for immediate notification
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void alertGhostInventoryCleanupFailure(UUID deliveryOrderId,
        String orderNumber,
        String deliveryItemsSummary,
        String errorMessage) {

        boolean featureOn = featureFlagsManager.isFeatureOn(FeatureFlagKeys.CLEAN_SHIP_SB_V1);
        if (!featureOn) {
            log.info("Feature flag CLEAN_SHIP_SB_V1 is off, skipping ghost inventory cleanup failure alert for order: {} (ID: {})",
                orderNumber, deliveryOrderId);
            return;
        }

        try {
            WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
                deliveryOrderId,
                orderNumber,
                deliveryItemsSummary,
                errorMessage
            );

            publishAlert(alertDto);

            log.warn("Ghost inventory cleanup failure alert sent: Order {} (ID: {}) requires manual recovery",
                orderNumber, deliveryOrderId);

        } catch (Exception e) {
            log.error("Error sending ghost inventory cleanup failure alert for order: {} (ID: {})",
                orderNumber, deliveryOrderId, e);
        }
    }

    /**
     * Alerts when a shipping order is fulfilled but not delivered
     * This method is designed to run independently without affecting parent transactions
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void alertShippingOrderFulfilledButNotDelivered(ShopifyOrderDto shopifyOrderDto, ShippingOrder shippingOrder) {

        boolean featureOn = featureFlagsManager.isFeatureOn(FeatureFlagKeys.CLEAN_SHIP_SB_V1);
        if (!featureOn) {
            log.info("Feature flag CLEAN_SHIP_SB_V1 is off, skipping fulfilled but not delivered alert for order: {} (ID: {})",
                shopifyOrderDto != null ? shopifyOrderDto.getName() : "N/A",
                shippingOrder != null ? shippingOrder.getId() : null);
            return;
        }

        // Validate input parameters
        if (shopifyOrderDto == null || shippingOrder == null) {
            log.warn("Cannot send alert: shopifyOrderDto or shippingOrder is null");
            return;
        }

        // Check fulfillment status
        List<String> fulfilledStatuses = List.of("partial", "fulfilled");
        String fulfillmentStatus = shopifyOrderDto.getFulfillmentStatus();

        if (StringUtils.isEmpty(fulfillmentStatus) || !fulfilledStatuses.contains(fulfillmentStatus)) {
            log.info("Shipping order {} is not fulfilled (status: {}), no alert needed",
                shippingOrder.getOrderNumber(), fulfillmentStatus);
            return;
        }

        // Calculate delivered quantity
        Integer deliveredQty = calculateDeliveredQuantity(shippingOrder);
        if (deliveredQty != null && deliveredQty > 0) {
            log.info("Shipping order {} has delivered quantity[ {}] info, no alert needed",
                shippingOrder.getOrderNumber(), deliveredQty);
            return;
        }

        try {
            // Prepare alert data
            AlertData alertData = prepareAlertData(shopifyOrderDto, shippingOrder);

            String orderDetailUrl = String.format(SHIPPING_ORDER_DETAIL, wmsAdminPortalBaseUrl, shippingOrder.getId());
            // Create and publish alert
            WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createShippingOrderFulfilledButNotDeliveredAlert(
                alertData.shippingOrderId(),
                alertData.orderNumber(),
                alertData.fulfillmentStatus(),
                alertData.facilityName(),
                orderDetailUrl
            );

            publishAlert(alertDto);

            log.warn("🚨 Fulfilled but not delivered alert sent: Order {} (ID: {}) - Fulfillment: {}",
                alertData.orderNumber(), alertData.shippingOrderId(),
                alertData.fulfillmentStatus());

        } catch (Exception e) {
            log.error("❌ Failed to send fulfilled but not delivered alert for order: {} (ID: {})",
                shopifyOrderDto.getName(), shippingOrder.getId(), e);
        }
    }

    /**
     * Calculates the total delivered quantity for a shipping order
     */
    private Integer calculateDeliveredQuantity(ShippingOrder shippingOrder) {
        if (shippingOrder.getShippingOrderItems() == null || shippingOrder.getShippingOrderItems().isEmpty()) {
            return null;
        }

        return shippingOrder.getShippingOrderItems()
            .stream()
            .filter(item -> item != null && item.getDeliveredQty() != null)
            .mapToInt(ShippingOrderItem::getDeliveredQty)
            .sum();
    }

    /**
     * Prepares alert data from shopify order and shipping order
     */
    private AlertData prepareAlertData(ShopifyOrderDto shopifyOrderDto, ShippingOrder shippingOrder) {
        UUID shippingOrderId = shippingOrder.getId();
        String orderNumber = shopifyOrderDto.getName();
        String fulfillmentStatus = shopifyOrderDto.getFulfillmentStatus();
        final String[] fulfilledCondition = {""};
        shippingOrder.getShippingOrderItems().forEach(shippingOrderItem -> {
            String skuNumber = shippingOrderItem.getSkuNumber();
            Integer fulfilledQty = shippingOrderItem.getFulfilledQty();
            Integer pickedQty = shippingOrderItem.getPickedQty();
            fulfilledCondition[0] = fulfilledCondition[0].concat("%s->%d/%d; ".formatted(skuNumber, fulfilledQty, pickedQty));
        });

        return new AlertData(
            shippingOrderId,
            orderNumber,
            finaleConfigProperties.getShipSb(),
            fulfillmentStatus,
            fulfilledCondition[0]
        );
    }

    /**
     * Data class for alert information
     */
    private record AlertData(
        UUID shippingOrderId,
        String orderNumber,
        String facilityName,
        String fulfillmentStatus,
        String fulfilledCondition
    ) {

    }

    /**
     * Checks if current time in Los Angeles is after 8:00 PM
     */
    private boolean isAfter8PMLosAngeles() {
        ZonedDateTime laTime = ZonedDateTime.now(DateUtils.LA_ZONE);
        int hour = laTime.getHour();
        return hour >= 20; // 20:00 (8PM) and later
    }

    /**
     * Checks if a duplicate alert already exists for the same entity and event type
     * This prevents spam and reduces noise in alert channels
     */
    private boolean isDuplicateAlert(WmsExceptionAlertDto alertDto) {
        log.info("[isDuplicateAlert] Alert start: {}", alertDto);
        if (alertDto.getEntityId() == null || alertDto.getAlertEventType() == null) {
            return false; // Cannot check duplicates without entity ID or event type
        }

        try {
            // Get recent events with proper sorting to ensure we check the latest ones
            // Sort by creation time descending to get the most recent events first
            Sort sort = Sort.by(Direction.DESC, "createdAt");
            Pageable pageable = PageRequest.of(0, 100, sort);

            BusinessEventPageDto<BusinessEventDto> existingEvents = businessEventClient.getEventByEntityIdAndEventTypes(
                String.valueOf(alertDto.getEntityId()),
                List.of(EventTypeEnums.WMS_EXCEPTION_ALERT.name()),
                pageable
            );

            log.info("[isDuplicateAlert] existingEvents found: {}", existingEvents);

            if (existingEvents.getContent().isEmpty()) {
                return false; // No existing events found
            }

            // Check if any existing event has the same alert event type within a reasonable time window
            // This prevents spam while allowing legitimate re-alerts after some time
            return existingEvents.getContent().stream()
                .anyMatch(event -> hasMatchingAlertType(event, alertDto.getAlertEventType()) &&
                    isWithinDuplicateTimeWindow(event));

        } catch (Exception e) {
            log.warn("Failed to check for duplicate alerts for entityId={}, eventType={}: {}",
                alertDto.getEntityId(), alertDto.getAlertEventType(), e.getMessage());
            return false; // On error, allow the alert to proceed
        }
    }

    /**
     * Checks if a business event contains a payload with the specified alert event type
     */
    private boolean hasMatchingAlertType(BusinessEventDto businessEvent, AlertEventType alertEventType) {
        if (businessEvent.getPayload() == null || alertEventType == null) {
            return false;
        }

        try {
            JsonNode payload = businessEvent.getPayload();
            JsonNode dataNode = payload.get("data");
            if (dataNode == null) {
                return false;
            }

            JsonNode alertEventTypeNode = dataNode.get("alertEventType");
            if (alertEventTypeNode == null || !alertEventTypeNode.isTextual()) {
                return false;
            }

            return alertEventTypeNode.textValue().equalsIgnoreCase(alertEventType.name());
        } catch (Exception e) {
            log.warn("Error checking alert type match for event: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a business event was created within the duplicate alert time window
     * This prevents alert spam by allowing re-alerts only after a certain time period
     */
    private boolean isWithinDuplicateTimeWindow(BusinessEventDto businessEvent) {
        if (businessEvent.getCreatedAt() == null) {
            log.warn("[isWithinDuplicateTimeWindow] Business event has no createdAt");
            return false; // If no creation time, don't consider it as duplicate
        }

        // Define duplicate alert time window (e.g., 1 hour)
        // Adjust this value based on business requirements
        long duplicateWindowMinutes = 60 * 24;

        ZonedDateTime eventTime = businessEvent.getCreatedAt().atZone(DateUtils.LA_ZONE);
        ZonedDateTime now = ZonedDateTime.now(DateUtils.LA_ZONE);

        long minutesSinceEvent = Duration.between(eventTime, now).toMinutes();

        // Return true if the event is within the duplicate time window
        boolean withinWindow = minutesSinceEvent < duplicateWindowMinutes;

        if (withinWindow) {
            log.info("Event found within duplicate time window: {} minutes ago", minutesSinceEvent);
        }

        return withinWindow;
    }

}