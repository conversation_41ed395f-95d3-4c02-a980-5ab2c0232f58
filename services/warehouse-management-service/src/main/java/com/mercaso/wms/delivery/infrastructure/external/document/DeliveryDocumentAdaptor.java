package com.mercaso.wms.delivery.infrastructure.external.document;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeliveryDocumentAdaptor {

    private final DocumentOperations documentOperations;

    @Value("${mercaso.document.operations.delivery.root-folder}")
    private String rootFolder;

    public String getSignedUrl(String documentName) {
        log.info("[getSignedUrl] Getting signed URL for document: {}", documentName);
        return documentOperations.getSignedUrl(rootFolder.concat(documentName));
    }

    public DocumentResponse uploadToS3(UploadDocumentRequest document) {
        document.setDocumentName(rootFolder.concat(document.getDocumentName()));
        DocumentResponse documentResponse = documentOperations.uploadDocument(document);

        documentResponse.setName(documentResponse.getName().replaceFirst(rootFolder, ""));
        return documentResponse;
    }

    public byte[] downloadDocument(String documentName) {
        return documentOperations.downloadDocument(rootFolder.concat(documentName));
    }

}
