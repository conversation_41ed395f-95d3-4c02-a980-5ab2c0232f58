package com.mercaso.wms.application.command.accountpreference;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class UpdateAccountPreferenceCommand extends BaseCommand {

    private AccountPreferenceStatus status;

    private UUID workWarehouseId;

    private Boolean isFullTime;

    private Integer dailyWorkHours;

    private UUID externalWarehouseId;

    private PickingTaskType taskType;

    private String preferredDepartment;

    private boolean autoAssign;

    private String name;

}
