package com.mercaso.wms.domain.crossdock;

import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.crossdock.enums.CrossDockTaskStatusEnum;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class CrossDockTask extends BaseDomain {

    private final UUID id;

    private String number;

    private UUID warehouseId;

    private CrossDockTaskStatusEnum status;

    private UUID pickerUserId;

    private String pickerUserName;

    private Integer totalQty;

    private String deliveryDate;

    private List<CrossDockTaskItem> crossDockTaskItems;

    public CrossDockTask createTask(UUID warehouseId, UUID pickerUserId, String pickerUserName, CrossDockTaskStatusEnum status, String deliveryDate) {
        this.warehouseId = warehouseId;
        this.status = status;
        this.pickerUserId = pickerUserId;
        this.pickerUserName = pickerUserName;
        this.totalQty = 0;
        this.crossDockTaskItems = new ArrayList<>();
        this.deliveryDate = deliveryDate;
        return this;
    }

    public void bindItem(CrossDockTaskItem crossDockTaskItem, String sequence) {
        crossDockTaskItem.setCrossDockTaskId(this.getId());

        List<String> currentSequences = crossDockTaskItem.getSequence();
        if (currentSequences == null || currentSequences.isEmpty()) {
            crossDockTaskItem.setSequence(List.of(sequence));
        } else if (!currentSequences.contains(sequence)) {
            List<String> updatedSequences = new ArrayList<>(currentSequences);
            updatedSequences.add(sequence);
            crossDockTaskItem.setSequence(updatedSequences);
        }
        crossDockTaskItem.setCrossDockedQty(1);
        crossDockTaskItems.add(crossDockTaskItem);
    }

}