package com.mercaso.wms.infrastructure.schedule;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.infrastructure.config.BatchProcessingProperties;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinaleTransferScheduler {

    private final BatchRepository batchRepository;

    private final PickingTaskApplicationService pickingTaskApplicationService;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final FeatureFlagsManager featureFlagsManager;

    private final BatchProcessingProperties batchProcessingProperties;

    private static final Integer LOCK_KEY = "FinaleTransferScheduler.finaleInventoryTransfer".hashCode();

    @Scheduled(cron = "0 55 * * * *", zone = "America/Los_Angeles")
    public void finaleInventoryTransfer() {
        finaleInventoryTransfer(null);
    }

    public void finaleInventoryTransfer(String deliveryDate) {
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(LOCK_KEY);
            if (Boolean.TRUE.equals(isAcquired)) {
                log.info("[finaleInventoryTransfer] FinaleTransferScheduler started ");
                LocalDate targetDeliveryDate;
                if (StringUtils.isEmpty(deliveryDate)) {
                    targetDeliveryDate = DateUtils.getNextDeliveryDate();
                } else {
                    targetDeliveryDate = LocalDate.parse(deliveryDate, DateTimeFormatter.ISO_LOCAL_DATE);
                }
                log.info("[finaleInventoryTransfer] Target delivery date: {}", targetDeliveryDate);

                // Query untransferred batches within configurable days before and after the delivery date
                LocalDate[] dateRange = DateUtils.getDeliveryDateRange(
                    targetDeliveryDate,
                    batchProcessingProperties.getDaysBefore(),
                    batchProcessingProperties.getDaysAfter());
                LocalDate startDate = dateRange[0];
                LocalDate endDate = dateRange[1];
                log.info("[finaleInventoryTransfer] Querying untransferred batches from {} to {} (daysBefore={}, daysAfter={})",
                    startDate, endDate, batchProcessingProperties.getDaysBefore(), batchProcessingProperties.getDaysAfter());

                List<Batch> batchList = batchRepository.findUntransferredBatchesByDateRange(startDate, endDate);
                if (CollectionUtils.isEmpty(batchList)) {
                    log.info("[finaleInventoryTransfer] No untransferred batches found in date range: {} to {}",
                        startDate,
                        endDate);
                    return;
                }
                log.info("[finaleInventoryTransfer] Found {} untransferred batches in date range", batchList.size());
                for (Batch batch : batchList) {
                    if (featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_FINALE_TRANSFER_FEATURE)) {
                        pickingTaskApplicationService.bulkInventoryTransfer(batch);
                    }
                }
                log.info("[finaleInventoryTransfer] FinaleTransferScheduler completed successfully");
            }
        } finally {
            pgAdvisoryLock.unLock(LOCK_KEY.hashCode());
        }
    }

}

