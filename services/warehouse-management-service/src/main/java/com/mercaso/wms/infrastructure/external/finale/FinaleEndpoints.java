package com.mercaso.wms.infrastructure.external.finale;

/**
 * Finale API endpoint identifiers for retry configuration
 * Centralizes endpoint naming to ensure consistency between code and configuration
 */
public final class FinaleEndpoints {
    
    private static final String SERVICE_PREFIX = "finale";
    
    // Internal endpoint names (used for rate limiting)
    public static final String ENDPOINT_FILL = "shipment.fill";
    public static final String ENDPOINT_PACK = "shipment.pack";
    public static final String ENDPOINT_INVENTORY_VARIANCE = "inventoryvariance.update";
    
    // Full endpoint keys for retry configuration (used in application.yml)
    public static final String RETRY_KEY_SHIPMENT_FILL = SERVICE_PREFIX + "." + ENDPOINT_FILL;
    public static final String RETRY_KEY_SHIPMENT_PACK = SERVICE_PREFIX + "." + ENDPOINT_PACK;
    public static final String RETRY_KEY_INVENTORY_VARIANCE = SERVICE_PREFIX + "." + ENDPOINT_INVENTORY_VARIANCE;
    
    private FinaleEndpoints() {
        // Utility class - prevent instantiation
    }
    
    /**
     * Get the full retry configuration key for an internal endpoint name
     * @param internalEndpoint the internal endpoint name (e.g., "shipment.fill")
     * @return the full retry key (e.g., "finale.shipment.fill")
     */
    public static String getRetryKey(String internalEndpoint) {
        return SERVICE_PREFIX + "." + internalEndpoint;
    }
    
    /**
     * List all available retry configuration keys for documentation/validation
     * @return array of all finale retry keys
     */
    public static String[] getAllRetryKeys() {
        return new String[] {
            RETRY_KEY_SHIPMENT_FILL,
            RETRY_KEY_SHIPMENT_PACK,
            RETRY_KEY_INVENTORY_VARIANCE
        };
    }
    
    /**
     * Get service prefix
     * @return the service prefix used for all finale endpoints
     */
    public static String getServicePrefix() {
        return SERVICE_PREFIX;
    }
}
