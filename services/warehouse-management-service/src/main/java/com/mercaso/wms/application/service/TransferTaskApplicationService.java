package com.mercaso.wms.application.service;

import static com.mercaso.wms.infrastructure.exception.ErrorCodeEnums.TRANSFER_TASK_ACTIVATED;
import static com.mercaso.wms.infrastructure.exception.ErrorCodeEnums.TRANSFER_TASK_LOADED;
import static com.mercaso.wms.infrastructure.exception.ErrorCodeEnums.TRANSFER_TASK_NOT_FOUND;
import static com.mercaso.wms.infrastructure.exception.ErrorCodeEnums.TRANSFER_TASK_NO_ITEMS;
import static com.mercaso.wms.infrastructure.exception.ErrorCodeEnums.TRANSFER_TASK_RECEIVED;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.application.command.transfertask.CreateTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.LoadTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.ReceiveTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.UpdateTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.UpdateTransferTaskCommand.UpdateTransferTaskItemDto;
import com.mercaso.wms.application.dto.event.TransferTaskActivatedPayloadDto;
import com.mercaso.wms.application.dto.event.TransferTaskCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.TransferTaskLoadedPayloadDto;
import com.mercaso.wms.application.dto.event.TransferTaskReceivedPayloadDto;
import com.mercaso.wms.application.dto.transfertask.TransferTaskDto;
import com.mercaso.wms.application.mapper.transfertask.TransferTaskDtoApplicationMapper;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.transfertask.TransferTask;
import com.mercaso.wms.domain.transfertask.TransferTaskRepository;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class TransferTaskApplicationService {

    private final TransferTaskRepository transferTaskRepository;
    private final WarehouseRepository warehouseRepository;
    private final LocationRepository locationRepository;
    private final ImsAdaptor imsAdaptor;
    private final BusinessEventDispatcher businessEventDispatcher;
    private final FinaleConfigProperties finaleConfigProperties;

    public TransferTaskDto getTransferTask(UUID transferTaskId) {
        TransferTask transferTask = transferTaskRepository.findById(transferTaskId);
        if (transferTask == null) {
            throw new WmsBusinessException("Transfer task not found");
        }
        return TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(transferTask);
    }

    public TransferTaskDto createTransferTask(CreateTransferTaskCommand command) {
        log.info("[createTransferTask] command: {}", command);
        Warehouse originWarehouse = getWarehouse(command.getOriginWarehouseId(), "MFC");
        Warehouse destinationWarehouse = getWarehouse(command.getDestinationWarehouseId(), "MDC");

        Location originLocation = getStagingLocation(finaleConfigProperties.getMfcStage());
        Location destinationLocation = getStagingLocation(finaleConfigProperties.getMdcStage());

        if (originWarehouse == null || destinationWarehouse == null) {
            log.error(
                "[createTransferTask] Warehouse and destination are not set, originWarehouse: {}, destinationWarehouse: {}",
                originWarehouse,
                destinationWarehouse);
            throw new WmsBusinessException("Invalid warehouse or location");
        }

        if (originLocation == null || destinationLocation == null) {
            log.warn("[createTransferTask] Staging location not found, originLocation: {}, destinationLocation: {}",
                originLocation,
                destinationLocation);
        }

        Map<UUID, ItemCategoryDto> itemCategoriesMap = null;
        if (!CollectionUtils.isEmpty(command.getTransferTaskItems())) {
            itemCategoriesMap = getItemCategoriesMap(command.getTransferTaskItems().stream()
                .map(CreateTransferTaskCommand.CreateTransferTaskItemDto::getItemId).toList());
        }

        TransferTask transferTask = TransferTask.builder()
            .build()
            .create(originWarehouse, destinationWarehouse, originLocation, destinationLocation, command, itemCategoriesMap);

        TransferTask savedTask = transferTaskRepository.save(transferTask);
        log.info("[createTransferTask]Transfer task created: {}", savedTask);
        dispatchEvents(savedTask.getId(),
            savedTask.getStatus(),
            savedTask.getStatus(),
            TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(savedTask));
        return TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(savedTask);
    }

    public TransferTaskDto updateTransferTask(UpdateTransferTaskCommand command) {
        log.info("[updateTransferTask] command: {}", command);
        TransferTask transferTask = getTask(command.getTransferTaskId());
        if (CollectionUtils.isEmpty(command.getTransferTaskItems())) {
            throw new WmsBusinessException("No items provided for the transfer task");
        }

        Location originLocation = getStagingLocation(finaleConfigProperties.getMfcStage());
        Location destinationLocation = getStagingLocation(finaleConfigProperties.getMdcStage());
        List<UUID> itemIds = command.getTransferTaskItems().stream()
            .map(UpdateTransferTaskItemDto::getItemId)
            .distinct()
            .toList();

        Map<UUID, ItemCategoryDto> itemCategoriesMap = getItemCategoriesMap(itemIds);
        transferTask.update(command, itemCategoriesMap, originLocation, destinationLocation);
        TransferTask savedTask = transferTaskRepository.save(transferTask);
        return TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(savedTask);
    }

    private Warehouse getWarehouse(UUID warehouseId, String defaultName) {
        return warehouseId != null ? warehouseRepository.findById(warehouseId) : warehouseRepository.findByName(defaultName);
    }

    private Location getStagingLocation(String locationName) {
        return locationRepository.findByName(locationName);
    }

    private void dispatchEvents(UUID transferTaskId,
        TransferTaskStatus originStatus,
        TransferTaskStatus newStatus,
        TransferTaskDto transferTaskDto) {
        log.info("[dispatchEvents] transferTaskId: {}, originStatus: {}, newStatus: {}",
            transferTaskId,
            originStatus,
            newStatus);
        if (newStatus == TransferTaskStatus.DRAFT) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(TransferTaskCreatedPayloadDto.builder()
                .transferTaskId(transferTaskId)
                .data(transferTaskDto)
                .build()));
        } else if (originStatus != newStatus && newStatus == TransferTaskStatus.READY_TO_LOAD) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(TransferTaskActivatedPayloadDto.builder()
                .transferTaskId(transferTaskId)
                .data(transferTaskDto)
                .build()));
        } else if (newStatus == TransferTaskStatus.IN_TRANSIT) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(TransferTaskLoadedPayloadDto.builder()
                .data(transferTaskDto)
                .transferTaskId(transferTaskId)
                .build()));
        } else if (newStatus == TransferTaskStatus.RECEIVED) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(TransferTaskReceivedPayloadDto.builder()
                .data(transferTaskDto)
                .transferTaskId(transferTaskId)
                .build()));
        }
    }

    public TransferTaskDto load(UUID transferTaskId, LoadTransferTaskCommand command) {
        log.info("[load] transferTaskId: {}, command: {}", transferTaskId, command);
        TransferTask transferTask = getTask(transferTaskId);
        if (transferTask.getState() == TransferTaskStatus.IN_TRANSIT) {
            throw new WmsBusinessException(TRANSFER_TASK_LOADED.getCode(), TRANSFER_TASK_LOADED.getMessage());
        }
        if (CollectionUtils.isEmpty(transferTask.getTransferTaskItems())) {
            throw new WmsBusinessException(TRANSFER_TASK_NO_ITEMS.getCode(), TRANSFER_TASK_NO_ITEMS.getMessage());
        }

        transferTask.loaded();
        transferTask.setTruckNumber(command != null ? command.getTruckNumber() : null);
        TransferTaskDto transferTaskDto = TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(transferTaskRepository.save(
            transferTask));
        log.info("[load] transferTaskDto: {}", transferTaskDto);
        dispatchEvents(transferTaskId, TransferTaskStatus.READY_TO_LOAD, transferTask.getState(), transferTaskDto);
        return transferTaskDto;
    }

    public TransferTaskDto receive(UUID transferTaskId, ReceiveTransferTaskCommand command) {
        log.info("[receive] transferTaskId: {}", transferTaskId);
        TransferTask transferTask = getTask(transferTaskId);
        if (transferTask.getState() == TransferTaskStatus.RECEIVED) {
            throw new WmsBusinessException(TRANSFER_TASK_RECEIVED.getCode(), TRANSFER_TASK_RECEIVED.getMessage());
        }
        if (CollectionUtils.isEmpty(transferTask.getTransferTaskItems())) {
            throw new WmsBusinessException(TRANSFER_TASK_NO_ITEMS.getCode(), TRANSFER_TASK_NO_ITEMS.getMessage());
        }

        if (command != null && StringUtils.isNotEmpty(command.getDestinationLocationName())) {
            if (!transferTask.getTransferTaskItems()
                .getFirst()
                .getDestinationLocationName()
                .equals(command.getDestinationLocationName())) {
                throw new WmsBusinessException("location name is not matched");
            }
        }
        transferTask.received();
        TransferTaskDto transferTaskDto = TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(transferTaskRepository.save(
            transferTask));
        log.info("[receive] transferTaskDto: {}", transferTaskDto);
        dispatchEvents(transferTaskId, TransferTaskStatus.IN_TRANSIT, transferTask.getState(), transferTaskDto);
        return transferTaskDto;
    }

    public TransferTaskDto activate(UUID transferTaskId) {
        TransferTask transferTask = getTask(transferTaskId);
        if (transferTask.getState() == TransferTaskStatus.READY_TO_LOAD) {
            throw new WmsBusinessException(TRANSFER_TASK_ACTIVATED.getCode(), TRANSFER_TASK_ACTIVATED.getMessage());
        }
        transferTask.activate();
        TransferTaskDto transferTaskDto = TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(transferTaskRepository.save(
            transferTask));
        dispatchEvents(transferTaskId, TransferTaskStatus.DRAFT, transferTask.getState(), transferTaskDto);
        return transferTaskDto;
    }

    public TransferTaskDto revertToDraft(UUID transferTaskId) {
        log.info("[revertToDraft] transferTaskId: {}", transferTaskId);
        TransferTask transferTask = getTask(transferTaskId);
        if (transferTask.getState() == TransferTaskStatus.DRAFT) {
            return TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(transferTask);
        }
        transferTask.revertToDraft();
        TransferTaskDto transferTaskDto = TransferTaskDtoApplicationMapper.INSTANCE.domainToDto(transferTaskRepository.save(
            transferTask));
        log.info("[revertToDraft] transferTaskDto: {}", transferTaskDto);
        dispatchEvents(transferTaskId, TransferTaskStatus.READY_TO_LOAD, transferTask.getState(), transferTaskDto);
        return transferTaskDto;
    }

    private TransferTask getTask(UUID transferTaskId) {
        TransferTask transferTask = transferTaskRepository.findById(transferTaskId);
        if (transferTask == null) {
            throw new WmsBusinessException(TRANSFER_TASK_NOT_FOUND.getCode(), TRANSFER_TASK_NOT_FOUND.getMessage());
        }
        return transferTask;
    }

    private Map<UUID, ItemCategoryDto> getItemCategoriesMap(List<UUID> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Map.of();
        }
        List<ItemCategoryDto> items = imsAdaptor.getItemsByIds(itemIds);
        if (CollectionUtils.isEmpty(items)) {
            throw new WmsBusinessException("No items found for the provided IDs");
        }
        return items.stream().collect(Collectors.toMap(ItemCategoryDto::getId, item -> item));
    }
}