package com.mercaso.wms.delivery.application.service;

import static com.mercaso.wms.infrastructure.external.ums.UmsAdaptor.DRIVER_ROLE;
import static com.mercaso.wms.infrastructure.utils.RSAEncryptor.encrypt;

import com.mercaso.user.client.dto.CreateUserRequest;
import com.mercaso.user.client.dto.UserDto;
import com.mercaso.wms.delivery.application.command.account.CreateAccountCommand;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverRequest;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.external.ums.UmsAdaptor;
import com.mercaso.wms.infrastructure.utils.RandomKeyGenerationUtils;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class RouteManagerService {

    private final RouteManagerAdaptor routeManagerAdaptor;
    private final AccountRepository accountRepository;
    private final UmsAdaptor umsAdaptor;
    private final WarehouseRepository warehouseRepository;

    @Value("${account.driver.creation.timeout.seconds:10}")
    private int driverCreationTimeoutSeconds;

    @Value("${account.driver.creation.poll.interval.seconds:1}")
    private int driverCreationPollIntervalSeconds;

    @Value("${account.secret.public-key}")
    private String publicKey;

    // Use class-level lock objects
    private final Object driverCreationLock = new Object();

    public Account addDriver(Account account) {
        log.info("Adding driver to RouteManager, email: {}", account.getEmail());

        AddDriverRequest driverRequest = new AddDriverRequest();
        driverRequest.setName(account.getUserName());
        driverRequest.setEmail(account.getEmail());

        AddDriverResponse response = routeManagerAdaptor.addDriver(driverRequest);

        if (response == null || response.getRequestId() == null) {
            throw new DeliveryBusinessException("Failed to add driver to RouteManager");
        }

        log.info("Driver added to RouteManager successfully, requestId: {}", response.getRequestId());

        waitForDriverCreation(account.getEmail());
        return account;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<Account> pullFromRouteManager() {
        log.info("Pulling drivers from RouteManager");

        List<Driver> drivers = routeManagerAdaptor.listDrivers();
        if (CollectionUtils.isEmpty(drivers)) {
            log.info("No drivers found in RouteManager");
            return accountRepository.findAllActiveDriver();
        }

        log.info("Found {} drivers in RouteManager: {}",
            drivers.size(), drivers.stream().map(Driver::getEmail).toList());

        List<Account> existingDrivers = accountRepository.findAll();
        Map<String, Account> emailToAccountMap = buildEmailToAccountMap(existingDrivers);

        DriverClassification classification = classifyDrivers(drivers, emailToAccountMap);

        updateDriversIfNeeded(classification.emailUpdatedDrivers());

        log.info("Route manager removed drivers: {}", classification.removedDrivers());

        if (CollectionUtils.isEmpty(classification.newDrivers())) {
            log.info("No new drivers found in RouteManager");
            return accountRepository.findAllActiveDriver();
        }

        createNewDriverAccounts(classification.newDrivers());

        return accountRepository.findAllActiveDriver();
    }

    private record DriverClassification(
        List<Driver> newDrivers,
        List<Account> removedDrivers,
        List<Account> emailUpdatedDrivers
    ) {

    }

    private DriverClassification classifyDrivers(List<Driver> drivers, Map<String, Account> emailToAccountMap) {
        Set<String> currentDriverEmails = drivers.stream()
            .map(driver -> driver.getEmail().toLowerCase())
            .collect(Collectors.toSet());

        List<Driver> newDrivers = new ArrayList<>();
        List<Account> emailUpdatedDrivers = new ArrayList<>();

        drivers.forEach(driver -> {
            String driverEmailLower = driver.getEmail().toLowerCase();
            Account existingAccount = emailToAccountMap.get(driverEmailLower);

            if (existingAccount == null) {
                newDrivers.add(driver);
            } else {
                existingAccount.update(driver.getName(), driver.getEmail(), AccountStatus.ACTIVE);
                emailUpdatedDrivers.add(existingAccount);
                log.info("Updating driver: {}, email: {}", driver.getName(), existingAccount.getEmail());
            }
        });

        List<Account> removedDrivers = emailToAccountMap.values().stream()
            .filter(account -> !currentDriverEmails.contains(account.getEmail().toLowerCase()))
            .toList();

        log.info("Driver classification - New: {}, Updated: {}, Removed: {}",
            newDrivers.size(), emailUpdatedDrivers.size(), removedDrivers.size());

        return new DriverClassification(newDrivers, removedDrivers, emailUpdatedDrivers);
    }

    private Map<String, Account> buildEmailToAccountMap(List<Account> existingDrivers) {
        return existingDrivers.stream()
            .collect(Collectors.toMap(
                account -> account.getEmail().toLowerCase(),
                Function.identity()
            ));
    }

    private void updateDriversIfNeeded(List<Account> needToUpdatedDrivers) {
        if (CollectionUtils.isNotEmpty(needToUpdatedDrivers)) {
            List<Account> accounts = accountRepository.saveAll(needToUpdatedDrivers);
            log.info("Updated {} existing driver accounts with new emails", accounts.size());
        }
    }

    private void createNewDriverAccounts(List<Driver> newDrivers) {
        Warehouse mdc = warehouseRepository.findByName("MDC");
        createNewDriverAccounts(newDrivers, mdc);
        log.info("Created {} new driver accounts", newDrivers.size());
    }

    private void createNewDriverAccounts(List<Driver> newDrivers, Warehouse mdc) {
        for (Driver newDriver : newDrivers) {
            try {
                UUID userId;
                String secretKey = null;

                UserDto user = umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(newDriver.getEmail());

                if (user != null) {
                    userId = user.getId();
                    umsAdaptor.assignDriverRolesToUser(userId);
                    log.warn("User already exists in UMS, email: {}", newDriver.getEmail());
                } else {
                    log.info("Creating new user in UMS, email: {}", newDriver.getEmail());

                    CreateUserRequest request = new CreateUserRequest();
                    request.setEmail(newDriver.getEmail());
                    request.setName(newDriver.getName());
                    secretKey = RandomKeyGenerationUtils.generateSecureRandomKeyV2();
                    request.setPassword(secretKey);
                    UserDto userDto = umsAdaptor.createUser(request, DRIVER_ROLE);
                    userId = userDto.getId();
                }

                CreateAccountCommand command = CreateAccountCommand.builder()
                    .email(newDriver.getEmail())
                    .userName(newDriver.getName())
                    .warehouseId(mdc.getId())
                    .build();
                Account account = Account.create(command, userId, encrypt(command.getEmail(), secretKey, publicKey));
                Account savedAccount = accountRepository.save(account);
                log.info("Created new account for newDriver: {}", savedAccount.getEmail());
            } catch (Exception e) {
                log.error("Error creating account for newDriver: {}", newDriver.getEmail(), e);
            }
        }
    }

    private void waitForDriverCreation(String email) {
        log.info("Waiting for driver creation in RouteManager, email: {}", email);
        Instant startTime = Instant.now();
        Instant timeoutTime = startTime.plusSeconds(driverCreationTimeoutSeconds);
        int attemptCount = 0;
        int maxAttempts = driverCreationTimeoutSeconds / driverCreationPollIntervalSeconds;

        while (attemptCount < maxAttempts) {
            attemptCount++;
            log.info("Checking driver creation, attempt {}/{}", attemptCount, maxAttempts);

            Optional<Driver> driver = findDriverByEmail(email);
            if (driver.isPresent()) {
                log.info("Driver created successfully in RouteManager, ID: {}", driver.get().getId());
                return;
            }

            if (Instant.now().isAfter(timeoutTime)) {
                break;
            }

            try {
                // Synchronize using class-level lock objects
                synchronized (driverCreationLock) {
                    driverCreationLock.wait(Duration.ofSeconds(driverCreationPollIntervalSeconds).toMillis());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new DeliveryBusinessException("Interrupted while waiting for driver creation");
            }
        }

        throw new DeliveryBusinessException(
            "Timeout waiting for driver creation in RouteManager after " + attemptCount + " attempts");
    }

    private Optional<Driver> findDriverByEmail(String email) {
        return routeManagerAdaptor.listDrivers().stream()
            .filter(driver -> driver.getEmail().equalsIgnoreCase(email))
            .findFirst();
    }
}
