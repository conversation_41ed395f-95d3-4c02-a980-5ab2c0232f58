package com.mercaso.wms.infrastructure.config.retry;

import com.mercaso.wms.infrastructure.external.finale.FinaleEndpoints;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Validates retry configuration on application startup
 * Ensures endpoint names in configuration match defined constants
 */
@Slf4j
@Component
public class RetryConfigValidator {

    private final RetryConfigProperties retryConfigProperties;

    public RetryConfigValidator(RetryConfigProperties retryConfigProperties) {
        this.retryConfigProperties = retryConfigProperties;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void validateRetryConfiguration() {
        log.info("Validating retry configuration...");

        Set<String> configuredEndpoints = retryConfigProperties.getEndpoints().keySet();
        Set<String> knownFinaleEndpoints = Set.of(FinaleEndpoints.getAllRetryKeys());

        // Log configured endpoints
        if (!configuredEndpoints.isEmpty()) {
            log.info("Configured retry endpoints: {}", configuredEndpoints);
        }

        // Validate finale endpoints
        for (String configuredEndpoint : configuredEndpoints) {
            if (configuredEndpoint.startsWith("finale.")) {
                if (!knownFinaleEndpoints.contains(configuredEndpoint)) {
                    log.warn("Unknown finale endpoint configured: '{}'. Known endpoints: {}",
                        configuredEndpoint, knownFinaleEndpoints);
                }
            }
        }

        // Log available endpoints for reference
        log.info("Available finale retry endpoints: {}", knownFinaleEndpoints);

        log.info("Retry configuration validation completed");
    }
}
