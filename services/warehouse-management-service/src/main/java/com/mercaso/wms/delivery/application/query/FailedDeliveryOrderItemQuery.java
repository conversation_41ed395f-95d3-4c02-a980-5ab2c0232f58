package com.mercaso.wms.delivery.application.query;

import com.mercaso.wms.application.query.SortType;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class FailedDeliveryOrderItemQuery {

    private String deliveryDate;

    private List<String> orderNumbers;

    private List<String> skuNumbers;

    private List<String> reasonCodes;

    @Min(value = 1, message = "Page number must be greater than 0")
    private int page;

    @Min(value = 1, message = "Page size must be greater than 0")
    private int pageSize;

    private List<SortType> sortTypes;
}
