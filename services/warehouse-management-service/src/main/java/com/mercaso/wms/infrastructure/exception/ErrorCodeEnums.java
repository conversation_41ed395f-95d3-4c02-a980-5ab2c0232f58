package com.mercaso.wms.infrastructure.exception;

import lombok.Getter;

@Getter
public enum ErrorCodeEnums {

    COMMON_CODE("WMS0000", "An error occurred, please try again later."),

    //    PICKING TASK
    PICKING_TASK_START_ERROR("WMS0001", "This picking task not belongs to you. Please refresh the page."),
    PICKING_TASK_CANCELED("WMS0002", "The picking task has been canceled."),

    //    SHIPPING ORDER ITEM
    SHIPPING_ORDER_ITEM_NOT_FOUND("WMS0010", "Shipping order item not found."),
    SHIPPING_ORDER_ITEM_HAS_PICKED("WMS0011", "Shipping order item has picked."),
    SHIPPING_ORDER_NOT_FOUND("WMS0012", "Shipping order not found."),
    SHIPPING_ORDER_INVALID_STATUS("WMS0013", "Shipping order has invalid status, the current status is %s."),

    //    WAREHOUSE
    WAREHOUSE_NOT_FOUND("WMS0020", "Warehouse not found."),

    //   RECEIVING TASK
    RECEIVING_TASK_NOT_FOUND("WMS0030", "Receiving task not found."),
    RECEIVING_TASK_ITEM_NOT_FOUND("WMS0031", "Receiving task item not found."),
    RECEIVING_TASK_ITEM_HAS_RECEIVED("WMS0032", "Receiving task item has received."),
    RECEIVING_TASK_HAS_RECEIVED("WMS0034", "Receiving task has received."),
    INVALID_VENDOR("WMS0035", "Invalid vendor provided."),
    INVALID_RECEIVING_TASK_STATUS("WMS0036", "Invalid receiving task status for this operation."),

    //   TRANSFER TASK
    TRANSFER_TASK_NOT_FOUND("WMS0040", "Transfer task not found."),
    TRANSFER_TASK_LOADED("WMS0041", "Transfer task has been loaded."),
    TRANSFER_TASK_RECEIVED("WMS0042", "Transfer task has been received."),
    TRANSFER_TASK_ACTIVATED("WMS0043", "Transfer task has been activated."),
    TRANSFER_TASK_DRAFTED("WMS0044", "Transfer task has been drafted."),
    TRANSFER_TASK_NO_ITEMS("WMS0045", "Transfer tasks do not have items."),
    // CROSS DOCK TASK
    CROSS_DOCK_TASK_ITEM_SEQUENCE_DUPLICATE("WMS0046", "This item has been scanned."),
    CROSS_DOCK_TASK_ITEM_ALREADY_BOUND("WMS0048", "This item is already bound to another cross dock task."),
    CROSS_DOCK_TASK_ITEM_OUTNUMBER("WMS0047", "More items are scanned than picked item quantities."),
    CROSS_DOCK_TASK_ITEMS_ALL_ASSIGNED("WMS0049", "All cross dock task items have been assigned."),
    CROSS_DOCK_TASK_ITEMS_NOT_FOUND("WMS0050", "Cross dock task items not found for shipping order item."),

    //   DELIVERY ORDER
    DELIVERY_ORDER_NOT_FOUND("DA0010", "Delivery order not found."),
    DELIVERY_ORDER_NOT_ARRIVED("DA0011", "Delivery order is not in arrived status."),
    DELIVERY_ORDER_CANCELED("DA0012", "Delivery order is already canceled."),
    DELIVERY_ORDER_INVALID_STATUS("DA0013", "Delivery order has invalid status, the current status is %s."),
    //   DELIVERY TASK
    DELIVERY_TASK_CANNOT_BE_BUILT("DA0020",
        "The delivery task cannot be built due to the presence of a task already in progress."),
    DELIVERY_TASK_NOT_FOUND("DA0021", "Delivery task not found."),
    DELIVERY_TASK_ROUTE_NOT_FOUND("DA0022", "Delivery task route not found."),
    DELIVERY_TASK_INVALID_DATA("DA0023", "Delivery task has invalid data."),
    DELIVERY_ROUTE_NOT_FOUND("DA0024", "Delivery route not found."),
    DELIVERY_ROUTE_ORDER_NOT_FOUND("DA0025", "Delivery route or order not found."),
    DELIVERY_TASK_INVALID_STATUS("DA0026", "Delivery task has invalid status, the current status is %s."),
    DELIVERY_TASK_ORDERS_NOT_COMPLETE("DA0027", "Available status are delivered, canceled or reschedule type is not null."),
    DELIVERY_TASK_CAN_NOT_REASSIGN("DA0028", "Delivery task can not reassign since task already completed."),
    DELIVERY_TASK_REASSIGNED("DA0029", "Delivery task is reassigned."),

    //   Driver ACCOUNT
    DRIVER_ACCOUNT_NOT_FOUND("DA0030", "Ner driver account not found."),
    DRIVER_ACCOUNT_NOT_ACTIVE("DA0031", "Driver account is not active."),

    // Payment summary
    DELIVERY_PAYMENT_NOT_FOUND("DA0040", "Payment summary not found for the selected delivery date."),

    ;

    private String code;

    private String message;

    ErrorCodeEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
