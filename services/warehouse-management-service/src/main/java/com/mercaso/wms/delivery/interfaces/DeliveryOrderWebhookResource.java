package com.mercaso.wms.delivery.interfaces;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.ims.client.dto.ItemSerachDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderAdminForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.service.DeliveryOrderApplicationService;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Shopify Delivery Order Webhook")
@Slf4j
@Validated
@RestController
@RequestMapping("/shopify/delivery-order")
@RequiredArgsConstructor
public class DeliveryOrderWebhookResource {

    @Value("${shopify.webhook.domain}")
    private String shopifyWebhookDomain;

    private final DeliveryOrderApplicationService deliveryOrderApplicationService;

    private final ShopifyAdaptor shopifyAdaptor;

    private final ImsAdaptor imsAdaptor;

    @PostMapping("/webhook")
    public void handleWebhook(@RequestBody String payload,
        @RequestHeader("X-Shopify-Shop-Domain") String shopDomain) {
        if (!shopDomain.equals(shopifyWebhookDomain)) {
            log.error("Received webhook for unknown domain: {}", shopDomain);
            return;
        }
        try {
            ShopifyOrderForDeliveryDto shopifyOrderDto = SerializationUtils.readValue(payload, new TypeReference<>() {
            });
            if (CollectionUtils.isEmpty(shopifyOrderDto.getLineItems())) {
                log.warn("Shopify delivery Order {} line items is empty for webhook, id: {}",
                    shopifyOrderDto.getName(),
                    shopifyOrderDto.getId());
                ShopifyOrderAdminForDeliveryDto shopifyOrderForDeliveryByAdminApi = shopifyAdaptor.getShopifyOrderForDeliveryByAdminApi(
                    shopifyOrderDto.getId());
                shopifyOrderDto = shopifyOrderForDeliveryByAdminApi.getOrder();
            }
            populateNullSkuLineItems(shopifyOrderDto);
            log.info("Received Shopify webhook for delivery order number: {} {}",
                shopifyOrderDto.getName(),
                shopifyOrderDto.getId());
            deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);
        } catch (IOException e) {
            String orderId = getShopifyOrderId(payload);
            if (orderId != null) {
                ShopifyOrderAdminForDeliveryDto shopifyOrderDto = shopifyAdaptor.getShopifyOrderForDeliveryByAdminApi(orderId);
                deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto.getOrder());
            } else {
                log.error("Failed to deserialize Shopify webhook payload: {}", payload);
            }
        }
    }

    private void populateNullSkuLineItems(ShopifyOrderForDeliveryDto shopifyOrderForDeliveryDto) {
        boolean hasNullSku = shopifyOrderForDeliveryDto.getLineItems().stream().anyMatch(item -> item.getSku() == null);
        if (hasNullSku) {
            shopifyOrderForDeliveryDto.getLineItems().forEach(item -> {
                if (item.getSku() == null) {
                    ItemSerachDto itemSerachDto = imsAdaptor.searchItemsByTitle(item.getTitle());
                    if (itemSerachDto != null) {
                        item.setSku(itemSerachDto.getSkuNumber());
                    } else {
                        log.error("No SKU found for delivery item with title: {}", item.getTitle());
                    }
                }
            });
        }
    }

    @PreAuthorize("hasAuthority('da:write:delivery-orders')")
    @PutMapping("/fill-item-info")
    public void fillItemInfo(@RequestBody List<String> shopifyOrderIds) {
        shopifyOrderIds.forEach(shopifyOrderId -> {
            log.info("Filling item info for shopify order: {}", shopifyOrderId);
            ShopifyOrderAdminForDeliveryDto shopifyOrderDto = shopifyAdaptor.getShopifyOrderForDeliveryByAdminApi(shopifyOrderId);
            deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto.getOrder());
        });
    }

    private ShopifyOrderForDeliveryDto getShopifyOrderDto(ShopifyOrderForDeliveryDto shopifyOrderDto) {
        if (CollectionUtils.isEmpty(shopifyOrderDto.getLineItems()) || shopifyOrderDto.getLineItems()
            .stream()
            .anyMatch(item -> item.getSku() == null)) {
            log.warn("Shopify Order {} line items is empty for webhook, id: {}",
                shopifyOrderDto.getName(),
                shopifyOrderDto.getId());
            ShopifyOrderAdminForDeliveryDto shopifyOrderByAdminApi = shopifyAdaptor.getShopifyOrderForDeliveryByAdminApi(
                shopifyOrderDto.getId());
            shopifyOrderDto = shopifyOrderByAdminApi.getOrder();
            shopifyOrderDto.getLineItems().forEach(item -> {
                if (item.getSku() == null) {
                    ItemSerachDto itemSerachDto = imsAdaptor.searchItemsByTitle(item.getTitle());
                    if (itemSerachDto != null) {
                        item.setSku(itemSerachDto.getSkuNumber());
                    } else {
                        log.error("No SKU found for item with title: {}", item.getTitle());
                    }
                }
            });
        }
        return shopifyOrderDto;
    }

    private String getShopifyOrderId(String payload) {
        String regex = "\"id\":\"(\\d+)\"";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(payload);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
