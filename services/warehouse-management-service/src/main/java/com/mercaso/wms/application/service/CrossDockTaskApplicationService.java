package com.mercaso.wms.application.service;

import com.mercaso.wms.application.command.crossdock.BindItemAndTaskCommand;
import com.mercaso.wms.application.command.crossdock.BindItemAndTaskCommand.BindItemAndTaskItem;
import com.mercaso.wms.application.command.crossdock.CreateCrossDockTaskCommand;
import com.mercaso.wms.application.dto.CrossDockTaskDto;
import com.mercaso.wms.application.mapper.crossdock.CrossDockTaskDtoApplicationMapper;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.AccountPreferenceRepository;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import com.mercaso.wms.domain.crossdock.enums.CrossDockTaskStatusEnum;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrderService;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItemRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class CrossDockTaskApplicationService {

    private final ShippingOrderService shippingOrderService;
    private final CrossDockTaskRepository crossDockTaskRepository;
    private final CrossDockTaskItemRepository crossDockTaskItemRepository;
    private final PickingTaskItemRepository pickingTaskItemRepository;
    private final ReceivingTaskItemRepository receivingTaskItemRepository;
    private final AccountPreferenceRepository accountPreferenceRepository;
    private final CrossDockTaskDtoApplicationMapper crossDockTaskDtoApplicationMapper;
    private final WarehouseRepository warehouseRepository;
    private final PickingTaskRepository pickingTaskRepository;
    private final ReceivingTaskRepository receivingTaskRepository;
    private final ShippingOrderItemRepository shippingOrderItemRepository;
    private final PgAdvisoryLock pgAdvisoryLock;

    @Transactional
    public CrossDockTaskDto createCrossDockTask(CreateCrossDockTaskCommand command) {
        UUID pickerUserId = command.getPickerUserId();
        String deliveryDate = DateUtils.getNextDeliveryDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        CrossDockTask existTask = crossDockTaskRepository.findByDeliveryDateAndPickerUserId(deliveryDate, pickerUserId);
        if (existTask != null) {
            return crossDockTaskDtoApplicationMapper.domainToDto(existTask);
        }
        AccountPreference picker = accountPreferenceRepository.findByUserId(pickerUserId);
        if (picker == null) {
            log.warn("Picker not found, pickerUserId={}", pickerUserId);
            throw new IllegalArgumentException("Picker not found");
        }
        Warehouse warehouse = warehouseRepository.findByName("MDC");
        if (warehouse == null) {
            log.error("Warehouse MDC not found");
            throw new IllegalArgumentException("Warehouse MDC not found");
        }
        UUID warehouseId = warehouse.getId();
        String pickerUserName = picker.getUserName();

        CrossDockTask crossDockTask = CrossDockTask.builder().build()
            .createTask(warehouseId, pickerUserId, pickerUserName, CrossDockTaskStatusEnum.COMPLETED, deliveryDate);

        CrossDockTask savedTask = crossDockTaskRepository.save(crossDockTask);

        return crossDockTaskDtoApplicationMapper.domainToDto(savedTask);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public CrossDockTaskDto bindItems(UUID crossDockTaskId, BindItemAndTaskCommand command) {
        log.info("[bindItems] Bind cross dock task with id {}", crossDockTaskId);

        if (crossDockTaskId == null) {
            log.error("[bindItems] Cross dock task id is null");
            throw new IllegalArgumentException("Cross dock task id cannot be null");
        }

        if (command == null || CollectionUtils.isEmpty(command.getItems())) {
            log.warn("[bindItems] Command or items is empty for task id {}", crossDockTaskId);
            return crossDockTaskDtoApplicationMapper.domainToDto(crossDockTaskRepository.findById(crossDockTaskId));
        }

        CrossDockTask crossDockTask = crossDockTaskRepository.findById(crossDockTaskId);
        if (crossDockTask == null) {
            log.error("[bindItems] Cross dock task not found with id {}", crossDockTaskId);
            throw new IllegalArgumentException("Cross dock task not found with id: " + crossDockTaskId);
        }

        // Sort by taskItemId to ensure that the lock order is consistent and avoid deadlocks
        List<BindItemAndTaskItem> sortedItems = command.getItems().stream()
            .sorted((a, b) -> {
                UUID aId = Optional.ofNullable(a.getPickingTaskItemId()).orElse(a.getReceivingTaskItemId());
                UUID bId = Optional.ofNullable(b.getPickingTaskItemId()).orElse(b.getReceivingTaskItemId());
                return aId == null ? (bId == null ? 0 : -1) : (bId == null ? 1 : aId.compareTo(bId));
            })
            .toList();

        for (BindItemAndTaskItem bindItem : sortedItems) {
            bindItem(crossDockTask, bindItem);
        }

        return crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask);
    }

    private void bindItem(CrossDockTask crossDockTask, BindItemAndTaskItem bindItem) {
        UUID taskItemId = Optional.ofNullable(bindItem.getPickingTaskItemId())
            .orElse(bindItem.getReceivingTaskItemId());
        if (taskItemId == null) {
            throw new IllegalArgumentException("Either pickingTaskItemId or receivingTaskItemId must be provided");
        }

        String sequence = bindItem.getSequence();
        if (sequence == null) {
            throw new IllegalArgumentException("Sequence must not be null");
        }

        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock(
            String.format("bindItems_item_%s", taskItemId).hashCode(),
            String.format("BindItems item lock for taskItem %s", taskItemId)
        );

        List<CrossDockTaskItem> taskItems = crossDockTaskItemRepository.findByTaskItemId(taskItemId);

        CrossDockItemSourceEnum source = bindItem.getPickingTaskItemId() != null
            ? CrossDockItemSourceEnum.PICKING_TASK
            : CrossDockItemSourceEnum.RECEIVING_TASK;

        validateQuantityBeforeBinding(crossDockTask, taskItems, taskItemId, source);

        UUID shippingOrderItemId = getShippingOrderItemId(taskItemId, source);
        boolean isHighValue = isHighValueItem(shippingOrderItemId);

        CrossDockTaskItem crossDockTaskItem;
        if (isHighValue && hasSequenceInTaskItems(taskItems, sequence)) {
            crossDockTaskItem = findExistingItemWithSequence(taskItems, sequence);
        } else {
            validateSequenceUniqueness(taskItems, sequence);
            crossDockTaskItem = findOrCreateTaskItem(taskItems, taskItemId, source, sequence);
        }

        crossDockTask.bindItem(crossDockTaskItem, sequence);
        updateShippingOrderItemFulfilledQty(crossDockTaskItem);
        crossDockTaskItemRepository.update(crossDockTaskItem);
    }

    private void validateQuantityBeforeBinding(CrossDockTask crossDockTask,
        List<CrossDockTaskItem> taskItems,
        UUID taskItemId,
        CrossDockItemSourceEnum source) {
        long itemsBoundToOtherTasks = taskItems.stream()
            .filter(item -> item.getCrossDockTaskId() != null && !item.getCrossDockTaskId().equals(crossDockTask.getId()))
            .count();

        long currentTaskBoundItemsCount;
        if (CollectionUtils.isEmpty(crossDockTask.getCrossDockTaskItems())) {
            currentTaskBoundItemsCount = 0;
        } else {
            currentTaskBoundItemsCount = crossDockTask.getCrossDockTaskItems().stream()
                .filter(item -> item.getTaskItemId().equals(taskItemId))
                .count();
        }

        long totalAfterBinding = itemsBoundToOtherTasks + currentTaskBoundItemsCount + 1;

        if (getPickedQty(taskItemId, source) < totalAfterBinding) {
            throw new WmsBusinessException(
                ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_OUTNUMBER.getCode(),
                ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_OUTNUMBER.getMessage()
            );
        }
    }

    private void validateSequenceUniqueness(List<CrossDockTaskItem> allTaskItems, String sequence) {
        boolean hasDuplicateSequence = allTaskItems.stream()
            .anyMatch(item -> !CollectionUtils.isEmpty(item.getSequence()) &&
                item.getSequence().contains(sequence));

        if (hasDuplicateSequence) {
            throw new WmsBusinessException(
                ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_SEQUENCE_DUPLICATE.getCode(),
                ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_SEQUENCE_DUPLICATE.getMessage()
            );
        }
    }

    private CrossDockTaskItem findOrCreateTaskItem(List<CrossDockTaskItem> taskItems, UUID taskItemId,
        CrossDockItemSourceEnum source, String sequence) {

        CrossDockTaskItem existingItem = taskItems.stream()
            .filter(item -> item.getCrossDockTaskId() == null)
            .filter(item -> CollectionUtils.isEmpty(item.getSequence()))
            .findFirst()
            .orElse(null);

        if (existingItem != null) {
            return existingItem;
        }

        return createTaskItem(taskItemId, source, sequence);
    }

    private UUID getShippingOrderItemId(UUID taskItemId, CrossDockItemSourceEnum source) {
        if (source == CrossDockItemSourceEnum.PICKING_TASK) {
            PickingTaskItem pickingItem = pickingTaskItemRepository.findById(taskItemId);
            return pickingItem != null ? pickingItem.getShippingOrderItemId() : null;
        } else {
            ReceivingTaskItem receivingItem = receivingTaskItemRepository.findById(taskItemId);
            return receivingItem != null ? receivingItem.getShippingOrderItemId() : null;
        }
    }

    private boolean isHighValueItem(UUID shippingOrderItemId) {
        if (shippingOrderItemId == null) {
            return false;
        }
        ShippingOrderItem shippingOrderItem = shippingOrderItemRepository.findById(shippingOrderItemId);
        if (shippingOrderItem == null) {
            log.warn("[isHighValueItem] ShippingOrderItem not found with id: {}", shippingOrderItemId);
            return false;
        }
        return shippingOrderItem.isHighValueItem();
    }

    private boolean hasSequenceInTaskItems(List<CrossDockTaskItem> taskItems, String sequence) {
        Optional<CrossDockTaskItem> duplicateItem = taskItems.stream()
            .filter(item -> !CollectionUtils.isEmpty(item.getSequence()) && item.getSequence().contains(sequence))
            .findFirst();
            
        if (duplicateItem.isPresent() && duplicateItem.get().getCrossDockTaskId() != null) {
            throw new WmsBusinessException(
                ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_SEQUENCE_DUPLICATE.getCode(),
                ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_SEQUENCE_DUPLICATE.getMessage()
            );
        }
        
        return duplicateItem.isPresent();
    }

    private CrossDockTaskItem findExistingItemWithSequence(List<CrossDockTaskItem> taskItems, String sequence) {
        return taskItems.stream()
            .filter(item -> !CollectionUtils.isEmpty(item.getSequence()) && item.getSequence().contains(sequence))
            .findFirst()
            .orElse(null);
    }

    private Integer getPickedQty(UUID taskItemId, CrossDockItemSourceEnum source) {
        if (source == CrossDockItemSourceEnum.PICKING_TASK) {
            PickingTaskItem pickingItem = pickingTaskItemRepository.findById(taskItemId);
            if (pickingItem == null) {
                log.error("[getPickedQty] PickingTaskItem not found with id: {}", taskItemId);
                throw new IllegalArgumentException("PickingTaskItem not found with id: " + taskItemId);
            }
            return pickingItem.getPickedQty() != null ? pickingItem.getPickedQty() : 0;
        } else {
            ReceivingTaskItem receivingItem = receivingTaskItemRepository.findById(taskItemId);
            if (receivingItem == null) {
                log.error("[getPickedQty] ReceivingTaskItem not found with id: {}", taskItemId);
                throw new IllegalArgumentException("ReceivingTaskItem not found with id: " + taskItemId);
            }
            return receivingItem.getReceivedQty() != null ? receivingItem.getReceivedQty() : 0;
        }
    }

    private void updateShippingOrderItemFulfilledQty(CrossDockTaskItem crossDockTaskItem) {
        if (crossDockTaskItem.getShippingOrderItemId() == null) {
            log.warn("[updateShippingOrderItemFulfilledQty] ShippingOrderItemId is null, skipping update for taskItemId: {}",
                crossDockTaskItem.getTaskItemId());
            return;
        }

        shippingOrderService.updateFulfilledQty(crossDockTaskItem.getShippingOrderId(),
            crossDockTaskItem.getShippingOrderItemId(),
            1);
        log.info("[updateShippingOrderItemFulfilledQty] Updated fulfilledQty for ShippingOrderItem: {}, " +
            "crossDockedQty: {}", crossDockTaskItem.getShippingOrderItemId(), crossDockTaskItem.getCrossDockedQty());
    }

    private CrossDockTaskItem createTaskItem(UUID taskItemId, CrossDockItemSourceEnum source, String sequence) {
        CrossDockTaskItem taskItem;
        if (source == CrossDockItemSourceEnum.PICKING_TASK) {
            PickingTaskItem pickingItem = pickingTaskItemRepository.findById(taskItemId);
            if (pickingItem == null) {
                log.error("[createTaskItem] PickingTaskItem not found with id: {}", taskItemId);
                throw new IllegalArgumentException("PickingTaskItem not found with id: " + taskItemId);
            }
            PickingTask pickingTask = pickingTaskRepository.findById(pickingItem.getPickingTaskId());
            taskItem = CrossDockTaskItem.builder()
                .build()
                .create(pickingItem, pickingTask, sequence);
        } else {
            ReceivingTaskItem receivingItem = receivingTaskItemRepository.findById(taskItemId);
            if (receivingItem == null) {
                log.error("[createTaskItem] ReceivingTaskItem not found with id: {}", taskItemId);
                throw new IllegalArgumentException("ReceivingTaskItem not found with id: " + taskItemId);
            }
            ReceivingTask receivingTask = receivingTaskRepository.findById(receivingItem.getReceivingTaskId());
            taskItem = CrossDockTaskItem.builder()
                .build()
                .create(receivingItem, receivingTask, sequence);
        }
        return crossDockTaskItemRepository.save(taskItem);
    }
}