package com.mercaso.wms.infrastructure.repository.pickingtask;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.PickingTaskJdbcTemplate;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.PickingTaskJpaDao;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.criteria.PickingTaskSearchCriteria;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.dataobject.PickingTaskDo;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.mapper.PickingTaskDoMapper;
import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.PickingTaskItemJpaDao;
import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.dataobject.PickingTaskItemDo;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class PickingTaskRepositoryImpl implements PickingTaskRepository {

    public final PickingTaskDoMapper mapper;
    private final PickingTaskJpaDao pickingTaskJpaDao;
    private final PickingTaskItemJpaDao pickingTaskItemJpaDao;
    private final PickingTaskJdbcTemplate pickingTaskJdbcTemplate;

    @Override
    public PickingTask save(PickingTask domain) {
        PickingTaskDo pickingTaskDo = mapper.domainToDo(domain);
        pickingTaskDo.getPickingTaskItems().forEach(pickingTaskItemDo -> pickingTaskItemDo.setPickingTask(pickingTaskDo));
        return mapper.doToDomain(pickingTaskJpaDao.saveAndFlush(pickingTaskDo));
    }

    @Override
    public PickingTask findById(UUID id) {
        Optional<PickingTaskDo> byId = pickingTaskJpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public PickingTask update(PickingTask domain) {
        PickingTaskDo pickingTaskDo = pickingTaskJpaDao.findById(domain.getId())
            .orElseThrow(() -> new WmsBusinessException("pickingTask not found."));
        PickingTaskDo target = mapper.domainToDo(domain);

        List<PickingTaskItemDo> needRemoveItems = new LinkedList<>();
        pickingTaskDo.getPickingTaskItems().forEach(pickingTaskItemDo -> {
            if (target.getPickingTaskItems().stream().noneMatch(item -> item.getId().equals(pickingTaskItemDo.getId()))) {
                needRemoveItems.add(pickingTaskItemDo);
            }
        });

        target.getPickingTaskItems().forEach(pickingTaskItemDo -> pickingTaskItemDo.setPickingTask(target));
        List<String> ignoreProperties = List.of("createdBy", "createdAt", "createdUserName");
        BeanUtils.copyProperties(target, pickingTaskDo, ignoreProperties.toArray(new String[0]));

        pickingTaskItemJpaDao.deleteAll(needRemoveItems);
        return mapper.doToDomain(pickingTaskJpaDao.save(pickingTaskDo));
    }

    @Override
    public List<PickingTask> saveAll(List<PickingTask> pickingTasks) {
        List<PickingTaskDo> pickingTaskDos = mapper.domainToDos(pickingTasks);
        pickingTaskDos.forEach(pickingTaskDo ->
            pickingTaskDo.getPickingTaskItems()
                .forEach(pickingTaskItemDo -> pickingTaskItemDo.setPickingTask(pickingTaskDo)));
        return mapper.doToDomains(pickingTaskJpaDao.saveAll(pickingTaskDos));
    }

    @Override
    public List<PickingTask> findByBatchId(UUID batchId) {
        return mapper.doToDomains(pickingTaskJpaDao.findByBatchId(batchId));
    }

    @Override
    public List<PickingTask> findByBatchIdAndTypeAndSource(UUID batchId, PickingTaskType type, SourceEnum source) {
        return mapper.doToDomains(pickingTaskJpaDao.findByBatchIdAndTypeAndSource(batchId, type, source));
    }

    @Override
    public List<PickingTask> findByIds(List<UUID> ids) {
        List<PickingTaskDo> pickingTaskDos = pickingTaskJpaDao.findAllById(ids);
        List<PickingTask> pickingTasks = mapper.doToDomains(pickingTaskDos);

        Map<UUID, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < ids.size(); i++) {
            orderMap.put(ids.get(i), i);
        }
        // keep the order of picking tasks based on the order of ids,
        // because the findAllById method does not guarantee the order
        pickingTasks.sort(Comparator.comparing(task -> orderMap.getOrDefault(task.getId(), Integer.MAX_VALUE)));
        return pickingTasks;
    }

    @Override
    public Page<PickingTask> findPickingTaskList(PickingTaskSearchCriteria criteria, Pageable pageable) {
        Page<PickingTaskDo> pickingTaskDoPage = pickingTaskJpaDao.findPickingTaskList(criteria, pageable);
        return pickingTaskDoPage.map(mapper::doToDomain);
    }

    @Override
    public Page<UUID> findPickingTaskIds(PickingTaskSearchCriteria criteria, Pageable pageable) {
        return pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);
    }

    @Override
    public List<PickingTask> findByOrderNumbers(List<String> orderNumbers) {
        return mapper.doToDomains(pickingTaskJpaDao.findByOrderNumbers(orderNumbers));
    }

    @Override
    public void deleteAll() {
        pickingTaskJpaDao.deleteAll();
    }

    @Override
    public void deleteByIds(List<UUID> ids) {
        pickingTaskJpaDao.deleteAllById(ids);
    }

    @Override
    public List<PickingTask> findByBatchIdAndStatusNotIn(UUID batchId, List<PickingTaskStatus> statuses) {
        return mapper.doToDomains(pickingTaskJpaDao.findByBatchIdAndStatusNotIn(batchId, statuses));
    }
}
