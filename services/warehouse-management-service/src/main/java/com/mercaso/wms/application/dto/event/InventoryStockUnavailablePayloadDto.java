package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InventoryStockUnavailablePayloadDto extends BusinessEventPayloadDto<InventoryStockDto> {

    private UUID inventoryStockId;

    @Builder
    public InventoryStockUnavailablePayloadDto(InventoryStockDto data, UUID inventoryStockId) {
        super(data);
        this.inventoryStockId = inventoryStockId;
    }

}
