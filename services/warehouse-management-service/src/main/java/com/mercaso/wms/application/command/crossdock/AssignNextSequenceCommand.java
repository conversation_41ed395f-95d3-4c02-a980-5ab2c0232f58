package com.mercaso.wms.application.command.crossdock;

import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignNextSequenceCommand {

    @NotNull(message = "ShippingOrderItemId cannot be null")
    private UUID shippingOrderItemId;
}