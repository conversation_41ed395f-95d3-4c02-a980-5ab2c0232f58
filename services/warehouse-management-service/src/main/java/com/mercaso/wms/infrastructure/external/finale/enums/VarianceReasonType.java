package com.mercaso.wms.infrastructure.external.finale.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum VarianceReasonType {
    VAR_DAMAGED("VAR_DAMAGED"),
    VAR_EXPIRED("VAR_EXPIRED"),
    VAR_FOUND("VAR_FOUND"),
    VAR_LOST("VAR_LOST"),
    VAR_RTN_CUSTOMER("VAR_RTN_CUSTOMER"),
    VAR_RTN_SUPPLIER("VAR_RTN_SUPPLIER"),
    VAR_SAMPLE("VAR_SAMPLE"),
    VAR_STOLEN("VAR_STOLEN");

    private final String code;
}