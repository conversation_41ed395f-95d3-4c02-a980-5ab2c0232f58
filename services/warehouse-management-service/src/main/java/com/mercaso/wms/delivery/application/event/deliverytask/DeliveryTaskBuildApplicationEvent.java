package com.mercaso.wms.delivery.application.event.deliverytask;

import com.mercaso.wms.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class DeliveryTaskBuildApplicationEvent extends BaseApplicationEvent<DeliveryTaskBuildPayloadDto> {

    public DeliveryTaskBuildApplicationEvent(Object source, DeliveryTaskBuildPayloadDto payload) {
        super(source, payload);
    }
}
