spring:
  config:
    import: vault://
    activate:
      on-profile: sat

finale:
  mfc-facility-url: /${finale.domain}/api/facility/100002
  domain: mercasosandbox
shopify:
  webhook:
    domain: sat-mercaso.myshopify.com
  shop-name: sat-mercaso
slack:
  fraud-order-alter:
    webhook: *********************************************************************************
    enable: false
  data-consistency-alert:
    webhook: *********************************************************************************
    enable: true
  wms-exception-alert:
    webhook: *********************************************************************************
  delivery:
    webhook-url: https://hooks.slack.com/triggers/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
    task-build-exception: https://hooks.slack.com/triggers/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
    order-issues: https://hooks.slack.com/triggers/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
    order-rescheduled: https://hooks.slack.com/triggers/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
delivery-demo:
  enabled: true
  task-numbers: D-DEMO-01,D-DEMO-02,D-DEMO-03,D-DEMO-04,D-DEMO-05
  order-numbers: M-DEMO-01,M-DEMO-02,M-DEMO-03,M-DEMO-04,M-DEMO-05,M-DEMO-06,M-DEMO-07,M-DEMO-08,M-DEMO-09,M-DEMO-10