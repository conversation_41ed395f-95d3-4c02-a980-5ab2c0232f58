ALTER table batch_items
    add COLUMN IF NOT EXISTS shipping_order_id UUID;

ALTER table batch_items
    add COLUMN IF NOT EXISTS shipping_order_item_id UUID;

ALTER table shipping_order_items
    add column IF NOT EXISTS picked_qty INTEGER;

create index IF NOT EXISTS batch_items_shipping_order_id_idx on batch_items (shipping_order_id);
create index IF NOT EXISTS batch_items_shipping_order_item_id_idx on batch_items (shipping_order_item_id);

COMMENT
ON COLUMN batch_items.shipping_order_id IS 'Shopify order id';
COMMENT
ON COLUMN batch_items.shipping_order_item_id IS 'Shopify order item id';
COMMENT
ON COLUMN shipping_order_items.picked_qty IS 'Shopify order item picked qty';

