INSERT INTO location (id, warehouse_id, name, type, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by,
                      aisle_number, bay_number, finale_id)
VALUES (uuid_generate_v4(),
        (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-67', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-68', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-69', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-70', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-71', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-72', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-73', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-74', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-75', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-76', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-77', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-78', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-79', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null),
    (uuid_generate_v4(), (SELECT id FROM warehouse WHERE name = 'MDC' LIMIT 1), 'D-80', 'SHIPPING_BIG', now(), '1', now(), '1', null, null, 'D', null, null)
ON CONFLICT (warehouse_id, name) DO NOTHING;