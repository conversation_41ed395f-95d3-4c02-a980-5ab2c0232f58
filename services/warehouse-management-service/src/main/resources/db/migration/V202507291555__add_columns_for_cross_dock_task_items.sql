ALTER TABLE cross_dock_task_items
    ADD COLUMN IF NOT EXISTS task_number VARCHAR(20);

ALTER TABLE cross_dock_task_items
    ADD COLUMN IF NOT EXISTS department VARCHAR(50);

ALTER TABLE cross_dock_task_items
    ADD COLUMN IF NOT EXISTS category VARCHAR(100);


CREATE INDEX IF NOT EXISTS idx_cross_dock_task_items_task_number ON cross_dock_task_items (task_number);
CREATE INDEX IF NOT EXISTS idx_cross_dock_task_items_department ON cross_dock_task_items (department);
CREATE INDEX IF NOT EXISTS idx_cross_dock_task_items_category ON cross_dock_task_items (category);


COMMENT ON COLUMN cross_dock_task_items.task_number IS 'The number field from picking_task or receiving_task, indicating the source task identifier';
COMMENT ON COLUMN cross_dock_task_items.department IS 'Item department information';
COMMENT ON COLUMN cross_dock_task_items.category IS 'Item category information';