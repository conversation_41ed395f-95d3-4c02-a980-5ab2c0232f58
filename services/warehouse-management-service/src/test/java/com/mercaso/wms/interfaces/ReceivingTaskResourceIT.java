package com.mercaso.wms.interfaces;

import static com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus.CREATED;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.receivingtask.BatchUpdateReceivedQtyCommand;
import com.mercaso.wms.application.command.receivingtask.BatchUpdateReceivedQtyCommand.UpdateReceivedQtyItem;
import com.mercaso.wms.application.command.receivingtask.BatchUpdateVendorCommand;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand.UpdateReceivingTaskItem;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.utils.ReceivingTaskResourceApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ReceivingTaskResourceIT extends AbstractIT {

    @Autowired
    private ReceivingTaskResourceApi receivingTaskResourceApi;

    @Autowired
    private CrossDockTaskItemRepository crossDockTaskItemRepository;

    @Test
    void when_scan_receive_item_id_then_return_receiving_task_item_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        ReceivingTaskItemDto receivingTaskItemDto = receivingTaskResourceApi.scanReceiveItem(result.getReceivingTaskItems()
            .getFirst()
            .getId());

        assertNotNull(receivingTaskItemDto);
        assertEquals(1, receivingTaskItemDto.getReceivedQty());

        ReceivingTask receivingTask = receivingTaskRepository.findById(result.getId());

        assertNotNull(receivingTask);
        assertEquals(ReceivingTaskStatus.RECEIVING, receivingTask.getStatus());

        receivingTask.getReceivingTaskItems().getFirst().setReceivedQty(9);
        receivingTask.getReceivingTaskItems().getLast().setReceivedQty(10);
        receivingTaskRepository.save(receivingTask);

        ReceivingTaskItemDto receivingTaskItemDto1 = receivingTaskResourceApi.scanReceiveItem(receivingTask.getReceivingTaskItems()
            .getFirst()
            .getId());

        assertNotNull(receivingTaskItemDto1);
        assertEquals(10, receivingTaskItemDto1.getReceivedQty());

        ReceivingTask receivingTask1 = receivingTaskRepository.findById(result.getId());

        assertNotNull(receivingTask1);
        assertEquals(ReceivingTaskStatus.RECEIVED, receivingTask1.getStatus());
    }

    @Test
    void when_receive_task_id_then_return_receiving_task_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        ReceivingTaskItemDto receivingTaskItemDto = receivingTaskResourceApi.scanReceiveItem(result.getReceivingTaskItems()
            .getFirst()
            .getId());

        assertNotNull(receivingTaskItemDto);
        assertEquals(1, receivingTaskItemDto.getReceivedQty());

        ReceivingTask receivingTask = receivingTaskRepository.findById(result.getId());

        assertNotNull(receivingTask);
        assertEquals(ReceivingTaskStatus.RECEIVING, receivingTask.getStatus());

        ReceivingTaskDto receivingTaskDto = receivingTaskResourceApi.receiveTask(result.getId());

        assertNotNull(receivingTaskDto);
        assertEquals(ReceivingTaskStatus.RECEIVED, receivingTaskDto.getStatus());
    }

    @Test
    void when_update_receiving_task_then_return_receiving_task_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        UpdateReceivingTaskItem updateReceivingTaskItem = UpdateReceivingTaskItem.builder()
            .receivingTaskItemId(result.getReceivingTaskItems().getFirst().getId())
            .expectQty(0)
            .build();
        UpdateReceivingTaskCommand command = UpdateReceivingTaskCommand.builder().receivingTaskId(result.getId())
            .receivingTaskItems(List.of(updateReceivingTaskItem))
            .build();
        ReceivingTaskDto receivingTaskDto = receivingTaskResourceApi.updateTask(command);

        assertNotNull(receivingTaskDto);
        assertEquals(0, receivingTaskDto.getReceivingTaskItems().getFirst().getExpectQty());
    }

    @Test
    void when_receive_qty_increased_then_create_cross_dock_items() throws Exception {
        receivingTaskRepository.deleteAll();
        List<CrossDockTaskItem> allItems = crossDockTaskItemRepository.findByTaskItemId(null);
        if (allItems != null && !allItems.isEmpty()) {
            crossDockTaskItemRepository.deleteByIds(allItems.stream().map(CrossDockTaskItem::getId).toList());
        }
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);
        UUID batchId = receivingTasks.getFirst().getBatchId();
        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());
        UUID receivingTaskItemId = result.getReceivingTaskItems().getFirst().getId();

        assertEquals(0, crossDockTaskItemRepository.findByTaskItemId(receivingTaskItemId).size());

        for (int i = 0; i < 3; i++) {
            receivingTaskResourceApi.scanReceiveItem(receivingTaskItemId);
        }

        org.awaitility.Awaitility.await().atMost(java.time.Duration.ofSeconds(3)).untilAsserted(() -> {
            List<CrossDockTaskItem> after = crossDockTaskItemRepository.findByTaskItemId(receivingTaskItemId);
            assertEquals(3, after.size());
            assertTrue(after.stream().allMatch(i -> i.getPickedQty() == 1));
            assertTrue(after.stream().allMatch(i -> batchId.equals(i.getBatchId())));
        });
    }

    @Test
    void when_batch_update_received_qty_then_return_receiving_task_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            2,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        UpdateReceivedQtyItem updateItem1 = new UpdateReceivedQtyItem();
        updateItem1.setReceivingTaskItemId(result.getReceivingTaskItems().getFirst().getId());
        updateItem1.setReceivedQty(10);
        
        UpdateReceivedQtyItem updateItem2 = new UpdateReceivedQtyItem();
        updateItem2.setReceivingTaskItemId(result.getReceivingTaskItems().getLast().getId());
        updateItem2.setReceivedQty(10);
        
        BatchUpdateReceivedQtyCommand command = new BatchUpdateReceivedQtyCommand();
        command.setReceivingTaskItems(List.of(updateItem1, updateItem2));

        ReceivingTaskDto receivingTaskDto = receivingTaskResourceApi.batchUpdateReceivedQty(command);

        assertNotNull(receivingTaskDto);
        assertEquals(ReceivingTaskStatus.RECEIVED, receivingTaskDto.getStatus());
        assertEquals(10, receivingTaskDto.getReceivingTaskItems().getFirst().getReceivedQty());
        assertEquals(10, receivingTaskDto.getReceivingTaskItems().getLast().getReceivedQty());
    }

    @Test
    void when_batch_update_vendor_then_return_updated_receiving_task_dtos() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            3,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask task1 = receivingTaskRepository.save(receivingTasks.get(0));
        ReceivingTask task2 = receivingTaskRepository.save(receivingTasks.get(1));
        ReceivingTask task3 = receivingTaskRepository.save(receivingTasks.get(2));

        // Verify initial vendor
        assertEquals(SourceEnum.VERNON.name(), task1.getVendor());
        assertEquals(SourceEnum.VERNON.name(), task2.getVendor());
        assertEquals(SourceEnum.VERNON.name(), task3.getVendor());

        BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
            .receivingTaskIds(List.of(task1.getId(), task2.getId(), task3.getId()))
            .vendor(SourceEnum.EXOTIC)
            .build();

        List<ReceivingTaskDto> result = receivingTaskResourceApi.batchUpdateVendor(command);

        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify vendor was updated to EXOTIC
        assertEquals(SourceEnum.EXOTIC.name(), result.get(0).getVendor());
        assertEquals(SourceEnum.EXOTIC.name(), result.get(1).getVendor());
        assertEquals(SourceEnum.EXOTIC.name(), result.get(2).getVendor());

        // Verify status remains CREATED
        assertEquals(CREATED, result.get(0).getStatus());
        assertEquals(CREATED, result.get(1).getStatus());
        assertEquals(CREATED, result.get(2).getStatus());

        // Verify in database
        ReceivingTask updatedTask1 = receivingTaskRepository.findById(task1.getId());
        ReceivingTask updatedTask2 = receivingTaskRepository.findById(task2.getId());
        ReceivingTask updatedTask3 = receivingTaskRepository.findById(task3.getId());

        assertEquals(SourceEnum.EXOTIC.name(), updatedTask1.getVendor());
        assertEquals(SourceEnum.EXOTIC.name(), updatedTask2.getVendor());
        assertEquals(SourceEnum.EXOTIC.name(), updatedTask3.getVendor());
        assertEquals(CREATED, updatedTask1.getStatus());
        assertEquals(CREATED, updatedTask2.getStatus());
        assertEquals(CREATED, updatedTask3.getStatus());
    }

    @Test
    void when_batch_update_vendor_with_single_task_then_return_updated_receiving_task_dto() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.MISSION,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask task = receivingTaskRepository.save(receivingTasks.getFirst());

        // Verify initial vendor
        assertEquals(SourceEnum.MISSION.name(), task.getVendor());

        BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
            .receivingTaskIds(List.of(task.getId()))
            .vendor(SourceEnum.SEVEN_STARS)
            .build();

        List<ReceivingTaskDto> result = receivingTaskResourceApi.batchUpdateVendor(command);

        assertNotNull(result);
        assertEquals(1, result.size());

        // Verify vendor was updated to SEVEN_STARS
        assertEquals(SourceEnum.SEVEN_STARS.name(), result.getFirst().getVendor());
        assertEquals(CREATED, result.getFirst().getStatus());

        // Verify in database
        ReceivingTask updatedTask = receivingTaskRepository.findById(task.getId());
        assertEquals(SourceEnum.SEVEN_STARS.name(), updatedTask.getVendor());
        assertEquals(CREATED, updatedTask.getStatus());
    }

    @Test
    void when_batch_update_vendor_with_different_online_vendors_then_success() throws JsonProcessingException {
        receivingTaskRepository.deleteAll();

        // Test updating to different online vendors
        SourceEnum[] vendors = {
            SourceEnum.MISSION,
            SourceEnum.VERNON,
            SourceEnum.EXOTIC,
            SourceEnum.SEVEN_STARS,
            SourceEnum.SMART_AND_FINAL,
            SourceEnum.CORE_MARK,
            SourceEnum.DOLLAR_EMPIRE
        };

        for (SourceEnum targetVendor : vendors) {
            List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
                1,
                SourceEnum.VERNON,
                CREATED,
                ReceivingTaskType.ONLINE_RECEIVING);

            ReceivingTask task = receivingTaskRepository.save(receivingTasks.getFirst());

            BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
                .receivingTaskIds(List.of(task.getId()))
                .vendor(targetVendor)
                .build();

            List<ReceivingTaskDto> result = receivingTaskResourceApi.batchUpdateVendor(command);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(targetVendor.name(), result.getFirst().getVendor());
            assertEquals(CREATED, result.getFirst().getStatus());

            // Verify in database
            ReceivingTask updatedTask = receivingTaskRepository.findById(task.getId());
            assertEquals(targetVendor.name(), updatedTask.getVendor());
            assertEquals(CREATED, updatedTask.getStatus());
        }
    }

}