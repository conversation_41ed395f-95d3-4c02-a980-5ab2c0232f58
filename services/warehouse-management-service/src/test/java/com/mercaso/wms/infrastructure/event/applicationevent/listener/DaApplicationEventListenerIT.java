package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskBuildApplicationEvent;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskBuildPayloadDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskBuildEventDto;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DaApplicationEventListenerIT extends AbstractIT {

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;
    @Autowired
    DaApplicationEventListener daApplicationEventListener;

    @Test
    void when_delivery_task_build_then_update_shipping_orders() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());
        shippingOrder.setDriverUserName(RandomStringUtils.randomAlphabetic(10));
        shippingOrder.setDriverUserId(UUID.randomUUID());
        shippingOrder.setTruckNumber(RandomStringUtils.randomAlphanumeric(10));
        shippingOrderRepository.update(shippingOrder);

        DeliveryTaskBuildEventDto data = new DeliveryTaskBuildEventDto();
        data.setTruckNumber(RandomStringUtils.randomAlphabetic(10));
        data.setDriverUserId(UUID.randomUUID());
        data.setDriverUserName(RandomStringUtils.randomAlphabetic(10));
        data.setOrders(List.of(new DeliveryTaskBuildEventDto.OrderInfo(null, "M-" + shippingOrder.getOrderNumber(), null, null)));

        DeliveryTaskBuildPayloadDto payload = new DeliveryTaskBuildPayloadDto();
        payload.setData(data);

        DeliveryTaskBuildApplicationEvent applicationEvent = new DeliveryTaskBuildApplicationEvent(new Object(), payload);

        daApplicationEventListener.handleDeliveryTaskBuildAppEvent(applicationEvent);

        ShippingOrder updated = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());
        assertEquals(data.getDriverUserId(), updated.getDriverUserId());
        assertEquals(data.getDriverUserName(), updated.getDriverUserName());
        assertEquals(data.getTruckNumber(), updated.getTruckNumber());
    }

}