package com.mercaso.wms.application.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.wms.application.command.receivingtask.BatchUpdateVendorCommand;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.application.mapper.receivingtask.ReceivingTaskDtoApplicationMapper;
import com.mercaso.wms.application.mapper.receivingtask.ReceivingTaskItemDtoApplicationMapper;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class ReceivingTaskApplicationServiceTest {

    private final ReceivingTaskRepository receivingTaskRepository = mock(ReceivingTaskRepository.class);
    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);
    private final ReceivingTaskDtoApplicationMapper receivingTaskDtoApplicationMapper = mock(ReceivingTaskDtoApplicationMapper.class);
    private final ReceivingTaskItemRepository receivingTaskItemRepository = mock(ReceivingTaskItemRepository.class);
    private final ReceivingTaskItemDtoApplicationMapper receivingTaskItemDtoApplicationMapper = mock(
        ReceivingTaskItemDtoApplicationMapper.class);
    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);
    private final ApplicationEventDispatcher applicationEventDispatcher = mock(ApplicationEventDispatcher.class);
    private final CrossDockTaskItemService crossDockTaskItemService = mock(CrossDockTaskItemService.class);

    private final ReceivingTaskApplicationService receivingTaskApplicationService = new ReceivingTaskApplicationService(
        receivingTaskRepository,
        batchItemQueryService,
        businessEventDispatcher,
        receivingTaskDtoApplicationMapper,
        receivingTaskItemRepository,
        receivingTaskItemDtoApplicationMapper,
        pgAdvisoryLock,
        applicationEventDispatcher,
        crossDockTaskItemService);

    @Test
    void createTask_ShouldCreateReceivingTaskAndDispatchEvent_WhenBatchItemsExist() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = List.of(BatchItem.builder().build());
        ReceivingTask receivingTask = ReceivingTask.builder().build();
        ReceivingTaskDto receivingTaskDto = new ReceivingTaskDto();
        receivingTaskDto.setId(UUID.randomUUID());
        receivingTaskDto.setNumber("RT-001");

        when(batchItemQueryService.findBy(batchId, SourceEnum.VERNON.name())).thenReturn(batchItems);
        when(receivingTaskRepository.save(any(ReceivingTask.class))).thenReturn(receivingTask);
        when(receivingTaskDtoApplicationMapper.domainToDto(receivingTask)).thenReturn(receivingTaskDto);

        // Act
        receivingTaskApplicationService.createReceivingTask(batchId);

        // Assert
        verify(batchItemQueryService).findBy(batchId, SourceEnum.VERNON.name());
        verify(receivingTaskRepository).save(any(ReceivingTask.class));
        verify(receivingTaskDtoApplicationMapper).domainToDto(receivingTask);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void createTask_ShouldNotCreateReceivingTask_WhenNoBatchItemsFound() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        when(batchItemQueryService.findBy(batchId, SourceEnum.VERNON.name())).thenReturn(List.of());

        // Act
        receivingTaskApplicationService.createReceivingTask(batchId);

        // Assert
        verify(batchItemQueryService).findBy(batchId, SourceEnum.VERNON.name());
        verifyNoInteractions(receivingTaskRepository);
        verifyNoInteractions(receivingTaskDtoApplicationMapper);
        verifyNoInteractions(businessEventDispatcher);
    }

    @Test
    void scanReceiveItem_shouldSucceed() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();

        ReceivingTaskItem mockItem = mock(ReceivingTaskItem.class);
        when(mockItem.getReceivingTaskId()).thenReturn(taskId);
        when(mockItem.getReceivedQty()).thenReturn(0);
        when(mockItem.getExpectQty()).thenReturn(10);

        ReceivingTask mockTask = mock(ReceivingTask.class);
        when(mockTask.getStatus()).thenReturn(ReceivingTaskStatus.RECEIVING);
        when(mockTask.scanReceive(itemId)).thenReturn(mockItem);

        ReceivingTaskItemDto expectedDto = new ReceivingTaskItemDto();

        when(receivingTaskItemRepository.findById(itemId)).thenReturn(mockItem);
        when(receivingTaskRepository.findById(taskId)).thenReturn(mockTask);
        when(receivingTaskRepository.save(mockTask)).thenReturn(mockTask);
        when(receivingTaskItemDtoApplicationMapper.domainToDto(mockItem)).thenReturn(expectedDto);

        // Act
        ReceivingTaskItemDto result = receivingTaskApplicationService.scanReceiveItem(itemId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedDto, result);
        verify(receivingTaskItemRepository).findById(itemId);
        verify(receivingTaskRepository).findById(taskId);
        verify(receivingTaskRepository).save(mockTask);
    }

    @Test
    void scanReceiveItem_whenItemNotFound_shouldThrowException() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        when(receivingTaskItemRepository.findById(itemId)).thenReturn(null);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> receivingTaskApplicationService.scanReceiveItem(itemId));
        assertEquals(ErrorCodeEnums.RECEIVING_TASK_ITEM_NOT_FOUND.getCode(), exception.getCode());
    }

    @Test
    void scanReceiveItem_whenItemFullyReceived_shouldThrowException() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ReceivingTaskItem mockItem = mock(ReceivingTaskItem.class);
        when(mockItem.getReceivedQty()).thenReturn(10);
        when(mockItem.getExpectQty()).thenReturn(10);
        when(receivingTaskItemRepository.findById(itemId)).thenReturn(mockItem);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> receivingTaskApplicationService.scanReceiveItem(itemId));
        assertEquals(ErrorCodeEnums.RECEIVING_TASK_ITEM_HAS_RECEIVED.getCode(), exception.getCode());
    }

    @Test
    void startReceiving_shouldSucceed() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        ReceivingTask mockTask = mock(ReceivingTask.class);
        when(mockTask.getStatus()).thenReturn(ReceivingTaskStatus.CREATED);
        when(receivingTaskRepository.findById(taskId)).thenReturn(mockTask);
        when(receivingTaskRepository.save(mockTask)).thenReturn(mockTask);

        ReceivingTaskDto mockDto = new ReceivingTaskDto();
        when(receivingTaskDtoApplicationMapper.domainToDto(mockTask)).thenReturn(mockDto);

        // Act
        ReceivingTask result = receivingTaskApplicationService.startReceiving(taskId);

        // Assert
        assertNotNull(result);
        verify(mockTask).start();
        verify(receivingTaskRepository).save(mockTask);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void startReceiving_whenTaskNotFound_shouldThrowException() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        when(receivingTaskRepository.findById(taskId)).thenReturn(null);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> receivingTaskApplicationService.startReceiving(taskId));
        assertEquals(ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getCode(), exception.getCode());
    }

    @Test
    void receive_ShouldSucceed_WhenTaskExists() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        ReceivingTask task = createReceivingTask(taskId, ReceivingTaskStatus.RECEIVING);
        ReceivingTaskDto expectedDto = createReceivingTaskDto(taskId);

        when(receivingTaskRepository.findById(taskId)).thenReturn(task);
        when(receivingTaskRepository.save(any())).thenReturn(task);
        when(receivingTaskDtoApplicationMapper.domainToDto(any())).thenReturn(expectedDto);
        when(businessEventDispatcher.dispatch(any())).thenReturn(DispatchResponseDto.builder()
            .event(new BusinessEventDto())
            .build());

        // Act
        ReceivingTaskDto result = receivingTaskApplicationService.receive(taskId);

        // Assert
        assertThat(result).isEqualTo(expectedDto);

        verify(receivingTaskRepository, times(1)).save(any());
        verify(businessEventDispatcher, times(1)).dispatch(any());
        verify(applicationEventDispatcher, times(1)).publishEvent(any());
    }

    @Test
    void receive_ShouldThrowException_WhenTaskNotFound() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        when(receivingTaskRepository.findById(taskId)).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.receive(taskId))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessageContaining("Receiving task not found");

        verify(receivingTaskRepository, never()).save(any());
        verify(businessEventDispatcher, never()).dispatch(any());
        verify(applicationEventDispatcher, never()).publishEvent(any());
    }

    @Test
    void receive_ShouldThrowException_WhenTaskAlreadyReceived() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        ReceivingTask task = createReceivingTask(taskId, ReceivingTaskStatus.RECEIVED);
        when(receivingTaskRepository.findById(taskId)).thenReturn(task);

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.receive(taskId))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessageContaining("Receiving task has received");

        verify(receivingTaskRepository, never()).save(any());
        verify(businessEventDispatcher, never()).dispatch(any());
        verify(applicationEventDispatcher, never()).publishEvent(any());
    }

    @Test
    void createTaskForCoreMark_ShouldCreateReceivingTask_WhenBatchItemsExist() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = List.of(BatchItem.builder().build());
        ReceivingTask receivingTask = ReceivingTask.builder().build();
        ReceivingTaskDto receivingTaskDto = new ReceivingTaskDto();
        receivingTaskDto.setId(UUID.randomUUID());
        receivingTaskDto.setNumber("RT-001");

        when(batchItemQueryService.findBy(batchId, SourceEnum.CORE_MARK.name())).thenReturn(batchItems);
        when(receivingTaskRepository.save(any(ReceivingTask.class))).thenReturn(receivingTask);
        when(receivingTaskDtoApplicationMapper.domainToDto(receivingTask)).thenReturn(receivingTaskDto);

        // Act
        receivingTaskApplicationService.createReceivingTask(batchId);

        // Assert
        verify(batchItemQueryService).findBy(batchId, SourceEnum.CORE_MARK.name());
        verify(receivingTaskRepository).save(any(ReceivingTask.class));
        verify(receivingTaskDtoApplicationMapper).domainToDto(receivingTask);
        verify(businessEventDispatcher).dispatch(any());
    }


    private ReceivingTask createReceivingTask(UUID id, ReceivingTaskStatus status) {
        return ReceivingTask.builder()
            .id(id)
            .status(status)
            .receivingTaskItems(List.of())
            .build();
    }

    private ReceivingTaskDto createReceivingTaskDto(UUID id) {
        return ReceivingTaskDto.builder()
            .id(id)
            .status(ReceivingTaskStatus.RECEIVED)
            .build();
    }

    @Test
    void batchUpdateVendor_ShouldUpdateVendorSuccessfully_WhenValidParameters() {
        // Arrange
        UUID taskId1 = UUID.randomUUID();
        UUID taskId2 = UUID.randomUUID();

        ReceivingTask task1 = createReceivingTask(taskId1, ReceivingTaskStatus.CREATED);
        task1.setVendor(SourceEnum.VERNON.name());

        ReceivingTask task2 = createReceivingTask(taskId2, ReceivingTaskStatus.CREATED);
        task2.setVendor(SourceEnum.VERNON.name());

        ReceivingTaskDto dto1 = createReceivingTaskDto(taskId1);
        dto1.setVendor(SourceEnum.EXOTIC.name());
        dto1.setStatus(ReceivingTaskStatus.CREATED);

        ReceivingTaskDto dto2 = createReceivingTaskDto(taskId2);
        dto2.setVendor(SourceEnum.EXOTIC.name());
        dto2.setStatus(ReceivingTaskStatus.CREATED);

        BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
            .receivingTaskIds(List.of(taskId1, taskId2))
            .vendor(SourceEnum.EXOTIC)
            .build();

        when(receivingTaskRepository.findByIds(List.of(taskId1, taskId2)))
            .thenReturn(List.of(task1, task2));
        when(receivingTaskRepository.saveAll(List.of(task1, task2)))
            .thenReturn(List.of(task1, task2));
        when(receivingTaskDtoApplicationMapper.domainToDto(task1)).thenReturn(dto1);
        when(receivingTaskDtoApplicationMapper.domainToDto(task2)).thenReturn(dto2);

        // Act
        List<ReceivingTaskDto> result = receivingTaskApplicationService.batchUpdateVendor(command);

        // Assert
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getVendor()).isEqualTo(SourceEnum.EXOTIC.name());
        assertThat(result.get(1).getVendor()).isEqualTo(SourceEnum.EXOTIC.name());
        assertThat(result.get(0).getStatus()).isEqualTo(ReceivingTaskStatus.CREATED);
        assertThat(result.get(1).getStatus()).isEqualTo(ReceivingTaskStatus.CREATED);

        verify(receivingTaskRepository).findByIds(List.of(taskId1, taskId2));
        verify(receivingTaskRepository).saveAll(List.of(task1, task2));
        verify(receivingTaskDtoApplicationMapper, times(2)).domainToDto(any(ReceivingTask.class));
    }

    @Test
    void batchUpdateVendor_ShouldThrowException_WhenInvalidVendor() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
            .receivingTaskIds(List.of(taskId))
            .vendor(SourceEnum.COSTCO) // Not in ONLINE_VENDOR_SET
            .build();

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.batchUpdateVendor(command))
            .isInstanceOf(WmsBusinessException.class)
            .hasFieldOrPropertyWithValue("code", ErrorCodeEnums.INVALID_VENDOR.getCode());

        verifyNoInteractions(receivingTaskRepository);
        verifyNoInteractions(receivingTaskDtoApplicationMapper);
    }

    @Test
    void batchUpdateVendor_ShouldThrowException_WhenNoTasksFound() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
            .receivingTaskIds(List.of(taskId))
            .vendor(SourceEnum.EXOTIC)
            .build();

        when(receivingTaskRepository.findByIds(List.of(taskId))).thenReturn(List.of());

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.batchUpdateVendor(command))
            .isInstanceOf(WmsBusinessException.class)
            .hasFieldOrPropertyWithValue("code", ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getCode());

        verify(receivingTaskRepository).findByIds(List.of(taskId));
        verifyNoInteractions(receivingTaskDtoApplicationMapper);
    }

    @Test
    void batchUpdateVendor_ShouldThrowException_WhenInvalidStatus() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        ReceivingTask task = createReceivingTask(taskId, ReceivingTaskStatus.RECEIVING); // Not CREATED
        task.setVendor(SourceEnum.VERNON.name());

        BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
            .receivingTaskIds(List.of(taskId))
            .vendor(SourceEnum.EXOTIC)
            .build();

        when(receivingTaskRepository.findByIds(List.of(taskId))).thenReturn(List.of(task));

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.batchUpdateVendor(command))
            .isInstanceOf(WmsBusinessException.class)
            .hasFieldOrPropertyWithValue("code", ErrorCodeEnums.INVALID_RECEIVING_TASK_STATUS.getCode());

        verify(receivingTaskRepository).findByIds(List.of(taskId));
        verifyNoInteractions(receivingTaskDtoApplicationMapper);
    }

    @Test
    void batchUpdateVendor_ShouldThrowException_WhenMixedValidAndInvalidStatus() {
        // Arrange
        UUID taskId1 = UUID.randomUUID();
        UUID taskId2 = UUID.randomUUID();

        ReceivingTask task1 = createReceivingTask(taskId1, ReceivingTaskStatus.CREATED);
        task1.setVendor(SourceEnum.VERNON.name());

        ReceivingTask task2 = createReceivingTask(taskId2, ReceivingTaskStatus.RECEIVING); // Invalid status
        task2.setVendor(SourceEnum.VERNON.name());

        BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
            .receivingTaskIds(List.of(taskId1, taskId2))
            .vendor(SourceEnum.EXOTIC)
            .build();

        when(receivingTaskRepository.findByIds(List.of(taskId1, taskId2)))
            .thenReturn(List.of(task1, task2));

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.batchUpdateVendor(command))
            .isInstanceOf(WmsBusinessException.class)
            .hasFieldOrPropertyWithValue("code", ErrorCodeEnums.INVALID_RECEIVING_TASK_STATUS.getCode());

        verify(receivingTaskRepository).findByIds(List.of(taskId1, taskId2));
        verifyNoInteractions(receivingTaskDtoApplicationMapper);
    }

    @Test
    void batchUpdateVendor_ShouldWorkForAllOnlineVendors() {
        // Test each vendor in ONLINE_VENDOR_SET
        SourceEnum[] onlineVendors = {
            SourceEnum.MISSION,
            SourceEnum.VERNON,
            SourceEnum.EXOTIC,
            SourceEnum.SEVEN_STARS,
            SourceEnum.SMART_AND_FINAL,
            SourceEnum.CORE_MARK,
            SourceEnum.DOLLAR_EMPIRE
        };

        for (SourceEnum vendor : onlineVendors) {
            // Arrange
            UUID taskId = UUID.randomUUID();
            ReceivingTask task = createReceivingTask(taskId, ReceivingTaskStatus.CREATED);
            task.setVendor(SourceEnum.VERNON.name());

            ReceivingTaskDto dto = createReceivingTaskDto(taskId);
            dto.setVendor(vendor.name());
            dto.setStatus(ReceivingTaskStatus.CREATED);

            BatchUpdateVendorCommand command = BatchUpdateVendorCommand.builder()
                .receivingTaskIds(List.of(taskId))
                .vendor(vendor)
                .build();

            when(receivingTaskRepository.findByIds(List.of(taskId))).thenReturn(List.of(task));
            when(receivingTaskRepository.saveAll(List.of(task))).thenReturn(List.of(task));
            when(receivingTaskDtoApplicationMapper.domainToDto(task)).thenReturn(dto);

            // Act
            List<ReceivingTaskDto> result = receivingTaskApplicationService.batchUpdateVendor(command);

            // Assert
            assertThat(result).hasSize(1);
            assertThat(result.getFirst().getVendor()).isEqualTo(vendor.name());
            assertThat(result.getFirst().getStatus()).isEqualTo(ReceivingTaskStatus.CREATED);

            verify(receivingTaskRepository).findByIds(List.of(taskId));
            verify(receivingTaskRepository).saveAll(List.of(task));
            verify(receivingTaskDtoApplicationMapper).domainToDto(task);
        }
    }
}