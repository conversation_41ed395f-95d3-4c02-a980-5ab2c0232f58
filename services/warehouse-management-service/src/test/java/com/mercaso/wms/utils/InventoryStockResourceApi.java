package com.mercaso.wms.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.command.inventorystock.CreateInventoryStockCommand;
import com.mercaso.wms.application.command.inventorystock.QueryInventoryStockCommand;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class InventoryStockResourceApi extends IntegrationTestRestUtil {

    private static final String CREATE_URL = "/inventory-stocks";

    private static final String SEARCH_URL = "/search/inventory-stocks";

    public static final String AVAILABLE_URL = "/inventory-stocks/%s/available";

    public static final String UNAVAILABLE_URL = "/inventory-stocks/%s/unavailable";

    public static final String QUERY_URL = "/query/inventory-stocks/%s";

    public static final String QUERY_BY_SKU_NUMBERS_URL = "/query/inventory-stocks/%s/by-sku-numbers";

    public static final String QUERY_BY_PICKING_TASK_ITEMS_URL = "/query/inventory-stocks/%s/by-picking-task-items";

    public InventoryStockResourceApi(Environment environment) {
        super(environment);
    }

    public InventoryStockDto create(CreateInventoryStockCommand command) {
        return createEntity(CREATE_URL, SerializationUtils.serialize(command), InventoryStockDto.class);
    }

    public Result<InventoryStockDto> search(
        List<String> skuNumbers,
        String locationName,
        InventoryStockStatus status,
        String title,
        String lotNumber,
        String lpnNumber,
        SortType sort) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_URL + "?page=1&pageSize=20");
        if (skuNumbers != null) {
            url.append("&skuNumbers=").append(String.join(",", skuNumbers));
        }
        if (locationName != null) {
            url.append("&locationName=").append(locationName);
        }
        if (status != null) {
            url.append("&status=").append(status);
        }
        if (title != null) {
            url.append("&title=").append(title);
        }
        if (lotNumber != null) {
            url.append("&lotNumber=").append(lotNumber);
        }
        if (lpnNumber != null) {
            url.append("&lpnNumber=").append(lpnNumber);
        }
        if (sort != null) {
            url.append("&sort=").append(sort);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Result<InventoryStockDto>>() {
        });
    }

    public InventoryStockDto available(UUID inventoryStockId) throws Exception {
        return updateEntity(String.format(AVAILABLE_URL, inventoryStockId), null, InventoryStockDto.class);
    }

    public InventoryStockDto unavailable(UUID inventoryStockId) throws Exception {
        return updateEntity(String.format(UNAVAILABLE_URL, inventoryStockId), null, InventoryStockDto.class);
    }

    public InventoryStockDto findById(UUID inventoryStockId) {
        return getEntity(String.format(QUERY_URL, inventoryStockId), InventoryStockDto.class).getBody();
    }

    public Map<String, List<InventoryStockDto>> findBySkuNumbers(UUID warehouseId, List<String> skuNumbers) throws Exception {
        String body = getEntity(
            String.format(QUERY_BY_SKU_NUMBERS_URL, warehouseId) + "?skuNumbers=" + String.join(",", skuNumbers),
            String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Map<String, List<InventoryStockDto>>>() {
        });
    }

    public Map<String, List<InventoryStockDto>> findByPickingTaskItemIds(UUID warehouseId, List<UUID> pickingTaskItemIds)
        throws Exception {
        String body = getEntity(
            String.format(QUERY_BY_PICKING_TASK_ITEMS_URL, warehouseId) + "?pickingTaskItemIds=" + String.join(",",
                pickingTaskItemIds.stream().map(UUID::toString).toList()),
            String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Map<String, List<InventoryStockDto>>>() {
        });
    }

    public Map<String, List<InventoryStockDto>> findByPickingTaskItemIdsPost(UUID warehouseId, List<UUID> pickingTaskItemIds)
        throws Exception {
        QueryInventoryStockCommand command = QueryInventoryStockCommand.builder()
            .pickingTaskItemIds(pickingTaskItemIds)
            .build();
        String body = createEntity(
            String.format(QUERY_BY_PICKING_TASK_ITEMS_URL, warehouseId),
            SerializationUtils.serialize(command),
            String.class);
        return SerializationUtils.readValue(body, new TypeReference<Map<String, List<InventoryStockDto>>>() {
        });
    }
}