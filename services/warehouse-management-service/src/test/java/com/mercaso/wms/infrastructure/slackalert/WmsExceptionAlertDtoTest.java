package com.mercaso.wms.infrastructure.slackalert;

import static org.assertj.core.api.Assertions.assertThat;

import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.alert.dto.WmsExceptionAlertDto;
import java.time.LocalDate;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class WmsExceptionAlertDtoTest {

    @Test
    void when_createGhostInventoryCleanupFailureAlert_with_validParams_then_createCorrectAlert() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-************";
        String deliveryItemsSummary = "[{sku=SKU001,qty=2},{sku=SKU002,qty=1}]";
        String errorMessage = "Database connection timeout during inventory cleanup";

        // When
        WmsExceptionAlertDto alert = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        assertThat(alert).isNotNull();
        assertThat(alert.getAlertEventType()).isEqualTo(WmsExceptionAlertDto.AlertEventType.GHOST_INVENTORY_CLEANUP_FAILED);
        assertThat(alert.getSeverity()).isEqualTo(WmsExceptionAlertDto.AlertSeverity.HIGH);
        assertThat(alert.getTitle()).isEqualTo("Ghost Inventory Cleanup Failed");
        assertThat(alert.getDescription()).contains(orderNumber);
        assertThat(alert.getDescription()).contains(deliveryOrderId.toString());
        assertThat(alert.getEntityId()).isEqualTo(deliveryOrderId);
        assertThat(alert.getEntityType()).isEqualTo(EntityEnums.DELIVERY_ORDER);
        assertThat(alert.getDetectedAt()).isNotNull();

        // Verify context data
        assertThat(alert.getContextData()).isNotNull();
        assertThat(alert.getContextData().get("deliveryOrderId")).isEqualTo(deliveryOrderId.toString());
        assertThat(alert.getContextData().get("orderNumber")).isEqualTo(orderNumber);
        assertThat(alert.getContextData().get("deliveryItems")).isEqualTo(deliveryItemsSummary);
        assertThat(alert.getContextData().get("errorMessage")).isEqualTo(errorMessage);
        assertThat(alert.getContextData().get("requiresRecovery")).isEqualTo(true);
        assertThat(alert.getContextData().get("alertTime")).isNotNull();
    }

    @Test
    void when_createGhostInventoryCleanupFailureAlert_with_nullOrderNumber_then_handleGracefully() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = null;
        String deliveryItemsSummary = "[{sku=SKU001,qty=2}]";
        String errorMessage = "Service unavailable";

        // When
        WmsExceptionAlertDto alert = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        assertThat(alert).isNotNull();
        assertThat(alert.getAlertEventType()).isEqualTo(WmsExceptionAlertDto.AlertEventType.GHOST_INVENTORY_CLEANUP_FAILED);
        assertThat(alert.getSeverity()).isEqualTo(WmsExceptionAlertDto.AlertSeverity.HIGH);
        assertThat(alert.getDescription()).contains("N/A");
        assertThat(alert.getContextData().get("orderNumber")).isEqualTo("N/A");
    }

    @Test
    void when_createGhostInventoryCleanupFailureAlert_with_nullDeliveryItems_then_handleGracefully() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-20250123-002";
        String deliveryItemsSummary = null;
        String errorMessage = "Inventory lock timeout";

        // When
        WmsExceptionAlertDto alert = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        assertThat(alert).isNotNull();
        assertThat(alert.getContextData().get("deliveryItems")).isEqualTo("NO_ITEMS");
    }

    @Test
    void when_createGhostInventoryCleanupFailureAlert_with_nullErrorMessage_then_handleGracefully() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-20250123-003";
        String deliveryItemsSummary = "[{sku=SKU003,qty=5}]";
        String errorMessage = null;

        // When
        WmsExceptionAlertDto alert = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        assertThat(alert).isNotNull();
        assertThat(alert.getContextData().get("errorMessage")).isEqualTo("Unknown error");
    }

    @Test
    void when_createGhostInventoryCleanupFailureAlert_with_emptyDeliveryItems_then_handleCorrectly() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-20250123-004";
        String deliveryItemsSummary = "NO_ITEMS";
        String errorMessage = "Connection failed";

        // When
        WmsExceptionAlertDto alert = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        assertThat(alert).isNotNull();
        assertThat(alert.getContextData().get("deliveryItems")).isEqualTo("NO_ITEMS");
    }

    @Test
    void when_createOrderAfterBatchAlert_then_createCorrectAlert() {
        // Given - test existing method to ensure we didn't break anything
        String orderNumber = "ORD-20250123-005";
        UUID orderId = UUID.randomUUID();
        String batchNumber = "BATCH-001";
        UUID batchId = UUID.randomUUID();
        LocalDate deliveryDate = LocalDate.of(2025, 1, 23);
        String orderDetailUrl = "http://example.com/orders/" + orderId;

        // When
        WmsExceptionAlertDto alert = WmsExceptionAlertDto.createOrderAfterBatchAlert(
            orderNumber, orderId, batchNumber, batchId, deliveryDate, orderDetailUrl);

        // Then
        assertThat(alert).isNotNull();
        assertThat(alert.getAlertEventType()).isEqualTo(WmsExceptionAlertDto.AlertEventType.ORDER_AFTER_BATCH_CREATION);
        assertThat(alert.getSeverity()).isEqualTo(WmsExceptionAlertDto.AlertSeverity.CRITICAL);
        assertThat(alert.getTitle()).isEqualTo("Order Created After Batch");
        assertThat(alert.getEntityId()).isEqualTo(orderId);
        assertThat(alert.getEntityType()).isEqualTo(EntityEnums.SHIPPING_ORDER);
    }

    @Test
    void when_testAlertEventTypeEnum_then_verifyGhostInventoryCleanupFailedExists() {
        // Given & When
        WmsExceptionAlertDto.AlertEventType eventType = WmsExceptionAlertDto.AlertEventType.GHOST_INVENTORY_CLEANUP_FAILED;

        // Then
        assertThat(eventType).isNotNull();
        assertThat(eventType.getValue()).isEqualTo("GHOST_INVENTORY_CLEANUP_FAILED");
        assertThat(eventType.getDisplayName()).isEqualTo("Ghost Inventory Cleanup Failed");
    }

    @Test
    void when_testAlertSeverityEnum_then_verifyHighSeverityProperties() {
        // Given & When
        WmsExceptionAlertDto.AlertSeverity severity = WmsExceptionAlertDto.AlertSeverity.HIGH;

        // Then
        assertThat(severity).isNotNull();
        assertThat(severity.getValue()).isEqualTo("HIGH");
        assertThat(severity.getDisplayName()).isEqualTo("High");
        assertThat(severity.getEmoji()).isEqualTo("🟠");
    }

    @Test
    void when_testEntityEnums_then_verifyShippingOrderProperties() {
        // Given & When
        EntityEnums entityType = EntityEnums.SHIPPING_ORDER;

        // Then
        assertThat(entityType).isNotNull();
        assertThat(entityType.getValue()).isEqualTo("ShippingOrder");
        assertThat(entityType.getIdField()).isEqualTo("shippingOrderId");
    }
}
