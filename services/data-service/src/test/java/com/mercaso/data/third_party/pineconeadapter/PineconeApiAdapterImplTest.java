package com.mercaso.data.third_party.pineconeadapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.google.protobuf.Struct;
import com.mercaso.data.master_catalog.adaptor.impl.PineconeApiAdapterImpl;
import com.mercaso.data.master_catalog.config.PineconeConfig;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.impl.MasterCatalogGeneratePredictionServiceImpl;
import io.pinecone.clients.Index;
import io.pinecone.clients.Pinecone;
import io.pinecone.proto.UpsertResponse;
import io.pinecone.unsigned_indices_model.QueryResponseWithUnsignedIndices;
import io.pinecone.unsigned_indices_model.ScoredVectorWithUnsignedIndices;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.openapitools.inference.client.ApiException;
import org.openapitools.inference.client.model.DenseEmbedding;
import org.openapitools.inference.client.model.Embedding;

public class PineconeApiAdapterImplTest {

    private PineconeApiAdapterImpl pineconeApiAdapter;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private MasterCatalogGeneratePredictionServiceImpl masterCatalogGeneratePotentialDuplicationService;
    private Index indexMock;

    @BeforeEach
    public void setUp() throws TimeoutException {
        MockitoAnnotations.openMocks(this);
        indexMock = mock(Index.class);
        Pinecone pineconeMock = mock(Pinecone.class);
        PineconeConfig pineconeConfig = mock(PineconeConfig.class);

        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
        masterCatalogGeneratePotentialDuplicationService = mock(
            MasterCatalogGeneratePredictionServiceImpl.class);
        doReturn("").when(pineconeConfig).getApiKey();
        pineconeApiAdapter = spy(
            new PineconeApiAdapterImpl(pineconeConfig));
        doReturn(pineconeMock).when(pineconeApiAdapter).getPineconeInstance(any());

        when(pineconeMock.getIndexConnection(anyString())).thenReturn(indexMock);
        doNothing().when(pineconeApiAdapter).safelyDeleteIndex(anyString(), any());
        doNothing().when(pineconeApiAdapter).waitForIndexDeletion(anyString(), any(), anyLong());
        doNothing().when(pineconeApiAdapter).safelyCreateIndex(anyString(), any());
        doNothing().when(pineconeApiAdapter).waitForIndexCreation(anyString(), any(), anyLong());
        doNothing().when(pineconeApiAdapter).waitForVectorsUpsert(any(), anyInt(), anyLong());

    }

    @Test
    void testInBatchRemoveDuplicationNormalTest() {

        String indexName = "test-index";
        Float threshold = 0.90f;
        UUID id1 = UUID.fromString("5da190cb-0ab2-3d09-067b-48b69fddce3a");
        UUID id2 = UUID.fromString("5cd6bbb7-7da0-2550-c43c-c728fee9e8dc");
        List<String> dataList = Arrays.asList(id1.toString(), id2.toString());

        MasterCatalogRawData data1 = new MasterCatalogRawData();
        data1.setId(id1);
        data1.setDescription("Dr. Pepper, Creamy Coconut, 20 oz (24 Pack)");
        data1.setPackageType(PackageType.PACK);

        MasterCatalogRawData data2 = new MasterCatalogRawData();
        data2.setId(id2);
        data2.setDescription("Dr. Pepper, Creamy Coconut, 12 oz (24 Pack)");
        data2.setPackageType(PackageType.PACK);

        List<String> rawIds = new ArrayList<>(Arrays.asList("5da190cb-0ab2-3d09-067b-48b69fddce3a",
            "5cd6bbb7-7da0-2550-c43c-c728fee9e8dc"));
        List<String> descriptionList = new ArrayList<>(
            Arrays.asList("Dr. Pepper, Creamy Coconut, 20 oz (24 Pack)",
                "Dr. Pepper, Creamy Coconut, 12 oz (24 Pack)"));
        List<Boolean> packTypeList = new ArrayList<>(
            Arrays.asList(Boolean.TRUE, Boolean.TRUE));
        when(masterCatalogRawDataRepository.findById(
            UUID.fromString("5da190cb-0ab2-3d09-067b-48b69fddce3a"))).thenReturn(
            Optional.of(data1));
        when(masterCatalogRawDataRepository.findById(
            UUID.fromString("5cd6bbb7-7da0-2550-c43c-c728fee9e8dc"))).thenReturn(
            Optional.of(data2));
        Struct dummyStruct = Struct.newBuilder().build();
        List<Struct> metaDataList = new ArrayList<>(Arrays.asList(dummyStruct, dummyStruct));
        DenseEmbedding dummyDenseEmbedding1 = mock(DenseEmbedding.class);
        when(dummyDenseEmbedding1.getValues()).thenReturn(Arrays.asList(0.1f, 0.2f, 0.3f));
        Embedding dummyEmbedding1 = mock(Embedding.class);
        when(dummyEmbedding1.getDenseEmbedding()).thenReturn(dummyDenseEmbedding1);

        DenseEmbedding dummyDenseEmbedding2 = mock(DenseEmbedding.class);
        when(dummyDenseEmbedding2.getValues()).thenReturn(Arrays.asList(0.4f, 0.5f, 0.6f));
        Embedding dummyEmbedding2 = mock(Embedding.class);
        when(dummyEmbedding2.getDenseEmbedding()).thenReturn(dummyDenseEmbedding2);

        try {
            doReturn(Arrays.asList(dummyEmbedding1, dummyEmbedding2)).when(pineconeApiAdapter)
                .getEmbeddings(any(), anyList());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }

        UpsertResponse dummyUpsertResponse = mock(UpsertResponse.class);
        when(dummyUpsertResponse.getUpsertedCount()).thenReturn(2);
        when(indexMock.upsert(anyList(), eq(""))).thenReturn(dummyUpsertResponse);

        QueryResponseWithUnsignedIndices queryResponse1 = mock(
            QueryResponseWithUnsignedIndices.class);
        ScoredVectorWithUnsignedIndices scoredVector1 = mock(ScoredVectorWithUnsignedIndices.class);
        String duplicateId1 = UUID.randomUUID().toString();
        when(scoredVector1.getId()).thenReturn(duplicateId1);
        when(scoredVector1.getScore()).thenReturn(0.91f); // Above threshold.
        when(queryResponse1.getMatchesList()).thenReturn(
            Arrays.asList(scoredVector1, scoredVector1));
        when(queryResponse1.getMatches(1)).thenReturn(scoredVector1);

        // For the second data: duplicate match with a score below threshold.
        QueryResponseWithUnsignedIndices queryResponse2 = mock(
            QueryResponseWithUnsignedIndices.class);
        ScoredVectorWithUnsignedIndices scoredVector2 = mock(ScoredVectorWithUnsignedIndices.class);
        String duplicateId2 = UUID.randomUUID().toString();
        when(scoredVector2.getId()).thenReturn(duplicateId2);
        when(scoredVector2.getScore()).thenReturn(0.89f); // Below threshold.
        when(queryResponse2.getMatchesList()).thenReturn(
            Arrays.asList(scoredVector2, scoredVector2));
        when(queryResponse2.getMatches(1)).thenReturn(scoredVector2);

        // When index.query is called in singleQueryList, return queryResponse1 then queryResponse2.
        when(indexMock.query(anyInt(), anyList(), isNull(), isNull(), isNull(), isNull(), any(),
            eq(false), eq(false)))
            .thenReturn(queryResponse1, queryResponse2);

        List<List<UUID>> result = pineconeApiAdapter.getDuplicationPredictionForInBatch(rawIds,
            descriptionList, packTypeList, metaDataList,
            indexName, threshold, 16);

        assertEquals(2, result.size());
        List<UUID> resultEntry1 = result.get(0);
        assertEquals(2, resultEntry1.size());
        assertTrue(resultEntry1.contains(id1));
        assertTrue(resultEntry1.contains(UUID.fromString(duplicateId1)));

        // For the second data, since the score is below threshold, we expect a singleton list: [id2].
        List<UUID> resultEntry2 = result.get(1);
        assertEquals(1, resultEntry2.size());
        assertTrue(resultEntry2.contains(id2));

    }

    @Test
    void testInBatchRemoveDuplicationHasDuplicatedPair() {

        String indexName = "test-index";
        Float threshold = 0.90f;
        UUID id1 = UUID.fromString("5da190cb-0ab2-3d09-067b-48b69fddce3a");
        UUID id2 = UUID.fromString("5cd6bbb7-7da0-2550-c43c-c728fee9e8dc");
        List<UUID> dataList = Arrays.asList(id1, id2);

        MasterCatalogRawData data1 = new MasterCatalogRawData();
        data1.setId(id1);
        data1.setDescription("Dr. Pepper, Creamy Coconut, 20 oz (24 Pack)");
        data1.setPackageType(PackageType.PACK);

        MasterCatalogRawData data2 = new MasterCatalogRawData();
        data2.setId(id2);
        data2.setDescription("Dr. Pepper, Creamy Coconut, 12 oz (24 Pack)");
        data2.setPackageType(PackageType.PACK);

        List<String> rawIds = new ArrayList<>(Arrays.asList("5da190cb-0ab2-3d09-067b-48b69fddce3a",
            "5cd6bbb7-7da0-2550-c43c-c728fee9e8dc"));
        when(masterCatalogRawDataRepository.findById(
            UUID.fromString("5da190cb-0ab2-3d09-067b-48b69fddce3a"))).thenReturn(
            Optional.of(data1));
        when(masterCatalogRawDataRepository.findById(
            UUID.fromString("5cd6bbb7-7da0-2550-c43c-c728fee9e8dc"))).thenReturn(
            Optional.of(data2));
        List<String> descriptionList = new ArrayList<>(
            Arrays.asList("Dr. Pepper, Creamy Coconut, 20 oz (24 Pack)",
                "Dr. Pepper, Creamy Coconut, 12 oz (24 Pack)"));
        List<Boolean> packTypeList = new ArrayList<>(
            Arrays.asList(Boolean.TRUE, Boolean.TRUE));
        Struct dummyStruct = Struct.newBuilder().build();
        List<Struct> metaDataList = new ArrayList<>(Arrays.asList(dummyStruct, dummyStruct));

        DenseEmbedding dummyDenseEmbedding1 = mock(DenseEmbedding.class);
        when(dummyDenseEmbedding1.getValues()).thenReturn(Arrays.asList(0.1f, 0.2f, 0.3f));
        Embedding dummyEmbedding1 = mock(Embedding.class);
        when(dummyEmbedding1.getDenseEmbedding()).thenReturn(dummyDenseEmbedding1);

        DenseEmbedding dummyDenseEmbedding2 = mock(DenseEmbedding.class);
        when(dummyDenseEmbedding2.getValues()).thenReturn(Arrays.asList(0.4f, 0.5f, 0.6f));
        Embedding dummyEmbedding2 = mock(Embedding.class);
        when(dummyEmbedding2.getDenseEmbedding()).thenReturn(dummyDenseEmbedding2);

        try {
            doReturn(Arrays.asList(dummyEmbedding1, dummyEmbedding2)).when(pineconeApiAdapter)
                .getEmbeddings(any(), anyList());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }

        UpsertResponse dummyUpsertResponse = mock(UpsertResponse.class);
        when(dummyUpsertResponse.getUpsertedCount()).thenReturn(2);
        when(indexMock.upsert(anyList(), eq(""))).thenReturn(dummyUpsertResponse);

        QueryResponseWithUnsignedIndices queryResponse1 = mock(
            QueryResponseWithUnsignedIndices.class);
        ScoredVectorWithUnsignedIndices scoredVector1 = mock(ScoredVectorWithUnsignedIndices.class);
        UUID duplicateId1 = id2;
        when(scoredVector1.getId()).thenReturn(String.valueOf(duplicateId1));
        when(scoredVector1.getScore()).thenReturn(0.91f); // Above threshold.
        when(queryResponse1.getMatchesList()).thenReturn(
            Arrays.asList(scoredVector1, scoredVector1));
        when(queryResponse1.getMatches(1)).thenReturn(scoredVector1);

        // For the second data: duplicate match with a score below threshold.
        QueryResponseWithUnsignedIndices queryResponse2 = mock(
            QueryResponseWithUnsignedIndices.class);
        ScoredVectorWithUnsignedIndices scoredVector2 = mock(ScoredVectorWithUnsignedIndices.class);
        UUID duplicateId2 = id1;
        when(scoredVector2.getId()).thenReturn(String.valueOf(duplicateId2));
        when(scoredVector2.getScore()).thenReturn(0.91f); // Below threshold.
        when(queryResponse2.getMatchesList()).thenReturn(
            Arrays.asList(scoredVector2, scoredVector2));
        when(queryResponse2.getMatches(1)).thenReturn(scoredVector2);

        // When index.query is called in singleQueryList, return queryResponse1 then queryResponse2.
        when(indexMock.query(anyInt(), anyList(), isNull(), isNull(), isNull(), isNull(), any(),
            eq(false), eq(false)))
            .thenReturn(queryResponse1, queryResponse2);

        List<List<UUID>> result = pineconeApiAdapter.getDuplicationPredictionForInBatch(rawIds,
            descriptionList, packTypeList, metaDataList,
            indexName, threshold, 16);

        assertEquals(1, result.size());
        List<UUID> resultEntry1 = result.get(0);
        assertEquals(2, resultEntry1.size());
        assertTrue(resultEntry1.contains(id1));
        assertTrue(resultEntry1.contains(duplicateId1));

    }

    @Test
    void testProductRemoveDuplicationNormalTest() {
        String indexName = "test-index";
        Float threshold = 0.90f;
        UUID id1 = UUID.fromString("5da190cb-0ab2-3d09-067b-48b69fddce3a");
        UUID id2 = UUID.fromString("5cd6bbb7-7da0-2550-c43c-c728fee9e8dc");
        List<UUID> dataList = Arrays.asList(id1, id2);

        MasterCatalogRawData data1 = new MasterCatalogRawData();
        data1.setId(id1);
        data1.setDescription("Dr. Pepper, Creamy Coconut, 20 oz (24 Pack)");
        data1.setPackageType(PackageType.PACK);

        MasterCatalogRawData data2 = new MasterCatalogRawData();
        data2.setId(id2);
        data2.setDescription("Dr. Pepper, Creamy Coconut, 12 oz (24 Pack)");
        data2.setPackageType(PackageType.PACK);

        List<String> rawIds = new ArrayList<>(Arrays.asList("5da190cb-0ab2-3d09-067b-48b69fddce3a",
            "5cd6bbb7-7da0-2550-c43c-c728fee9e8dc"));
        when(masterCatalogRawDataRepository.findById(
            UUID.fromString("5da190cb-0ab2-3d09-067b-48b69fddce3a"))).thenReturn(
            Optional.of(data1));
        when(masterCatalogRawDataRepository.findById(
            UUID.fromString("5cd6bbb7-7da0-2550-c43c-c728fee9e8dc"))).thenReturn(
            Optional.of(data2));
        List<String> descriptionList = new ArrayList<>(
            Arrays.asList("Dr. Pepper, Creamy Coconut, 20 oz (24 Pack)",
                "Dr. Pepper, Creamy Coconut, 12 oz (24 Pack)"));
        List<Boolean> packTypeList = new ArrayList<>(
            Arrays.asList(Boolean.TRUE, Boolean.TRUE));
        DenseEmbedding dummyDenseEmbedding1 = mock(DenseEmbedding.class);
        when(dummyDenseEmbedding1.getValues()).thenReturn(Arrays.asList(0.1f, 0.2f, 0.3f));
        Embedding dummyEmbedding1 = mock(Embedding.class);
        when(dummyEmbedding1.getDenseEmbedding()).thenReturn(dummyDenseEmbedding1);

        DenseEmbedding dummyDenseEmbedding2 = mock(DenseEmbedding.class);
        when(dummyDenseEmbedding2.getValues()).thenReturn(Arrays.asList(0.4f, 0.5f, 0.6f));
        Embedding dummyEmbedding2 = mock(Embedding.class);
        when(dummyEmbedding2.getDenseEmbedding()).thenReturn(dummyDenseEmbedding2);

        try {
            doReturn(Arrays.asList(dummyEmbedding1, dummyEmbedding2)).when(pineconeApiAdapter)
                .getEmbeddings(any(), anyList());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }

        UpsertResponse dummyUpsertResponse = mock(UpsertResponse.class);
        when(dummyUpsertResponse.getUpsertedCount()).thenReturn(2);
        when(indexMock.upsert(anyList(), eq(""))).thenReturn(dummyUpsertResponse);

        QueryResponseWithUnsignedIndices queryResponse1 = mock(
            QueryResponseWithUnsignedIndices.class);
        ScoredVectorWithUnsignedIndices scoredVector1 = mock(ScoredVectorWithUnsignedIndices.class);
        String duplicateId1 = UUID.randomUUID().toString();
        when(scoredVector1.getId()).thenReturn(duplicateId1);
        when(scoredVector1.getScore()).thenReturn(0.91f); // Above threshold.
        when(queryResponse1.getMatchesList()).thenReturn(
            Arrays.asList(scoredVector1, scoredVector1));
        when(queryResponse1.getMatches(0)).thenReturn(scoredVector1);

        // For the second data: duplicate match with a score below threshold.
        QueryResponseWithUnsignedIndices queryResponse2 = mock(
            QueryResponseWithUnsignedIndices.class);
        ScoredVectorWithUnsignedIndices scoredVector2 = mock(ScoredVectorWithUnsignedIndices.class);
        String duplicateId2 = UUID.randomUUID().toString();
        when(scoredVector2.getId()).thenReturn(duplicateId2);
        when(scoredVector2.getScore()).thenReturn(0.89f); // Below threshold.
        when(queryResponse2.getMatchesList()).thenReturn(
            Arrays.asList(scoredVector2, scoredVector2));
        when(queryResponse2.getMatches(0)).thenReturn(scoredVector2);

        // When index.query is called in singleQueryList, return queryResponse1 then queryResponse2.
        when(indexMock.query(anyInt(), anyList(), isNull(), isNull(), isNull(), isNull(), any(),
            eq(false), eq(false)))
            .thenReturn(queryResponse1, queryResponse2);

        List<List<UUID>> result = pineconeApiAdapter.getDuplicationPredictionForProduct(rawIds,
            descriptionList, packTypeList,
            indexName, threshold, 32);

        assertEquals(2, result.size());
        List<UUID> resultEntry1 = result.get(0);
        assertEquals(2, resultEntry1.size());
        assertTrue(resultEntry1.contains(id1));
        assertTrue(resultEntry1.contains(UUID.fromString(duplicateId1)));

        // For the second data, since the score is below threshold, we expect a singleton list: [id2].
        List<UUID> resultEntry2 = result.get(1);
        assertEquals(1, resultEntry2.size());
        assertTrue(resultEntry2.contains(id2));
    }

    @Test
    void testUpsertNormal() throws ApiException {

        Embedding dummyEmbedding = mock(Embedding.class);
        DenseEmbedding dummyDenseEmbedding = mock(DenseEmbedding.class);
        when(dummyEmbedding.getDenseEmbedding()).thenReturn(dummyDenseEmbedding);
        List<Float> dummyVector = Arrays.asList(0.1f, 0.2f, 0.3f);
        when(dummyDenseEmbedding.getValues()).thenReturn(dummyVector);
        List<Embedding> dummyEmbeddings = Collections.singletonList(dummyEmbedding);
        doReturn(dummyEmbeddings).when(pineconeApiAdapter).getEmbeddings(any(), anyList());

        // Prepare input lists.
        List<String> idList = Collections.singletonList(UUID.randomUUID().toString());
        List<String> descriptionList = Collections.singletonList("dummy description");
        Struct dummyStruct = Struct.newBuilder().build();
        List<Struct> metaDataList = Collections.singletonList(dummyStruct);

        // Prepare a dummy UpsertResponse.
        UpsertResponse dummyUpsertResponse = mock(UpsertResponse.class);
        when(dummyUpsertResponse.getUpsertedCount()).thenReturn(1);
        when(indexMock.upsert(anyList(), anyString())).thenReturn(dummyUpsertResponse);

        // Call the method under test.
        int count = pineconeApiAdapter.upsertToProductTable(idList, descriptionList, metaDataList,
            "test-index", 32);

        // Verify that the returned upsert count is as expected.
        assertEquals(1, count);
    }


}
