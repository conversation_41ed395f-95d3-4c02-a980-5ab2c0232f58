package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationStartEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationStartPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePredictionService;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class RemoveDuplicationStartEventListenerTest {

    private ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private MasterCatalogGeneratePredictionService masterCatalogGeneratePredictionService;
    private MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;

    private RemoveDuplicationStartEventListener listener;

    @BeforeEach
    void setUp() {
        applicationEventPublisherProvider = mock(ApplicationEventPublisherProvider.class);
        masterCatalogGeneratePredictionService = mock(
            MasterCatalogGeneratePredictionService.class);
        masterCatalogBatchJobMapper = mock(
            MasterCatalogBatchJobMapper.class);
        masterCatalogBatchJobRepository = mock(
            MasterCatalogBatchJobRepository.class);

        listener = new RemoveDuplicationStartEventListener(
            applicationEventPublisherProvider,
            masterCatalogGeneratePredictionService,
            masterCatalogBatchJobMapper,
            masterCatalogBatchJobRepository
        );

    }

    @Test
    void testNormalCase() {

        UUID taskId = UUID.randomUUID();
        List<UUID> rawDataIds = List.of(UUID.randomUUID());
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        RemoveDuplicationStartPayload payload = new RemoveDuplicationStartPayload();
        payload.setData(taskDto);
        payload.setRawDataIds(rawDataIds);

        RemoveDuplicationStartEvent event = new RemoveDuplicationStartEvent(this, payload);

        MasterCatalogBatchJob task = MasterCatalogBatchJob.builder()
            .id(taskId).build();

        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(task);
        List<List<UUID>> duplicateRawIds = List.of(List.of(UUID.randomUUID()));

        when(masterCatalogGeneratePredictionService.generatePotentialDuplicationInBatch(
            rawDataIds,
            "remove-duplication-temporary", 0.93F))
            .thenReturn(duplicateRawIds);
        listener.handleEvent(event);
        assertEquals(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_START, task.getStatus());

        verify(masterCatalogBatchJobRepository).save(task);

        verify(applicationEventPublisherProvider).removeDuplicationInProgress(task,
            duplicateRawIds);
    }

    @Test
    void testFail() {
        RemoveDuplicationStartPayload payload = new RemoveDuplicationStartPayload();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        List<UUID> rawDataIds = List.of(UUID.randomUUID());
        payload.setData(taskDto);
        payload.setRawDataIds(rawDataIds);
        RemoveDuplicationStartEvent event = new RemoveDuplicationStartEvent(this, payload);

        when(masterCatalogGeneratePredictionService.generatePotentialDuplicationInBatch(
            any(), any(), any()))
            .thenReturn(any());

        listener.handleEvent(event);

        verify(masterCatalogBatchJobRepository, never()).save(any());
        verify(applicationEventPublisherProvider, never()).removeDuplicationInProgress(
            any(), any());
    }

}
