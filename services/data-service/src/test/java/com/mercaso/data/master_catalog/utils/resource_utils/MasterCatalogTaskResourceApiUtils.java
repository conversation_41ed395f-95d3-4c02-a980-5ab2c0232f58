package com.mercaso.data.master_catalog.utils.resource_utils;


import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MasterCatalogTaskResourceApiUtils extends IntegrationTestRestUtil {

  public static final String V1_TASKS = "/master-catalog/v1/tasks";
  public static final String V1_TASKS_LIST = "/master-catalog/v1/tasks/search";

  public MasterCatalogTaskResourceApiUtils(Environment environment) {
    super(environment);
  }

  public void assign(String id, String assignTo) {
    String url = V1_TASKS + "/assign/" + id;

    Map<String, String> params = new HashMap<>();
    params.put("assignTo", assignTo);

    try {
      performRequest(url, params, null, HttpMethod.PUT);
    } catch (Exception e) {
      log.error("Error updating potentially duplicate raw data: {}", e.getMessage(), e);
    }
  }

  public CustomPage<MasterCatalogTaskDto> search(String jobId, String jobNumber, String taskNumber, String assignedBy, String assignedTo, MasterCatalogTaskStatus status, MasterCatalogTaskType type, Integer pageNumber, Integer pageSize) {
    Map<String, String> params = new HashMap<>();
    params.put("page", String.valueOf(pageNumber));
    params.put("pageSize", String.valueOf(pageSize));
    
    if (jobId != null) {
      params.put("jobId", jobId);
    }
    if (jobNumber != null) {
      params.put("jobNumber", jobNumber);
    }
    if (taskNumber != null) {
      params.put("taskNumber", jobId);
    }
    if (assignedBy != null) {
      params.put("assignedBy", assignedBy);
    }
    if (assignedTo != null) {
      params.put("assignedTo", assignedTo);
    }
    if (status != null) {
      params.put("status", status.name());
    }
    if (type != null) {
      params.put("type", type.name());
    }

    ParameterizedTypeReference<CustomPage<MasterCatalogTaskDto>> responseType = new ParameterizedTypeReference<>() {};
    return getEntityByMap(V1_TASKS_LIST, responseType, params).getBody();
  }

  public MasterCatalogTaskDto getById(UUID id) {
    String url = V1_TASKS + "/" + id;
    return getForObject(url, MasterCatalogTaskDto.class);
  }

  public void batchAssign(String assignTo, List<UUID> ids) {
    String url = V1_TASKS + "/batch-assign";

    Map<String, String> params = new HashMap<>();
    params.put("assignTo", assignTo);

    try {
      performRequest(url, params, ids, HttpMethod.POST);
    } catch (Exception e) {
      log.error("Error assigning tasks: {}", e.getMessage(), e);
    }
  }
}
