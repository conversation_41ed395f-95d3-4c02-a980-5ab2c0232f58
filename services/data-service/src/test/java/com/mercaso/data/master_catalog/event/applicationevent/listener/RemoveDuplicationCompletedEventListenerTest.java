package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationCompletedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationCompletedPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePredictionService;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class RemoveDuplicationCompletedEventListenerTest {

    private ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private MasterCatalogGeneratePredictionService masterCatalogGeneratePredictionService;
    private MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private RemoveDuplicationCompletedEventListener listener;

    @BeforeEach
    void setUp() {
        applicationEventPublisherProvider = mock(ApplicationEventPublisherProvider.class);
        masterCatalogGeneratePredictionService = mock(
            MasterCatalogGeneratePredictionService.class);
        masterCatalogBatchJobMapper = mock(
            MasterCatalogBatchJobMapper.class);

        listener = new RemoveDuplicationCompletedEventListener(
            applicationEventPublisherProvider,
            masterCatalogGeneratePredictionService,
            masterCatalogBatchJobMapper
        );

    }

    @Test
    void testNormalCase() {

        UUID taskId = UUID.randomUUID();
        List<UUID> rawDataIds = List.of(UUID.randomUUID());
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        RemoveDuplicationCompletedPayload payload = new RemoveDuplicationCompletedPayload();
        payload.setData(taskDto);
        payload.setRawDataIds(rawDataIds);

        RemoveDuplicationCompletedEvent event = new RemoveDuplicationCompletedEvent(this, payload);

        MasterCatalogBatchJob task = MasterCatalogBatchJob.builder()
            .id(taskId).build();

        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(task);
        List<List<UUID>> duplicateRawIds = List.of(List.of(UUID.randomUUID()));

        when(
            masterCatalogGeneratePredictionService.generatePotentialDuplicationWithProduct(
                eq(rawDataIds),
                anyString(), eq(0.93F)))
            .thenReturn(duplicateRawIds);
        listener.handleEvent(event);
        verify(applicationEventPublisherProvider).generateProductsInProgress(task,
            duplicateRawIds);
    }

    @Test
    void testFail() {
        RemoveDuplicationCompletedPayload payload = new RemoveDuplicationCompletedPayload();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        List<UUID> rawDataIds = List.of(UUID.randomUUID());
        payload.setData(taskDto);
        payload.setRawDataIds(rawDataIds);
        RemoveDuplicationCompletedEvent event = new RemoveDuplicationCompletedEvent(this, payload);

        when(
            masterCatalogGeneratePredictionService.generatePotentialDuplicationWithProduct(
                any(), any(), any()))
            .thenReturn(any());

        listener.handleEvent(event);

        verify(applicationEventPublisherProvider, never()).publishEventCreateAssociationInProgress(
            any(), any());
    }

}

