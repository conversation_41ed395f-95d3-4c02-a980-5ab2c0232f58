package com.mercaso.data.master_catalog.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.Struct;
import com.mercaso.data.master_catalog.adaptor.VectorDBAdapter;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

class MasterCatalogGeneratePredictionServiceImplTest {

    @Mock
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @Mock
    private VectorDBAdapter vectorDBAdapter;

    private MasterCatalogGeneratePredictionServiceImpl service;

    @BeforeEach
    void setUp() {
        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
        vectorDBAdapter = mock(VectorDBAdapter.class);
        service = new MasterCatalogGeneratePredictionServiceImpl(
            masterCatalogRawDataRepository,
            vectorDBAdapter
        );
    }

    @Test
    void generatePotentialDuplicationInBatch_WithValidData_ShouldReturnDuplicationResults() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(id1, id2);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData rawData1 = createMockRawData(id1, "Product A", PackageType.INDIVIDUAL);
        MasterCatalogRawData rawData2 = createMockRawData(id2, "Product B", PackageType.PACK);

        List<MasterCatalogRawData> rawDataList = Arrays.asList(rawData1, rawData2);
        List<List<UUID>> expectedDuplications = Arrays.asList(
            Arrays.asList(id1, id2)
        );

        when(masterCatalogRawDataRepository.findById(id1)).thenReturn(Optional.of(rawData1));
        when(masterCatalogRawDataRepository.findById(id2)).thenReturn(Optional.of(rawData2));
        when(vectorDBAdapter.getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(expectedDuplications);

        // Act
        List<List<UUID>> result = service.generatePotentialDuplicationInBatch(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).size());
        assertTrue(result.get(0).contains(id1));
        assertTrue(result.get(0).contains(id2));

        verify(vectorDBAdapter).getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        );
    }

    @Test
    void generatePotentialDuplicationWithProduct_WithValidData_ShouldReturnDuplicationResults() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(id1, id2);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData rawData1 = createMockRawData(id1, "Product A", PackageType.INDIVIDUAL);
        MasterCatalogRawData rawData2 = createMockRawData(id2, "Product B", PackageType.PACK);

        List<List<UUID>> expectedDuplications = Arrays.asList(
            Arrays.asList(id1, id2)
        );

        when(masterCatalogRawDataRepository.findById(id1)).thenReturn(Optional.of(rawData1));
        when(masterCatalogRawDataRepository.findById(id2)).thenReturn(Optional.of(rawData2));
        when(vectorDBAdapter.getDuplicationPredictionForProduct(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(expectedDuplications);

        // Act
        List<List<UUID>> result = service.generatePotentialDuplicationWithProduct(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).size());
        assertTrue(result.get(0).contains(id1));
        assertTrue(result.get(0).contains(id2));

        verify(vectorDBAdapter).getDuplicationPredictionForProduct(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        );
    }

    @Test
    void pushProduct_WithValidData_ShouldCallVectorDBAdapter() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        String indexName = "test-index";

        MasterCatalogRawData rawData1 = createMockRawData(id1, "Product A", PackageType.INDIVIDUAL);
        rawData1.setUpc("123456789");
        rawData1.setSkuNumber("SKU001");

        MasterCatalogRawData rawData2 = createMockRawData(id2, "Product B", PackageType.PACK);
        rawData2.setUpc("987654321");
        rawData2.setSkuNumber("SKU002");

        List<MasterCatalogRawData> productList = Arrays.asList(rawData1, rawData2);

        // Act
        service.pushProduct(productList, indexName);

        // Assert
        verify(vectorDBAdapter).upsertToProductTable(
            anyList(), anyList(), anyList(), eq(indexName), eq(32)
        );
    }

    @Test
    void generatePotentialAssociation_WithMixedPackageTypes_ShouldSeparateAndProcess() {
        // Arrange
        UUID individualId1 = UUID.randomUUID();
        UUID individualId2 = UUID.randomUUID();
        UUID packId1 = UUID.randomUUID();
        UUID packId2 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(individualId1, individualId2, packId1, packId2);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData individual1 = createMockRawData(individualId1, "Individual A", PackageType.INDIVIDUAL);
        MasterCatalogRawData individual2 = createMockRawData(individualId2, "Individual B", PackageType.INDIVIDUAL);
        MasterCatalogRawData pack1 = createMockRawData(packId1, "Pack A", PackageType.PACK);
        MasterCatalogRawData pack2 = createMockRawData(packId2, "Pack B", PackageType.PACK);

        when(masterCatalogRawDataRepository.findById(individualId1)).thenReturn(Optional.of(individual1));
        when(masterCatalogRawDataRepository.findById(individualId2)).thenReturn(Optional.of(individual2));
        when(masterCatalogRawDataRepository.findById(packId1)).thenReturn(Optional.of(pack1));
        when(masterCatalogRawDataRepository.findById(packId2)).thenReturn(Optional.of(pack2));

        List<List<UUID>> expectedPackAssociations = Arrays.asList(
            Arrays.asList(packId1, packId2)
        );
        List<List<UUID>> expectedIndividualAssociations = Arrays.asList(
            Arrays.asList(individualId1, individualId2)
        );

        when(vectorDBAdapter.getAssociationPrediction(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(expectedPackAssociations).thenReturn(expectedIndividualAssociations);

        // Act
        List<List<UUID>> result = service.generatePotentialAssociation(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(vectorDBAdapter, times(2)).getAssociationPrediction(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        );
    }

    @Test
    void generatePotentialAssociation_WithEmptyList_ShouldReturnEmptyResult() {
        // Arrange
        List<UUID> uuidList = Collections.emptyList();
        String indexName = "test-index";
        Float threshold = 0.8f;

        when(masterCatalogRawDataRepository.findById(any())).thenReturn(Optional.empty());

        // Act
        List<List<UUID>> result = service.generatePotentialAssociation(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void generatePotentialDuplicationInBatch_WithMissingRawData_ShouldSkipMissingItems() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(id1, id2);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData rawData1 = createMockRawData(id1, "Product A", PackageType.INDIVIDUAL);

        when(masterCatalogRawDataRepository.findById(id1)).thenReturn(Optional.of(rawData1));
        when(masterCatalogRawDataRepository.findById(id2)).thenReturn(Optional.empty());
        when(vectorDBAdapter.getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(Collections.emptyList());

        // Act
        List<List<UUID>> result = service.generatePotentialDuplicationInBatch(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void generatePotentialDuplicationWithProduct_WithNullValues_ShouldHandleGracefully() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(id1);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData rawData1 = createMockRawData(id1, null, null);
        rawData1.setUpc(null);
        rawData1.setSkuNumber(null);

        when(masterCatalogRawDataRepository.findById(id1)).thenReturn(Optional.of(rawData1));
        when(vectorDBAdapter.getDuplicationPredictionForProduct(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(Collections.emptyList());

        // Act
        List<List<UUID>> result = service.generatePotentialDuplicationWithProduct(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void generatePotentialAssociation_WithOnlyIndividualItems_ShouldProcessCorrectly() {
        // Arrange
        UUID individualId1 = UUID.randomUUID();
        UUID individualId2 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(individualId1, individualId2);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData individual1 = createMockRawData(individualId1, "Individual A", PackageType.INDIVIDUAL);
        MasterCatalogRawData individual2 = createMockRawData(individualId2, "Individual B", PackageType.INDIVIDUAL);

        when(masterCatalogRawDataRepository.findById(individualId1)).thenReturn(Optional.of(individual1));
        when(masterCatalogRawDataRepository.findById(individualId2)).thenReturn(Optional.of(individual2));

        List<List<UUID>> expectedAssociations = Arrays.asList(
            Arrays.asList(individualId1, individualId2)
        );

        when(vectorDBAdapter.getAssociationPrediction(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(Collections.emptyList()).thenReturn(expectedAssociations);

        // Act
        List<List<UUID>> result = service.generatePotentialAssociation(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).size());
    }

    @Test
    void generatePotentialAssociation_WithOnlyPackItems_ShouldProcessCorrectly() {
        // Arrange
        UUID packId1 = UUID.randomUUID();
        UUID packId2 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(packId1, packId2);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData pack1 = createMockRawData(packId1, "Pack A", PackageType.PACK);
        MasterCatalogRawData pack2 = createMockRawData(packId2, "Pack B", PackageType.PACK);

        when(masterCatalogRawDataRepository.findById(packId1)).thenReturn(Optional.of(pack1));
        when(masterCatalogRawDataRepository.findById(packId2)).thenReturn(Optional.of(pack2));

        List<List<UUID>> expectedAssociations = Arrays.asList(
            Arrays.asList(packId1, packId2)
        );

        when(vectorDBAdapter.getAssociationPrediction(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(expectedAssociations).thenReturn(Collections.emptyList());

        // Act
        List<List<UUID>> result = service.generatePotentialAssociation(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).size());
    }

    @Test
    void createMetaDataForNewProduct_WithValidData_ShouldCreateCorrectMetadata() {
        // Arrange
        List<String> upcList = Arrays.asList("123456789", "987654321");
        List<String> skuList = Arrays.asList("SKU001", "SKU002");
        List<String> nameList = Arrays.asList("Product A", "Product B");
        List<Boolean> packTypeList = Arrays.asList(true, false);

        // Act
        List<Struct> result = service.createMetaDataForNewProduct(upcList, skuList, nameList, packTypeList);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify first metadata entry
        Struct firstMetadata = result.get(0);
        assertTrue(firstMetadata.getFieldsMap().containsKey("single"));
        assertTrue(firstMetadata.getFieldsMap().containsKey("upc"));
        assertTrue(firstMetadata.getFieldsMap().containsKey("sku"));
        assertTrue(firstMetadata.getFieldsMap().containsKey("name"));

        // Verify second metadata entry
        Struct secondMetadata = result.get(1);
        assertTrue(secondMetadata.getFieldsMap().containsKey("single"));
        assertTrue(secondMetadata.getFieldsMap().containsKey("upc"));
        assertTrue(secondMetadata.getFieldsMap().containsKey("sku"));
        assertTrue(secondMetadata.getFieldsMap().containsKey("name"));
    }

    @Test
    void createFiltersFromPackType_WithValidData_ShouldCreateCorrectFilters() {
        // Arrange
        List<Boolean> packTypeList = Arrays.asList(true, false, true);

        // Act
        List<Struct> result = service.createFiltersFromPackType(packTypeList);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());

        for (int i = 0; i < result.size(); i++) {
            Struct filter = result.get(i);
            assertTrue(filter.getFieldsMap().containsKey("single"));
            assertEquals(packTypeList.get(i), filter.getFieldsMap().get("single").getBoolValue());
        }
    }

    @Test
    void generatePotentialDuplicationInBatch_WithLargeBatch_ShouldProcessCorrectly() {
        // Arrange
        List<UUID> uuidList = new ArrayList<>();
        List<MasterCatalogRawData> rawDataList = new ArrayList<>();

        for (int i = 0; i < 100; i++) {
            UUID id = UUID.randomUUID();
            uuidList.add(id);
            rawDataList.add(createMockRawData(id, "Product " + i, PackageType.INDIVIDUAL));
        }

        String indexName = "test-index";
        Float threshold = 0.8f;
        List<List<UUID>> expectedDuplications = Collections.emptyList();

        for (MasterCatalogRawData rawData : rawDataList) {
            when(masterCatalogRawDataRepository.findById(rawData.getId())).thenReturn(Optional.of(rawData));
        }

        when(vectorDBAdapter.getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(expectedDuplications);

        // Act
        List<List<UUID>> result = service.generatePotentialDuplicationInBatch(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(vectorDBAdapter).getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        );
    }

    @Test
    void generatePotentialAssociation_WithNullPackageType_ShouldHandleGracefully() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(id1);
        String indexName = "test-index";
        Float threshold = 0.8f;

        MasterCatalogRawData rawData1 = createMockRawData(id1, "Product A", null);

        when(masterCatalogRawDataRepository.findById(id1)).thenReturn(Optional.of(rawData1));
        when(vectorDBAdapter.getDuplicationPredictionForProduct(
            anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(Collections.emptyList());

        // Act
        List<List<UUID>> result = service.generatePotentialAssociation(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void createMetaDataForNewProduct_WithEmptyLists_ShouldReturnEmptyList() {
        // Arrange
        List<String> upcList = new ArrayList<>();
        List<String> skuList = new ArrayList<>();
        List<String> nameList = new ArrayList<>();
        List<Boolean> packTypeList = new ArrayList<>();

        // Act
        List<Struct> result = service.createMetaDataForNewProduct(upcList, skuList, nameList, packTypeList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void createMetaDataForNewProduct_WithNullValues_ShouldHandleGracefully() {
        // Arrange
        List<String> upcList = Arrays.asList("123456789", "", "987654321");
        List<String> skuList = Arrays.asList("SKU001", "SKU002", "");
        List<String> nameList = Arrays.asList("Product A", "", "Product C");
        List<Boolean> packTypeList = Arrays.asList(true, true, false);

        // Act
        List<Struct> result = service.createMetaDataForNewProduct(upcList, skuList, nameList, packTypeList);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify that null values are handled properly
        for (Struct metadata : result) {
            assertTrue(metadata.getFieldsMap().containsKey("single"));
            assertTrue(metadata.getFieldsMap().containsKey("upc"));
            assertTrue(metadata.getFieldsMap().containsKey("sku"));
            assertTrue(metadata.getFieldsMap().containsKey("name"));
        }
    }

    @Test
    void createFiltersFromPackType_WithEmptyList_ShouldReturnEmptyList() {
        // Arrange
        List<Boolean> packTypeList = new ArrayList<>();

        // Act
        List<Struct> result = service.createFiltersFromPackType(packTypeList);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @Test
    void generatePotentialDuplicationInBatch_WithZeroThreshold_ShouldCallVectorDBAdapter() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(id1);
        String indexName = "test-index";
        Float threshold = 0.0f;

        MasterCatalogRawData rawData1 = createMockRawData(id1, "Product A", PackageType.INDIVIDUAL);

        when(masterCatalogRawDataRepository.findById(id1)).thenReturn(Optional.of(rawData1));
        when(vectorDBAdapter.getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(Collections.emptyList());

        // Act
        List<List<UUID>> result = service.generatePotentialDuplicationInBatch(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        verify(vectorDBAdapter).getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        );
    }

    @Test
    void generatePotentialDuplicationInBatch_WithMaxThreshold_ShouldCallVectorDBAdapter() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        List<UUID> uuidList = Arrays.asList(id1);
        String indexName = "test-index";
        Float threshold = 1.0f;

        MasterCatalogRawData rawData1 = createMockRawData(id1, "Product A", PackageType.INDIVIDUAL);

        when(masterCatalogRawDataRepository.findById(id1)).thenReturn(Optional.of(rawData1));
        when(vectorDBAdapter.getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        )).thenReturn(Collections.emptyList());

        // Act
        List<List<UUID>> result = service.generatePotentialDuplicationInBatch(uuidList, indexName, threshold);

        // Assert
        assertNotNull(result);
        verify(vectorDBAdapter).getDuplicationPredictionForInBatch(
            anyList(), anyList(), anyList(), anyList(), eq(indexName), eq(threshold), eq(32)
        );
    }

    private MasterCatalogRawData createMockRawData(UUID id, String name, PackageType packageType) {
        MasterCatalogRawData rawData = new MasterCatalogRawData();
        rawData.setId(id);
        rawData.setName(name);
        rawData.setPackageType(packageType);
        rawData.setUpc("123456789");
        rawData.setSkuNumber("SKU001");
        return rawData;
    }
}
