package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsCompletedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsCompletedPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePredictionService;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class GenerateProductsCompletedListenerTest {


    private ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private MasterCatalogGeneratePredictionService masterCatalogGeneratePredictionService;
    private MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private GenerateProductsCompletedListener listener;

    @BeforeEach
    void setUp() {
        applicationEventPublisherProvider = mock(ApplicationEventPublisherProvider.class);
        masterCatalogGeneratePredictionService = mock(MasterCatalogGeneratePredictionService.class);
        masterCatalogBatchJobMapper = mock(MasterCatalogBatchJobMapper.class);
        listener = new GenerateProductsCompletedListener(
            applicationEventPublisherProvider,
            masterCatalogGeneratePredictionService,
            masterCatalogBatchJobMapper
        );
    }

    @Test
    void handleEvent_WithValidData_ShouldProcessSuccessfully() {
        // Given
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();

        MasterCatalogBatchJobDto jobDto = MasterCatalogBatchJobDto.builder()
            .id(jobId)
            .build();

        List<UUID> productIds = Arrays.asList(productId1, productId2);
        List<List<UUID>> duplicateRawIds = Arrays.asList(
            Arrays.asList(productId1, productId2)
        );

        GenerateProductsCompletedPayload payload = GenerateProductsCompletedPayload.builder()
            .batchJobId(jobId)
            .productIds(productIds)
            .payload(jobDto) // important
            .build();

        GenerateProductsCompletedEvent event = new GenerateProductsCompletedEvent(jobDto, payload);

        MasterCatalogBatchJob jobEntity = new MasterCatalogBatchJob();
        jobEntity.setId(jobId);

        when(masterCatalogBatchJobMapper.toEntity(jobDto)).thenReturn(jobEntity);
        when(masterCatalogGeneratePredictionService.generatePotentialAssociation(
            eq(productIds), eq("prod-product-table"), eq(0.93F)
        )).thenReturn(duplicateRawIds);

        // When
        listener.handleEvent(event);

        // Then
        verify(masterCatalogGeneratePredictionService).generatePotentialAssociation(
            eq(productIds), eq("prod-product-table"), eq(0.93F)
        );
        verify(masterCatalogBatchJobMapper).toEntity(jobDto);
        verify(applicationEventPublisherProvider).publishEventCreateAssociationInProgress(jobEntity, duplicateRawIds);
    }

    @Test
    void testFail() {
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();

        MasterCatalogBatchJobDto jobDto = MasterCatalogBatchJobDto.builder()
            .id(jobId)
            .build();

        List<UUID> productIds = Arrays.asList(productId1, productId2);
        List<List<UUID>> duplicateRawIds = Arrays.asList(
            Arrays.asList(productId1, productId2)
        );

        GenerateProductsCompletedPayload payload = GenerateProductsCompletedPayload.builder()
            .batchJobId(jobId)
            .productIds(productIds)
            .payload(jobDto) // important
            .build();

        GenerateProductsCompletedEvent event = new GenerateProductsCompletedEvent(jobDto, payload);

        MasterCatalogBatchJob jobEntity = new MasterCatalogBatchJob();
        jobEntity.setId(jobId);

        when(masterCatalogBatchJobMapper.toEntity(jobDto)).thenReturn(jobEntity);
        when(masterCatalogGeneratePredictionService.generatePotentialAssociation(
            eq(productIds), eq("prod-product-table"), eq(0.93F)
        )).thenReturn(duplicateRawIds);

        when(
            masterCatalogGeneratePredictionService.generatePotentialAssociation(
                any(), any(), any()))
            .thenReturn(any());

        listener.handleEvent(event);

        verify(applicationEventPublisherProvider, never()).generateProductsInProgress(
            any(), any());
    }
}
