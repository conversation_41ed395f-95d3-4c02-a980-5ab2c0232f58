package com.mercaso.data.master_catalog.event.payload.domain;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.event.annotation.BusinessEntityIdentifier;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GenerateProductsInProgressPayload extends
    BusinessEventPayload<MasterCatalogBatchJobDto> {

    @BusinessEntityIdentifier(value = "Generate Production In Progress")
    private UUID batchJobId;
    private List<List<UUID>> rawDataIds;

    @Builder
    public GenerateProductsInProgressPayload(@NotNull UUID batchJobId,
        MasterCatalogBatchJobDto payload, List<List<UUID>> rawDataIds) {
        super(payload);
        this.batchJobId = batchJobId;
        this.rawDataIds = rawDataIds;
    }
}
