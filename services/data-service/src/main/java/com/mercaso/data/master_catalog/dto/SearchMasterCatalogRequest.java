package com.mercaso.data.master_catalog.dto;

import com.mercaso.data.master_catalog.enums.SearchType;
import jakarta.validation.constraints.AssertTrue;
import java.util.stream.Stream;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class SearchMasterCatalogRequest {

    private int page = 1;
    private int pageSize = 20;
    private String storeId;
    private String upc;
    private String description;

    @AssertTrue(message = "The search condition is invalid, only can use one of the following: storeId, upc, description")
    public boolean isSearchConditionValid() {
        long count = Stream.of(storeId, upc, description)
            .filter(s -> s != null && !s.isEmpty())
            .count();
        return count == 1 || count == 0;
    }

    public SearchType getSearchType() {
        if (StringUtils.isNotBlank(upc)) {
            return SearchType.UPC;
        } else if (StringUtils.isNotBlank(storeId)) {
            return SearchType.STORE_ID;
        } else {
            return SearchType.ALL;
        }
    }
}
