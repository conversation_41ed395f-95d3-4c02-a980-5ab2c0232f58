package com.mercaso.data.metrics.adapter.impl;

import com.mercaso.data.metrics.adapter.MetricsS3OperationAdapter;
import com.mercaso.data.metrics.config.DocumentStorageMetricsProperties;
import com.mercaso.data.metrics.dto.DocumentResponseDto;
import com.mercaso.data.metrics.dto.UploadDocumentRequestDto;
import com.mercaso.data.metrics.mapper.MetricsDocumentResponseDtoMapper;
import com.mercaso.data.metrics.mapper.MetricsUploadDocumentRequestDtoMapper;
import com.mercaso.document.operations.operations.DocumentOperations;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MetricsS3OperationAdapterImpl implements MetricsS3OperationAdapter {

    private final DocumentOperations documentOperations;
    private final MetricsDocumentResponseDtoMapper documentResponseDtoMapper;
    private final MetricsUploadDocumentRequestDtoMapper uploadDocumentRequestDtoMapper;
    private final DocumentStorageMetricsProperties documentStorageMetricsProperties;

    @Override
    public DocumentResponseDto upload(UploadDocumentRequestDto uploadDocumentRequestDto) {

        uploadDocumentRequestDto.setDocumentName(
            documentStorageMetricsProperties.getRootFolder() + uploadDocumentRequestDto.getDocumentName());

        return documentResponseDtoMapper.fromDto(documentOperations.uploadDocument(uploadDocumentRequestDtoMapper.toDto(
            uploadDocumentRequestDto)));
    }

    @Override
    public String getSignedUrl(String documentName) {
        return documentOperations.getSignedUrl(documentStorageMetricsProperties.getRootFolder() + documentName);
    }
}
