package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogSquareInventoryChangeRepository extends JpaRepository<MasterCatalogSquareInventoryChange, UUID> {

    List<MasterCatalogSquareInventoryChange> findAllByOccurredAtBetween(Instant startDate, Instant endDate);

    List<MasterCatalogSquareInventoryChange> findAllByOccurredAtBetweenAndMasterCatalogRawDataIdIn(Instant startDate,
        Instant endDate, List<UUID> masterCatalogRawDataIds);

    @Query("SELECT i FROM MasterCatalogSquareInventoryChange i " +
        "JOIN MasterCatalogRawData mcrd ON i.masterCatalogRawDataId = mcrd.id " +
        "WHERE mcrd.storeId = :storeId " +
        "ORDER BY i.updatedAt DESC" +
        " LIMIT 1")
    Optional<MasterCatalogSquareInventoryChange> findTopByStoreIdAndOrderByUpdatedAtDesc(UUID storeId);
}
