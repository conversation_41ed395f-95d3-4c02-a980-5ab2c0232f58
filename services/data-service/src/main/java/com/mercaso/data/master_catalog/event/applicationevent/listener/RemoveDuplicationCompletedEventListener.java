package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationCompletedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationCompletedPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePredictionService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class RemoveDuplicationCompletedEventListener implements
    ApplicationEventListener<RemoveDuplicationCompletedEvent, RemoveDuplicationCompletedPayload> {

    private final ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private final MasterCatalogGeneratePredictionService masterCatalogGeneratePredictionService;
    private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;

    @Override
    public void handleEvent(RemoveDuplicationCompletedEvent event) {
        log.info("REMOVE_DUPLICATION_COMPLETED listener start handle event.");
        try {
            RemoveDuplicationCompletedPayload payload = event.getPayload();
            MasterCatalogBatchJobDto jobDto = payload.getData();
            List<UUID> rawDataIds = payload.getRawDataIds();
            List<List<UUID>> duplicateRawIds = masterCatalogGeneratePredictionService.generatePotentialDuplicationWithProduct(
                rawDataIds, "prod-product-table", 0.93F);
            MasterCatalogBatchJob job = masterCatalogBatchJobMapper.toEntity(
                jobDto);
            applicationEventPublisherProvider.generateProductsInProgress(job,
                duplicateRawIds);

        } catch (Exception e) {
            log.error(
                "REMOVE_DUPLICATION_COMPLETED listener, error processing remove duplication event: {}",
                e.getMessage(), e);
            ;
        }
    }

}