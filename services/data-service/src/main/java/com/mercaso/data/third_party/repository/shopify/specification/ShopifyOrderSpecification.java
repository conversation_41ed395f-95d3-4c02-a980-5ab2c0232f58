package com.mercaso.data.third_party.repository.shopify.specification;

import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrderEntity;
import com.mercaso.data.third_party.enums.shopify.ShopifyOrderStatusEnums;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ShopifyOrderSpecification {

    private static final String FULFILLMENT_STATUS = "fulfillmentStatus";
    private static final String CANCELLED_AT = "cancelledAt";
    private static final String CREATED_AT = "createdAt";
    private static final String TAG_LIST = "tagList";
    private static final String TAG = "tag";
    private static final String PARTIAL = "partial";
    private static final String FULFILLED = "fulfilled";

    public static Specification<ShopifyOrderEntity> hasStatusAtAndTags(ShopifyOrderStatusEnums status, List<String> tags) {

        return (Root<ShopifyOrderEntity> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by status
            if (status != null) {
                addStatusPredicate(predicates, root, builder, status);
            }

            // Filter by tags
            if (!CollectionUtils.isEmpty(tags)) {
                Join<ShopifyOrderEntity, String> tagJoin = root.join(TAG_LIST, JoinType.INNER);

                predicates.add(tagJoin.get(TAG).in(tags));

                query.groupBy(root.get("id"));
                query.having(builder.ge(builder.countDistinct(tagJoin.get(TAG)), tags.size()));
            }

            return builder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private static void addStatusPredicate(List<Predicate> predicates, Root<ShopifyOrderEntity> root, CriteriaBuilder builder,
        ShopifyOrderStatusEnums status) {
        switch (status) {
            case OPEN:
                // cancelledAt is null and fulfillmentStatus is null or is partial
                predicates.add(builder.and(
                    builder.isNull(root.get(CANCELLED_AT)),
                    builder.or(
                        builder.isNull(root.get(FULFILLMENT_STATUS)),
                        builder.equal(root.get(FULFILLMENT_STATUS), PARTIAL)
                    )
                ));
                break;
            case SHIPPED:
                // cancelledAt is null and fulfillmentStatus is fulfilled
                predicates.add(builder.and(
                    builder.isNull(root.get(CANCELLED_AT)),
                    builder.equal(root.get(FULFILLMENT_STATUS), FULFILLED)
                ));
                break;
            case CANCELLED:
                // cancelledAt is not null
                predicates.add(builder.isNotNull(root.get(CANCELLED_AT)));
                break;
            default:
                break;
        }
    }
}