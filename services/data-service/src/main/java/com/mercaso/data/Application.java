package com.mercaso.data;

import java.net.InetAddress;
import java.net.UnknownHostException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.Banner.Mode;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@SpringBootApplication
@Slf4j
@EnableKafka
@EnableJpaAuditing
public class Application extends SpringBootServletInitializer {

    private static final String PORT_PROPERTY = "server.port";

    public static void main(String[] args) {
        try {
            SpringApplication app = new SpringApplication(Application.class);
            Environment env = app.run(args).getEnvironment();
            displayApplicationUrls(env);
        } catch (UnknownHostException e) {
            log.error("Error getting host address", e);
            throw new RuntimeException("Application failed to start", e);
        }
    }

    private static void displayApplicationUrls(Environment env) throws UnknownHostException {
        String port = env.getProperty(PORT_PROPERTY);
        String hostAddress = InetAddress.getLocalHost().getHostAddress();

        log.info("""
                Access URLs:
                ----------------------------------------------------------
                \tService: \tdata-service\t
                \tLocal: \t\thttp://127.0.0.1:{}
                \tExternal: \thttp://{}:{}
                ----------------------------------------------------------
                """,
            port, hostAddress, port);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application
            .sources(Application.class)
            .bannerMode(Mode.OFF);
    }
}