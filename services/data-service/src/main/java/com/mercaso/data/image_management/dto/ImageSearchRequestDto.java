package com.mercaso.data.image_management.dto;

import com.mercaso.data.image_management.enums.ImageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImageSearchRequestDto {

    private String sku;
    private String upc;
    private ImageTypeEnum imageType;
    private String imageAngle;
    private Boolean isPrimary;
    private Boolean isEach;
    private Integer page;
    private Integer pageSize;
    private String sortBy;
    private String sortDirection;
} 