package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrderLineItem;
import lombok.Getter;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface MasterCatalogSquareOrderLineItemRepository
    extends JpaRepository<MasterCatalogSquareOrderLineItem, UUID> {

    @Query("SELECT masterCatalogOrderId FROM MasterCatalogSquareOrderLineItem WHERE masterCatalogRawDataId = :rawDataId")
    List<UUID> findOrderIdsByMasterCatalogRawDataId(UUID rawDataId);

    List<MasterCatalogSquareOrderLineItem> findByMasterCatalogOrderIdIn(List<UUID> orderIds);

    @Query(value = """
        select i.id                         as id,
            i.master_catalog_raw_data_id as master_catalog_raw_data_id,
            i.quantity                   as quantity,
            i.master_catalog_order_id    as master_catalog_order_id,
            o.order_created_at           as order_created_at
        from master_catalog_square_order_line_item i
                 inner join master_catalog_square_order o on i.master_catalog_order_id = o.id
        where i.master_catalog_raw_data_id in (:rawDataIds)
          and o.order_created_at >= :createdAfter
        order by o.created_at
            """, nativeQuery = true)
    List<SquareOrderLineItemInfo> findAllByRawDataIdInAndOrderCreatedAfter(List<UUID> rawDataIds, Instant createdAfter);

    interface SquareOrderLineItemInfo {
        UUID getId();

        UUID getMasterCatalogRawDataId();

        UUID getMasterCatalogOrderId();

        Integer getQuantity();

        Instant getOrderCreatedAt();
    }
}
