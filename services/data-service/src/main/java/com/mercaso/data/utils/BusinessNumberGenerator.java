package com.mercaso.data.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

public class BusinessNumberGenerator {

  private static final String JOB_PRE = "MJ-";
  private static final String TASK_PRE = "MT-";
  private static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();


  private static final DateTimeFormatter DATE_TIME_MILLIS_FORMATTER =
      DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

  private static final DateTimeFormatter DATE_TIME_FORMATTER =
      DateTimeFormatter.ofPattern("yyyyMMdd");

  public static String generateJobNumber() {
    return JOB_PRE + LocalDateTime.now(DEFAULT_ZONE).format(DATE_TIME_FORMATTER);
  }

  public static String generateTaskNumber() {
    int random = ThreadLocalRandom.current().nextInt(100, 1000);
    return TASK_PRE + LocalDateTime.now(DEFAULT_ZONE).format(DATE_TIME_MILLIS_FORMATTER) + random;
  }

}
