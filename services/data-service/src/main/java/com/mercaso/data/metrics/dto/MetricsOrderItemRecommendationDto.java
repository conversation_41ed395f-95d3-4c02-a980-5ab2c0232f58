package com.mercaso.data.metrics.dto;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetricsOrderItemRecommendationDto {

    private int rank;
    private String sku;
    private Boolean stockStatus;
    private String name;
    private String department;
    private LocalDateTime lastOrderedDate;
    private String type;

}
