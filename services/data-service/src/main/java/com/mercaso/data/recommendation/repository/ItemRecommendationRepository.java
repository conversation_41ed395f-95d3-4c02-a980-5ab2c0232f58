package com.mercaso.data.recommendation.repository;

import com.mercaso.data.recommendation.entity.ItemRecommendation;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ItemRecommendationRepository extends JpaRepository<ItemRecommendation, UUID>, JpaSpecificationExecutor<ItemRecommendation> {

  @Query("SELECT DISTINCT ir.departmentId, ir.department FROM ItemRecommendation ir WHERE ir.storeId = :storeId AND ir.version = :version AND ir.department IS NOT NULL AND ir.departmentId IS NOT NULL ORDER BY ir.department")
  List<Object[]> findDistinctDepartmentsWithIdByStoreIdAndVersion(@Param("storeId") String storeId, @Param("version") String version);
}
