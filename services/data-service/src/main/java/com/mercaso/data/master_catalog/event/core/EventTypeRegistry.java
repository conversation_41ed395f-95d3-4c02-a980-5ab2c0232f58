package com.mercaso.data.master_catalog.event.core;

import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventType;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Event type registry, manages all event types in the system
 * Provides registration, lookup and other functions for event types
 */
@Slf4j
@Component
public class EventTypeRegistry {

    private final Map<String, ApplicationEventType<?, ?>> nameRegistry = new ConcurrentHashMap<>();
    private final Map<Class<?>, ApplicationEventType<?, ?>> payloadClassRegistry = new ConcurrentHashMap<>();
    private final Map<Class<?>, ApplicationEventType<?, ?>> eventClassRegistry = new ConcurrentHashMap<>();

    /**
     * Register event type
     */
    public void register(ApplicationEventType<?, ?> applicationEventType) {
        nameRegistry.put(applicationEventType.getName(), applicationEventType);
        payloadClassRegistry.put(applicationEventType.getPayloadClass(), applicationEventType);
        eventClassRegistry.put(applicationEventType.getApplicationEventClass(), applicationEventType);
        log.debug("Registered event type: {}", applicationEventType.getName());
    }

    /**
     * Find event type by name
     */
    public Optional<ApplicationEventType<?, ?>> findByName(String name) {
        return Optional.ofNullable(nameRegistry.get(name));
    }

    /**
     * Find event type by Payload type
     */
    public Optional<ApplicationEventType<?, ?>> findByPayloadClass(Class<?> payloadClass) {
        return Optional.ofNullable(payloadClassRegistry.get(payloadClass));
    }

    /**
     * Find event type by event type
     */
    public Optional<ApplicationEventType<?, ?>> findByEventClass(Class<?> eventClass) {
        return Optional.ofNullable(eventClassRegistry.get(eventClass));
    }

    /**
     * Get all registered event types
     */
    public Collection<ApplicationEventType<?, ?>> getAllEventTypes() {
        return Collections.unmodifiableCollection(nameRegistry.values());
    }
} 