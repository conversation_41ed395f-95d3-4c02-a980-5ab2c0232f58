package com.mercaso.data.metrics.repository;

import com.mercaso.data.metrics.entity.StoreProfilingStoreContinuousMetricsEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface StoreProfilingStoreContinuousMetricsRepository extends JpaRepository<StoreProfilingStoreContinuousMetricsEntity, Long>,
    JpaSpecificationExecutor<StoreProfilingStoreContinuousMetricsEntity> {

    List<StoreProfilingStoreContinuousMetricsEntity> findByStoreIdAndMetricName(String storeId, String metricName);

    List<StoreProfilingStoreContinuousMetricsEntity> findByStoreIdAndMetricCategoryIn(String storeId, List<String> metricCategory);

    @Query("SELECT DISTINCT sm.metricName FROM StoreProfilingStoreContinuousMetricsEntity sm WHERE sm.storeId = :storeId ORDER BY sm.metricName")
    List<String> findDistinctMetricNamesByStoreId(@Param("storeId") String storeId);


} 