package com.mercaso.data.recommendation.repository;

import com.mercaso.data.recommendation.dto.SearchPriceDto;
import com.mercaso.data.recommendation.entity.PriceRecommendation;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface PriceRecommendationRepository extends JpaRepository<PriceRecommendation, UUID>,
    JpaSpecificationExecutor<PriceRecommendation> {

  @Query("SELECT p FROM PriceRecommendation p "
      + "where p.storeId = :#{#search.storeId} "
      + "and (:#{#search.latestOrder} = false or p.latestOrder = :#{#search.latestOrder}) "
      + "and (:#{#search.departmentId} is null or p.departmentId = :#{#search.departmentId}) "
      + "and (:#{#search.searchText} is null or p.name ilike CONCAT('%', :#{#search.searchText}, '%'))")
  Page<PriceRecommendation> search(SearchPriceDto search, Pageable pageable);

  List<PriceRecommendation> findByStoreId(String storeId);

  List<PriceRecommendation> findByStoreIdAndUpcIn(String storeId, List<String> upcList);

  List<PriceRecommendation> findByStoreIdAndEverGreen(@Size(max = 255) String storeId, Boolean everGreen);
}
