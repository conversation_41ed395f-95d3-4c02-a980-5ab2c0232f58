package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.external.DocumentResponseDto;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.springframework.web.multipart.MultipartFile;

public interface MasterCatalogImageService {


    DocumentResponseDto uploadImage(MultipartFile file);

    Map<UUID, List<String>> getImagesPathByRawDataIds(List<UUID> rawDataIds);
}