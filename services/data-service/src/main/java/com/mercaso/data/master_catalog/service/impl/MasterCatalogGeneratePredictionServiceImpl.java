package com.mercaso.data.master_catalog.service.impl;

import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.mercaso.data.master_catalog.adaptor.VectorDBAdapter;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePredictionService;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class MasterCatalogGeneratePredictionServiceImpl implements
    MasterCatalogGeneratePredictionService {

    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private final VectorDBAdapter vectorDBAdapter;

    public List<List<UUID>> generatePotentialDuplicationInBatch(List<UUID> uuidList,
        String indexName,
        Float threshold) {
        List<MasterCatalogRawData> masterCatalogRawDataList = fetchMasterCatalogRawData(
            uuidList);
        List<String> idList = fetchIdsGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getId);
        List<String> nameList = fetchNamesGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getName);
        List<Boolean> packTypeList = fetchPackTypeGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getPackageType);
        List<Struct> metaDataList = createFiltersFromPackType(packTypeList);
        log.info("Master Catalog Remove Duplication In Batch Start...");
        return vectorDBAdapter.getDuplicationPredictionForInBatch(idList, nameList,
            packTypeList, metaDataList, indexName, threshold, 32);
    }

    public List<List<UUID>> generatePotentialDuplicationWithProduct(List<UUID> uuidList,
        String indexName,
        Float threshold) {
        List<MasterCatalogRawData> masterCatalogRawDataList = fetchMasterCatalogRawData(
            uuidList);
        List<String> idList = fetchIdsGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getId);
        List<String> nameList = fetchNamesGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getName);
        List<Boolean> packTypeList = fetchPackTypeGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getPackageType);
        log.info("Master Catalog Remove Duplication With Product Start...");
        return vectorDBAdapter.getDuplicationPredictionForProduct(idList, nameList,
            packTypeList, indexName, threshold, 32);

    }

    public void pushProduct(List<MasterCatalogRawData> productList,
        String indexName) {
        List<String> idList = fetchIdsGeneric(productList,
            MasterCatalogRawData::getId);
        List<String> nameList = fetchNamesGeneric(productList, MasterCatalogRawData::getName);
        List<String> upcList = fetchUpcsGeneric(productList, MasterCatalogRawData::getUpc);
        List<String> skuList = fetchSkusGeneric(productList, MasterCatalogRawData::getSkuNumber);
        List<Boolean> packTypeList = fetchPackTypeGeneric(productList,
            MasterCatalogRawData::getPackageType);
        List<Struct> metaDataList = createMetaDataForNewProduct(upcList, skuList, nameList,
            packTypeList);
        log.info("Master Catalog Upsert To Product Start...");
        vectorDBAdapter.upsertToProductTable(idList, nameList, metaDataList,
            indexName, 32);
    }

    @Override
    public List<List<UUID>> generatePotentialAssociation(List<UUID> uuidList, String indexName, Float threshold) {

        List<List<MasterCatalogRawData>> rawDataListSeperatedByPack = separateByPackType(uuidList);
        List<MasterCatalogRawData> individualItems = rawDataListSeperatedByPack.getFirst();
        List<MasterCatalogRawData> packItems = rawDataListSeperatedByPack.getLast();
        log.info("Master Catalog Create Association Start...");

        List<List<UUID>> potentialAssociationForPackItems = handleGetPotentialAssociationByPackType(packItems,
            indexName,
            threshold);
        pushProduct(packItems, indexName);

        List<List<UUID>> potentialAssociationForIndividualItems = handleGetPotentialAssociationByPackType(individualItems,
            indexName,
            threshold);
        pushProduct(individualItems, indexName);

        return Stream.concat(potentialAssociationForPackItems.stream(), potentialAssociationForIndividualItems.stream()).toList();
    }

    private List<List<UUID>> handleGetPotentialAssociationByPackType(
        List<MasterCatalogRawData> masterCatalogRawDataList, String indexName, Float threshold) {
        List<Boolean> itemsTypeList = fetchPackTypeGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getPackageType);
        List<Boolean> flippedItemsTypeList = itemsTypeList.stream().map(bool -> !bool).toList();

        List<String> idList = fetchIdsGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getId);
        List<String> nameList = fetchNamesGeneric(masterCatalogRawDataList,
            MasterCatalogRawData::getName);

        return vectorDBAdapter.getAssociationPrediction(idList,
            nameList,
            flippedItemsTypeList,
            indexName,
            threshold,
            32);
    }

    private List<List<MasterCatalogRawData>> separateByPackType(List<UUID> uuidList) {
        List<MasterCatalogRawData> masterCatalogRawDataList = fetchMasterCatalogRawData(uuidList);

        List<MasterCatalogRawData> individualItems = new ArrayList<>();
        List<MasterCatalogRawData> packItems = new ArrayList<>();

        for (MasterCatalogRawData rawData : masterCatalogRawDataList) {
            if (rawData.getPackageType() == PackageType.INDIVIDUAL) {
                individualItems.add(rawData);
            } else {
                packItems.add(rawData);
            }
        }

        return List.of(individualItems, packItems);
    }

    private List<MasterCatalogRawData> fetchMasterCatalogRawData(List<UUID> idLists) {
        return idLists.stream()
            .map(masterCatalogRawDataRepository::findById).flatMap(Optional::stream).toList();
    }

    private <T> List<String> fetchIdsGeneric(List<T> items, Function<T, UUID> idExtractor) {
        return items.stream()
            .map(idExtractor)
            .map(UUID::toString)
            .collect(Collectors.toList());
    }

    private <T> List<String> fetchNamesGeneric(List<T> items, Function<T, String> nameExtractor) {
        return items.stream()
            .map(nameExtractor)
            .collect(Collectors.toList());
    }

    /**
     * Generic method to fetch UPC values.
     */
    private <T> List<String> fetchUpcsGeneric(List<T> items, Function<T, String> upcExtractor) {
        return items.stream()
            .map(item -> Objects.toString(upcExtractor.apply(item), ""))
            .collect(Collectors.toList());
    }

    /**
     * Generic method to fetch SKU values.
     */
    private <T> List<String> fetchSkusGeneric(List<T> items, Function<T, String> skuExtractor) {
        return items.stream()
            .map(item -> Objects.toString(skuExtractor.apply(item), ""))
            .collect(Collectors.toList());
    }

    /**
     * Generic method to fetch and convert package types to a boolean value. Here we assume that if
     * the package type equals PackageType.INDIVIDUAL, it's true.
     */
    private <T> List<Boolean> fetchPackTypeGeneric(List<T> items,
        Function<T, PackageType> packageTypeExtractor) {
        return items.stream()
            .map(item -> PackageType.INDIVIDUAL.equals(packageTypeExtractor.apply(item)))
            .collect(Collectors.toList());
    }

    private List<Boolean> fetchSingleProduct(
        List<MasterCatalogProduct> masterCatalogProductList) {
        return masterCatalogProductList.stream()
            .map(MasterCatalogProduct::getSingleProduct).toList();
    }

    List<Struct> createFiltersFromPackType(List<Boolean> packTypeList) {
        return packTypeList.stream()
            .map(data -> Struct.newBuilder()
                .putFields("single", Value.newBuilder().setBoolValue(data).build())
                .build())
            .collect(Collectors.toList());
    }

    List<Struct> createMetaDataForNewProduct(List<String> upcList, List<String> skuList,
        List<String> nameList, List<Boolean> packTypeList) {
        List<Struct> metaDataList = new ArrayList<>();
        for (int i = 0; i < upcList.size(); i++) {
            Boolean isSingle =
                (packTypeList.get(i) == null || packTypeList.get(i)) ? Boolean.TRUE : Boolean.FALSE;
            metaDataList.add(
                Struct.newBuilder()
                    .putFields("single",
                        Value.newBuilder().setBoolValue(isSingle).build())
                    .putFields("upc", Value.newBuilder().setStringValue(upcList.get(i)).build())
                    .putFields("sku", Value.newBuilder().setStringValue(skuList.get(i)).build())
                    .putFields("name",
                        Value.newBuilder().setStringValue(nameList.get(i)).build())
                    .build()
            );

        }
        return metaDataList;
    }
}
