package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

public interface MasterCatalogTaskService {

  void assign(UUID id, String assignTo);

  List<MasterCatalogTaskDto> createTasks(UUID jobId, MasterCatalogTaskType type, Integer taskCount);

  Page<MasterCatalogTaskDto> search(UUID jobId, String jobNumber,  String taskNumber, String assignBy, String assignTo, MasterCatalogTaskStatus status, MasterCatalogTaskType type, PageRequest pageRequest);

  MasterCatalogTaskDto getDetailsById(UUID id);

  MasterCatalogTask validateUserPermission(UUID taskId, String action);

  void batchAssign(List<UUID> ids, String assignTo);
}
