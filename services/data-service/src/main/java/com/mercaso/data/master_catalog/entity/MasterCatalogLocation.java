package com.mercaso.data.master_catalog.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.data.master_catalog.converter.JsonNodeConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/**
 * Table to store master_catalog_locations
 */
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_locations")
public class MasterCatalogLocation extends BaseEntity {


    /**
     * Store id of the location
     */
    @Column(name = "store_id")
    private UUID storeId;

    /**
     * Name of the location
     */
    @Size(max = 255)
    @Column(name = "name")
    private String name;

    /**
     * Address line 1 of the location
     */
    @Size(max = 255)
    @Column(name = "address_line_1")
    private String addressLine1;

    /**
     * Postal code of the location
     */
    @Size(max = 255)
    @Column(name = "postal_code")
    private String postalCode;

    /**
     * City of the location
     */
    @Size(max = 255)
    @Column(name = "city")
    private String city;

    /**
     * Country of the location
     */
    @Size(max = 255)
    @Column(name = "country")
    private String country;

    /**
     * Latitude of the location
     */
    @Column(name = "latitude")
    private Double latitude;

    /**
     * Longitude of the location
     */
    @Column(name = "longitude")
    private Double longitude;

    /**
     * Metadata of the location
     */
    @Column(name = "metadata", columnDefinition = "jsonb")
    @Convert(converter = JsonNodeConverter.class)
    @JdbcTypeCode(SqlTypes.JSON)
    private JsonNode metadata;

}
