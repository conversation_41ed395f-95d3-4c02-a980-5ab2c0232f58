package com.mercaso.data.master_catalog.entity;

import com.mercaso.data.master_catalog.enums.PackageType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_raw_data")
public class MasterCatalogRawData extends BaseEntity {

    @NotNull
    @Column(name = "store_id", nullable = false)
    private UUID storeId;

    @Size(max = 64)
    @Column(name = "upc", length = 64)
    private String upc;

    @Size(max = 255)
    @NotNull
    @Column(name = "name", nullable = false)
    private String name;

    @NotNull
    @Column(name = "description", nullable = false, length = Integer.MAX_VALUE)
    private String description;

    @Size(max = 64)
    @Column(name = "brand", length = 64)
    private String brand;

    @Size(max = 255)
    @Column(name = "sku_number")
    private String skuNumber;

    @Size(max = 64)
    @Column(name = "department", length = 64)
    private String department;

    @Size(max = 64)
    @Column(name = "category", length = 64)
    private String category;

    @Size(max = 64)
    @Column(name = "sub_category", length = 64)
    private String subCategory;

    @Size(max = 64)
    @Column(name = "clazz", length = 64)
    private String clazz;

    @Size(max = 64)
    @Column(name = "primary_vendor", length = 64)
    private String primaryVendor;

    @Size(max = 255)
    @Column(name = "source_id")
    private String sourceId;

    @Size(max = 64)
    @Column(name = "source_from")
    private String sourceFrom;

    /**
     * {@link com.mercaso.data.master_catalog.enums.RawDataStatus}
     */
    @Size(max = 16)
    @Column(name = "status", length = 16)
    private String status;

    @Column(precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "package_type")
    @Enumerated(EnumType.STRING)
    private PackageType packageType;

    @Column(name = "package_size")
    private Integer packageSize;

    @Column(name = "source_status")
    private String sourceStatus;

    private transient String variationId;

    @Column(name = "completed_at")
    private Instant completedAt;

    private transient List<MasterCatalogImage> images;
}
