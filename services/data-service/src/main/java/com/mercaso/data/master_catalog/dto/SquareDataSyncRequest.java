package com.mercaso.data.master_catalog.dto;


import com.mercaso.data.master_catalog.enums.square.SyncEntity;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
@ToString
public class SquareDataSyncRequest {

    /**
     * If the store id is not provided, the data will be synced for all the stores
     * The store id
     */
    private UUID storeId;
    private Long startTimestamp;
    private SyncEntity syncEntity;
}
