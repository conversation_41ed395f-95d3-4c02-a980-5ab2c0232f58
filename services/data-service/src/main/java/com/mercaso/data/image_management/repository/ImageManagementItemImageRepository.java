package com.mercaso.data.image_management.repository;

import com.mercaso.data.image_management.entity.ImageManagementItemImage;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

public interface ImageManagementItemImageRepository extends JpaRepository<ImageManagementItemImage, UUID>  {

    @Query("SELECT i FROM ImageManagementItemImage i WHERE " +
           "(:sku IS NULL OR i.sku = :sku) AND " +
           "(:upc IS NULL OR i.upc = :upc) AND " +
           "(:imageType IS NULL OR i.imageType = :imageType) AND " +
           "(:imageAngle IS NULL OR i.imageAngel = :imageAngle) AND " +
           "(:isPrimary IS NULL OR i.isPrimary = :isPrimary) AND " +
           "(:isEach IS NULL OR i.eachFlag = :isEach)")
    Page<ImageManagementItemImage> searchImages(
            @Param("sku") String sku,
            @Param("upc") String upc,
            @Param("imageType") String imageType,
            @Param("imageAngle") String imageAngle,
            @Param("isPrimary") Boolean isPrimary,
            @Param("isEach") Boolean isEach,
            Pageable pageable);
}
