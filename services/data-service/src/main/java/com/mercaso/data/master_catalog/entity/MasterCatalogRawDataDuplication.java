package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_raw_data_duplication")
public class MasterCatalogRawDataDuplication extends BaseEntity {

    @Size(max = 64)
    @NotNull
    @Column(name = "upc", nullable = false, length = 64)
    private String upc;

    @NotNull
    @Column(name = "duplication_group", nullable = false)
    private UUID duplicationGroup;

    @Column(name = "created_by")
    private String createdBy;
}
