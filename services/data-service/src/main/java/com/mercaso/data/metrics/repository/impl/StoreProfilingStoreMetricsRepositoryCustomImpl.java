package com.mercaso.data.metrics.repository.impl;

import com.mercaso.data.metrics.entity.StoreProfilingStoreMetricsEntity;
import com.mercaso.data.metrics.repository.StoreProfilingStoreMetricsRepositoryCustom;
import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class StoreProfilingStoreMetricsRepositoryCustomImpl implements StoreProfilingStoreMetricsRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    private static final String STORE_ID = "storeId";

    @Override
    public Page<String> findDistinctStoreIds(Specification<StoreProfilingStoreMetricsEntity> spec, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        CriteriaQuery<String> dataCq = cb.createQuery(String.class);
        Root<StoreProfilingStoreMetricsEntity> dataRoot = dataCq.from(StoreProfilingStoreMetricsEntity.class);

        Predicate dataPredicate = spec.toPredicate(dataRoot, dataCq, cb);

        Subquery<String> inSub = dataCq.subquery(String.class);
        Root<StoreProfilingStoreInfoEntity> infoRoot = inSub.from(StoreProfilingStoreInfoEntity.class);
        inSub.select(infoRoot.get(STORE_ID));

        Predicate storeExistsPredicate = dataRoot.get(STORE_ID).in(inSub);
        
        if (dataPredicate != null) {
            dataCq.where(cb.and(dataPredicate, storeExistsPredicate));
        } else {
            dataCq.where(storeExistsPredicate);
        }

        dataCq.select(dataRoot.get(STORE_ID)).distinct(true);

        Sort sort = pageable.getSort();
        if (sort.isSorted()) {
            List<jakarta.persistence.criteria.Order> jpaOrders = new ArrayList<>();
            for (Sort.Order order : sort) {
                if (STORE_ID.equalsIgnoreCase(order.getProperty())) {
                    if (order.isAscending()) {
                        jpaOrders.add(cb.asc(dataRoot.get(STORE_ID)));
                    } else {
                        jpaOrders.add(cb.desc(dataRoot.get(STORE_ID)));
                    }
                }
            }
            if (!jpaOrders.isEmpty()) {
                dataCq.orderBy(jpaOrders);
            }
        } else {
            dataCq.orderBy(cb.asc(dataRoot.get(STORE_ID)));
        }


        TypedQuery<String> typedQuery = entityManager.createQuery(dataCq);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<String> resultList = typedQuery.getResultList();

        CriteriaQuery<Long> countCq = cb.createQuery(Long.class);
        Root<StoreProfilingStoreMetricsEntity> countRoot = countCq.from(StoreProfilingStoreMetricsEntity.class);

        Predicate countPredicate = spec.toPredicate(countRoot, countCq, cb);
        
        Subquery<String> countInSub = countCq.subquery(String.class);
        Root<StoreProfilingStoreInfoEntity> countInfoRoot = countInSub.from(StoreProfilingStoreInfoEntity.class);
        countInSub.select(countInfoRoot.get(STORE_ID));

        Predicate countStoreExistsPredicate = countRoot.get(STORE_ID).in(countInSub);
        
        if (countPredicate != null) {
            countCq.where(cb.and(countPredicate, countStoreExistsPredicate));
        } else {
            countCq.where(countStoreExistsPredicate);
        }

        countCq.select(cb.countDistinct(countRoot.get(STORE_ID)));
        Long total = entityManager.createQuery(countCq).getSingleResult();

        return new PageImpl<>(resultList, pageable, total);
    }
}