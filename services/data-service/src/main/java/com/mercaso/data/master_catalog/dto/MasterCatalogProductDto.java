package com.mercaso.data.master_catalog.dto;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogProduct}
 */
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogProductDto extends BaseDto {

    private UUID id;
    private Instant createdAt;
    private Instant updatedAt;
    private String upc;
    private String name;
    private String description;
    private String brand;
    private String skuNumber;
    private String department;
    private String category;
    private String subCategory;
    private String clazz;
    private String primaryVendor;
    private UUID masterCatalogRawDataId;
    private Boolean singleProduct;
    private List<String> images;
}
