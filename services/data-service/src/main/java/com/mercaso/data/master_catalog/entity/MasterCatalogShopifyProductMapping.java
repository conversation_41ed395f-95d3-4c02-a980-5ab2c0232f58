package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "master_catalog_shopify_product_mapping")
@Builder
public class MasterCatalogShopifyProductMapping extends BaseEntity {

    @Column(name = "sku_number", nullable = false, length = 32)
    private String skuNumber;

    @Column(name = "product_id", nullable = false)
    private Integer productId;
}
