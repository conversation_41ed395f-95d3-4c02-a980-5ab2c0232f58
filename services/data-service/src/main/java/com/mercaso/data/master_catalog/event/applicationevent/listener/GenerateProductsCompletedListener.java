package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsCompletedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsCompletedPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePredictionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
public class GenerateProductsCompletedListener implements
    ApplicationEventListener<GenerateProductsCompletedEvent, GenerateProductsCompletedPayload> {

    private final ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private final MasterCatalogGeneratePredictionService masterCatalogGeneratePredictionService;
    private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;

    @Override
    public void handleEvent(GenerateProductsCompletedEvent event) {
        log.info("GENERATE_PRODUCTS_COMPLETED listener start handle event.");
        try {
            GenerateProductsCompletedPayload payload = event.getPayload();
            MasterCatalogBatchJobDto jobDto = payload.getData();
            List<UUID> productIds = payload.getProductIds();
            List<List<UUID>> duplicateRawIds = masterCatalogGeneratePredictionService.generatePotentialAssociation(
                productIds, "prod-product-table", 0.93F);
            MasterCatalogBatchJob job = masterCatalogBatchJobMapper.toEntity(
                jobDto);
            applicationEventPublisherProvider.publishEventCreateAssociationInProgress(job,
                duplicateRawIds);

        } catch (Exception e) {
            log.error(
                "GENERATE_PRODUCTS_COMPLETED listener, error processing association event: {}",
                e.getMessage(), e);
            ;
        }
    }
}
