package com.mercaso.data.metrics.mapper;

import com.mercaso.data.metrics.dto.StoreProfilingStoreContinuousMetricsDto;
import com.mercaso.data.metrics.entity.StoreProfilingStoreContinuousMetricsEntity;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface StoreProfilingStoreContinuousMetricsDtoMapper {

    StoreProfilingStoreContinuousMetricsDto toDto(StoreProfilingStoreContinuousMetricsEntity entity);

    StoreProfilingStoreContinuousMetricsEntity toEntity(StoreProfilingStoreContinuousMetricsDto dto);

    List<StoreProfilingStoreContinuousMetricsDto> toDtoList(List<StoreProfilingStoreContinuousMetricsEntity> entities);
} 