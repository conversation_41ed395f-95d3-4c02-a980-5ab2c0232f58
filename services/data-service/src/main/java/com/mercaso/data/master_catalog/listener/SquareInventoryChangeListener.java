package com.mercaso.data.master_catalog.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareInventoryChangeDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@RequiredArgsConstructor
public class SquareInventoryChangeListener extends AnalysisEventListener<SquareInventoryChangeDto> {

    private final UUID storeId;
    private final SquareApiAdapter squareApiAdapter;
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository;
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private final MasterCatalogLocationRepository masterCatalogLocationRepository;
    private final List<SquareInventoryChangeDto> changes = new ArrayList<>();
    private static final int BATCH_SIZE = 100;
    public static final String META_DATA_KEY_LOCATION_ID = "locationId";
    private static final UUID MERCASO_STORE_ID = UUID.fromString("b350491e-e238-4caf-b328-cf2438e057d8");
    private static final String ACTIVE_SOURCE_STATUS = "ACTIVE";

    @Override
    public void invoke(SquareInventoryChangeDto data, AnalysisContext context) {
        changes.add(data);

        if (changes.size() >= BATCH_SIZE) {
            processBatchChanges();
            changes.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!changes.isEmpty()) {
            processBatchChanges();
        }
        log.info("Finished processing all inventory changes");
    }

    private void processBatchChanges() {
        if (CollectionUtils.isEmpty(changes)) {
            return;
        }

        log.info("processBatchChanges for storeId: {}, changes: {}", storeId, changes);

        try {
            // 1. Get SKU list
            List<String> skus = changes.stream()
                .map(SquareInventoryChangeDto::getSku)
                .toList();

            log.info("processBatchChanges for storeId: {} and skus: {}", storeId, skus);

            // 2. Get SKU to UPC mapping
            List<MasterCatalogRawData> skuRawDataList = masterCatalogRawDataRepository
                .findAllByStoreIdAndSourceStatusAndSkuNumberIn(MERCASO_STORE_ID, ACTIVE_SOURCE_STATUS ,skus);

            Map<String, List<String>> skuToUpc = skuRawDataList.stream()
                .filter(data -> {
                    if (data.getUpc() == null) {
                        log.warn("processBatchChanges SKU {} does not have a UPC", data.getSkuNumber());
                    }
                    return data.getUpc() != null && data.getSkuNumber() != null;
                })
                .collect(Collectors.groupingBy(
                    MasterCatalogRawData::getSkuNumber,
                    Collectors.mapping(MasterCatalogRawData::getUpc, Collectors.toList())
                ));

            log.info("processBatchChanges SKU to UPC mapping: {}", skuToUpc);

            // 3. Get UPC list
            List<String> upcs = new ArrayList<>(skuToUpc.values().stream().flatMap(Collection::stream).toList());

            // 4. Get UPC raw data
            List<MasterCatalogRawData> upcRawDataList = masterCatalogRawDataRepository
                .findAllByStoreIdAndUpcIn(storeId, upcs);

            // 5. Get raw data IDs
            List<UUID> rawDataIds = upcRawDataList.stream()
                .map(MasterCatalogRawData::getId)
                .toList();

            // 6. Get variation mappings
            List<MasterCatalogSquareVariationMapping> mappings = variationMappingRepository
                .findAllByMasterCatalogRawDataIdIn(rawDataIds);

            log.info("processBatchChanges Variation mappings: {}", mappings);
            if (mappings.isEmpty()) {
                log.warn("processBatchChanges No variation mappings found for storeId: {}", storeId);
                return;
            }

            // 7. Create UPC to variation ID mapping
            Map<String, String> upcToVariationId = new HashMap<>();
            for (MasterCatalogRawData rawData : upcRawDataList) {
                mappings.stream()
                    .filter(mapping -> mapping.getMasterCatalogRawDataId().equals(rawData.getId()))
                    .peek(m -> log.info("processBatchChanges upcToVariationId upc: {}, Mapping: {}", rawData.getUpc(), m))
                    .findFirst()
                    .ifPresent(mapping -> upcToVariationId.put(rawData.getUpc(), mapping.getVariationId()));
            }

            log.info("processBatchChanges UPC to Variation ID mapping: {}", upcToVariationId);

            // 8. Get locations
            List<MasterCatalogLocation> storeLocations = masterCatalogLocationRepository.findAllByStoreId(storeId);
            List<String> locationIds = storeLocations.stream()
                .map(l -> l.getMetadata().get(META_DATA_KEY_LOCATION_ID).asText())
                .filter(Objects::nonNull)
                .distinct()
                .toList();

            log.info("processBatchChanges Location IDs: {}", locationIds);
            if (locationIds.isEmpty()) {
                log.warn("processBatchChanges No locations found for storeId: {}", storeId);
                return;
            }

            Map<String, String> skuToUpcFinal = skuToUpc.entrySet().stream()
                .filter(entry -> entry.getValue().stream().anyMatch(upcToVariationId::containsKey))
                .collect(Collectors.toMap(Entry::getKey,
                    entry -> {
                        log.info("processBatchChanges sku:{} upcs: {}", entry.getKey(), entry.getValue());
                        return entry.getValue()
                            .stream()
                            .filter(upcToVariationId::containsKey)
                            .findFirst()
                            .get();
                    }));
            log.info("processBatchChanges skuToUpcFinal: {}", skuToUpcFinal);

            // 9. Process changes
            Map<String, Integer> batchChanges = new HashMap<>();
            for (SquareInventoryChangeDto change : changes) {
                String sku = change.getSku();
                String upc = skuToUpcFinal.get(sku);
                String variationId = upc != null ? upcToVariationId.get(upc) : null;
                log.info("processBatchChanges SKU: {}, UPC: {}, Variation ID: {}", sku, upc, variationId);
                if (variationId != null) {
                    batchChanges.put(variationId, change.getChange());
                }
            }

            if (batchChanges.isEmpty()) {
                log.warn("No valid changes found for storeId: {}", storeId);
                return;
            }

            squareApiAdapter.batchChangeInventory(storeId, locationIds, batchChanges);

        } catch (Exception e) {
            log.error("Failed to process batch inventory changes: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process batch inventory changes", e);
        }
    }
} 