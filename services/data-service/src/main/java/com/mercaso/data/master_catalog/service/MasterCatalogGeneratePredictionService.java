package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import java.util.List;
import java.util.UUID;

public interface MasterCatalogGeneratePredictionService {

    public List<List<UUID>> generatePotentialDuplicationInBatch(List<UUID> uuidList,
        String indexName,
        Float threshold);

    public List<List<UUID>> generatePotentialDuplicationWithProduct(List<UUID> uuidList,
        String indexName,
        Float threshold);

    public void pushProduct(List<MasterCatalogRawData> productList,
        String indexName);

    public List<List<UUID>> generatePotentialAssociation(List<UUID> uuidList,
        String indexName,
        Float threshold);
}
