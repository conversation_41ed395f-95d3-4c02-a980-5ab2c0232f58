package com.mercaso.data.master_catalog.dto.dashboard;

import com.mercaso.data.master_catalog.dto.BaseDto;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogSuqareInventoryDailyMetric}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogSuqareInventoryDailyMetricDto extends BaseDto {

    private UUID id;
    private Instant createdAt;
    private Instant updatedAt;
    private UUID masterCatalogRawDataId;
    private UUID storeId;
    private Long quantity;
    private Long inStockQuantity;
    private Long outStockQuantity;
    private Instant date;
}
