package com.mercaso.data.master_catalog.event.core;

import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventType;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import org.springframework.stereotype.Component;

/**
 * Builder for creating event type definitions
 */
@Component
public class EventTypeBuilder {

    /**
     * Start building a new event type
     *
     * @param name Event name
     * @return Builder instance
     */
    public <P extends BusinessEventPayload<?>> EventTypeDefinitionBuilder<P> newEvent(String name) {
        return new EventTypeDefinitionBuilder<>(name);
    }

    public static class EventTypeDefinitionBuilder<P extends BusinessEventPayload<?>> {

        private final String name;
        private Class<P> payloadClass;

        private EventTypeDefinitionBuilder(String name) {
            this.name = name;
        }

        /**
         * Set the payload class for this event
         */
        public <T extends P> EventTypeDefinitionBuilder<T> withPayload(Class<T> payloadClass) {
            EventTypeDefinitionBuilder<T> builder = new EventTypeDefinitionBuilder<>(name);
            builder.payloadClass = payloadClass;
            return builder;
        }

        /**
         * Build the event type with default configuration
         */
        public <E extends BaseApplicationEvent<P>> ApplicationEventType<P, E> build(Class<E> eventClass) {
            return new DefaultApplicationEventType<>(name, payloadClass, eventClass);
        }

        /**
         * Build the event type with custom configuration
         */
        public <E extends BaseApplicationEvent<P>> ApplicationEventType<P, E> buildWithConfig(
            Class<E> eventClass,
            boolean storeEvent,
            boolean publishLocal) {

            return new ConfigurableApplicationEventType<>(
                name, payloadClass, eventClass,
                storeEvent, publishLocal);
        }
    }

    /**
     * Default implementation of EventType
     */
    private static class DefaultApplicationEventType<P extends BusinessEventPayload<?>, E extends BaseApplicationEvent<P>>
        extends AbstractApplicationEventType<P, E> {

        public DefaultApplicationEventType(String name, Class<P> payloadClass, Class<E> eventClass) {
            super(name, payloadClass, eventClass);
        }
    }

    /**
     * Configurable implementation of EventType that doesn't rely on annotations
     */
    private static class ConfigurableApplicationEventType<P extends BusinessEventPayload<?>, E extends BaseApplicationEvent<P>>
        extends AbstractApplicationEventType<P, E> {

        private final boolean persistEvent;
        private final boolean publishLocal;

        public ConfigurableApplicationEventType(
            String name, Class<P> payloadClass, Class<E> eventClass,
            boolean persistEvent, boolean publishLocal) {
            super(name, payloadClass, eventClass);
            this.persistEvent = persistEvent;
            this.publishLocal = publishLocal;
        }

        @Override
        public boolean shouldPersist() {
            return persistEvent || super.shouldPersist();
        }

        @Override
        public boolean shouldPublishLocal() {
            return publishLocal || super.shouldPublishLocal();
        }
    }
} 