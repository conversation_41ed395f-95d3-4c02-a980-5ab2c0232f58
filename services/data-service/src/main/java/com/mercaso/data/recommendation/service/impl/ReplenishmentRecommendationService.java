package com.mercaso.data.recommendation.service.impl;

import static com.mercaso.data.master_catalog.constants.SquareConstants.REPLENISHMENT_STORE_IDS;
import static java.util.stream.Collectors.toMap;

import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.data.master_catalog.adaptor.WmsClientAdaptor;
import com.mercaso.data.master_catalog.dto.ReplenishmentForecastFileExportResult;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.ReplenishmentRecommendationDto;
import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.exception.MasterCatalogReplenishmentForecastException;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.ReplenishmentRecommendationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository.SquareOrderLineItemInfo;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderRepository;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.recommendation.mapper.ReplenishmentRecommendationMapper;
import com.mercaso.data.recommendation.service.ReplenishmentRecommendationCsvExporter;
import com.mercaso.ims.client.dto.ItemDto;
import com.mercaso.wms.client.dto.InventoryStockDto;
import com.mercaso.wms.client.dto.ResultInventoryStockDto;
import com.opencsv.CSVWriter;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import java.io.ByteArrayOutputStream;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReplenishmentRecommendationService {

  private final ReplenishmentRecommendationRepository replenishmentRecommendationRepository;
  private final StoreRepository storeRepository;
  private final MasterCatalogSquareOrderRepository masterCatalogSquareOrderRepository;
  private final MasterCatalogSquareOrderLineItemRepository masterCatalogSquareOrderLineItemRepository;
  private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
  private final MasterCatalogSquareInventoryRepository masterCatalogSquareInventoryRepository;
  private static final String MERCASO_STORE_ID = "b350491e-e238-4caf-b328-cf2438e057d8";
  private static final String ACTIVE_SOURCE_STATUS = "ACTIVE";
  private static final Integer DEFAULT_INVENTORY_QUANTITY = Integer.MAX_VALUE;
  private final WmsClientAdaptor wmsClientAdaptor;
  private final ImsClientAdaptor imsClientAdaptor;
  private final ReplenishmentRecommendationCsvExporterFactory csvExporterFactory;
  private final ReplenishmentRecommendationMapper replenishmentRecommendationMapper;

  private static final float DEFAULT_WEIGHT = 0.25f;
  private static final float REQUIRED_WEEK_BUFFER = 1.5f;

  public ReplenishmentForecastFileExportResult exportDataToCSV(UUID storeId) {
    // Retrieve the latest batch number for the given store
    String latestBatchNumber = replenishmentRecommendationRepository.findLatestBatchNumberByStoreId(
        storeId);

    // Fetch the store details, throw exception if not found
    Store store = storeRepository.findById(storeId)
        .orElseThrow(() -> new IllegalArgumentException("Store not found for ID: " + storeId));

    // Generate the CSV file name
    String fileName = String.format("%s_%s.csv", store.getName(),
        StringUtils.defaultIfEmpty(latestBatchNumber, "none"));

    // Fetch the forecast data, default to an empty list if none found
    List<ReplenishmentRecommendation> forecasts = replenishmentRecommendationRepository
        .findByStoreIdAndBatchNumber(storeId, latestBatchNumber).orElse(Collections.emptyList());

    if (forecasts.isEmpty()) {
      log.warn("No replenishment forecast data found for store ID: {}", storeId);
    }

    ReplenishmentRecommendationCsvExporter exporter = csvExporterFactory.getExporter(store
        .getIntegrationType().replace("-S3", ""));

    // Generate the CSV file
    try (ByteArrayOutputStream out = new ByteArrayOutputStream(); OutputStreamWriter writer = new OutputStreamWriter(
        out); CSVWriter csvWriter = new CSVWriter(writer)) {
      exporter.writeHeader(csvWriter);
      exporter.writeData(csvWriter, forecasts, storeId);
      csvWriter.flush();
      log.info("Successfully exported replenishment forecast data for store ID: {} as {}", storeId,
          fileName);
      return new ReplenishmentForecastFileExportResult(fileName, out.toByteArray());
    } catch (Exception e) {
      log.error("Failed to generate CSV file for store ID: {}", storeId, e);
      throw new MasterCatalogReplenishmentForecastException("Failed to generate CSV file", e);
    }
  }

  @Transactional
  public void generateReplenishment(List<UUID> storeIds) {
    if (storeIds != null) {
      storeIds.forEach(this::generateReplenishment);
      return;
    }
    // If no store IDs are provided, default to the hardcoded store IDs
    REPLENISHMENT_STORE_IDS.forEach(storeId -> generateReplenishment(UUID.fromString(storeId)));
  }

  /**
   * Forecasts replenishment needs for a specific store. The process involves analyzing historical
   * sales data, current
   * inventory, and available stock from Mercaso to determine optimal replenishment quantities.
   *
   * @param storeId The UUID of the store to forecast replenishment for
   */
  private void generateReplenishment(UUID storeId) {
    // Step 1: Initialize and validate
    validateStoreAndInitialize(storeId);
    String batchNumber = generateBatchNumber();
    deleteTheSameBatchNumberForecastData(storeId, batchNumber);
    Instant pastDays = Instant.now().minus(28, ChronoUnit.DAYS);

    // Step 2: Retrieve store items and map data
    List<MasterCatalogRawData> storeItems = retrieveStoreItems(storeId);
    if (storeItems.isEmpty()) {
      log.warn("No items found for store ID: {}", storeId);
      return;
    }

    // Step 3: Create mapping between store UPCs and raw data IDs
    Map<String, List<UUID>> storeUpcToRawDataIds = createStoreUpcToRawDataIdsMap(storeItems,
        storeId);

    // Step 4: Get Mercaso SKUs corresponding to store UPCs
    List<String> mercasoSkuNumbers = retrieveMercasoSkuNumbers(storeId, storeUpcToRawDataIds);
    if (mercasoSkuNumbers.isEmpty()) {
      log.warn("No relevant sku numbers found for store ID: {}", storeId);
      return;
    }

    // Step 5: Get Mercaso items and create mappings
    Map<String, List<MasterCatalogRawData>> mercasoItemsBySku = retrieveMercasoItemsAndCreateMappings(
        mercasoSkuNumbers);

    // Step 6: Map store UPCs to corresponding Mercaso SKUs
    Map<String, String> storeUpcToSkuMap = mapStoreUpcsToMercasoSkus(storeId, storeUpcToRawDataIds,
        mercasoItemsBySku);

    logUpcMatchData(storeId, mercasoSkuNumbers, storeUpcToRawDataIds, storeUpcToSkuMap);

    // Step 7: Determine UPCs that need replenishment and get inventory data
    Set<String> upcsToDoReplenishment = storeUpcToSkuMap.keySet();
    log.info("Found {} upcs that need to do replenishment for store ID: {}",
        upcsToDoReplenishment.size(), storeId);

    Map<UUID, Integer> inventoryMapByRawDataId = retrieveInventoryData(storeUpcToRawDataIds,
        upcsToDoReplenishment);

    // Step 8: Generate forecasts for each UPC
    List<ReplenishmentRecommendation> forecasts = generateForecasts(storeId, pastDays,
        storeUpcToRawDataIds,
        mercasoItemsBySku, storeUpcToSkuMap, upcsToDoReplenishment, inventoryMapByRawDataId);

    // Step 9: Save forecasts and log results
    if (forecasts.isEmpty()) {
      log.warn("No replenishment forecast data generated for store ID: {}", storeId);
      return;
    }

    logForecastCoverageRate(storeId, batchNumber, storeUpcToRawDataIds.keySet().size(),
        upcsToDoReplenishment.size(), forecasts.size());

    replenishmentRecommendationRepository.saveAll(forecasts);
    log.info("Completed replenishment forecast for store ID: {} with batch number: {}", storeId,
        batchNumber);
  }

  private void logUpcMatchData(UUID storeId, List<String> mercasoSkuNumbers,
      Map<String, List<UUID>> storeUpcToRawDataIds,
      Map<String, String> storeUpcToSkuMap) {
    log.info("Find {} mercaso sku numbers for store: {}", mercasoSkuNumbers.size(), storeId);
    log.info("Find {} store upc numbers for store: {}", storeUpcToRawDataIds.keySet().size(),
        storeId);
    log.info("Find {} store upc numbers that match mercaso sku numbers for store: {}",
        storeUpcToSkuMap.size(), storeId);
  }

  /**
   * Validates store existence and initializes the process.
   *
   * @param storeId The UUID of the store
   * @return The Store entity
   */
  private Store validateStoreAndInitialize(UUID storeId) {
    return storeRepository.findById(storeId)
        .orElseThrow(() -> new IllegalArgumentException("Store not found for ID: " + storeId));
  }

  /**
   * Retrieves all items for the specified store.
   *
   * @param storeId The UUID of the store
   * @return List of store items
   */
  private List<MasterCatalogRawData> retrieveStoreItems(UUID storeId) {
    List<MasterCatalogRawData> storeItems = masterCatalogRawDataRepository.findAllByStoreId(
        storeId);
    if (storeItems.isEmpty()) {
      log.warn("No items found for store ID: {}", storeId);
    }
    return storeItems;
  }

  /**
   * Creates a mapping between store UPCs and their raw data IDs.
   *
   * @param storeItems The list of store items
   * @param storeId    The UUID of the store
   * @return Map of UPC to list of raw data IDs
   */
  private Map<String, List<UUID>> createStoreUpcToRawDataIdsMap(
      List<MasterCatalogRawData> storeItems, UUID storeId) {
    Map<String, List<UUID>> storeUpcToRawDataIds = storeItems.stream().collect(Collectors
        .groupingBy(MasterCatalogRawData::getUpc,
            Collectors.mapping(MasterCatalogRawData::getId, Collectors.toList())));
    log.info("Found {} unique UPCs for store ID: {}", storeUpcToRawDataIds.size(), storeId);
    return storeUpcToRawDataIds;
  }

  /**
   * Retrieves Mercaso SKU numbers based on store UPCs.
   *
   * @param storeId              The UUID of the store
   * @param storeUpcToRawDataIds Map of store UPC to raw data IDs
   * @return List of Mercaso SKU numbers
   */
  private List<String> retrieveMercasoSkuNumbers(UUID storeId,
      Map<String, List<UUID>> storeUpcToRawDataIds) {
    List<String> mercasoSkuNumbers = masterCatalogRawDataRepository
        .findAllSkuNumbersByStoreIdAndSourceStatusAndUpcIn(UUID.fromString(MERCASO_STORE_ID),
            ACTIVE_SOURCE_STATUS,
            storeUpcToRawDataIds.keySet().stream().toList());

    if (mercasoSkuNumbers.isEmpty()) {
      log.warn("No relevant sku numbers found for store ID: {}", storeId);
    }
    return mercasoSkuNumbers;
  }

  /**
   * Retrieves Mercaso items and creates mappings by SKU.
   *
   * @param mercasoSkuNumbers List of Mercaso SKU numbers
   * @return Map of SKU to list of Mercaso raw data
   */
  private Map<String, List<MasterCatalogRawData>> retrieveMercasoItemsAndCreateMappings(
      List<String> mercasoSkuNumbers) {
    List<MasterCatalogRawData> mercasoItems = masterCatalogRawDataRepository
        .findAllByStoreIdAndSourceStatusAndSkuNumberIn(
            UUID.fromString(MERCASO_STORE_ID), ACTIVE_SOURCE_STATUS, mercasoSkuNumbers);

    return mercasoItems.stream().collect(Collectors.groupingBy(MasterCatalogRawData::getSkuNumber));
  }

  /**
   * Maps store UPCs to corresponding Mercaso SKUs.
   *
   * @param storeId              The UUID of the store
   * @param storeUpcToRawDataIds Map of store UPC to raw data IDs
   * @param mercasoItemsBySku    Map of SKU to list of Mercaso raw data
   * @return Map of store UPC to Mercaso SKU
   */
  private Map<String, String> mapStoreUpcsToMercasoSkus(UUID storeId,
      Map<String, List<UUID>> storeUpcToRawDataIds,
      Map<String, List<MasterCatalogRawData>> mercasoItemsBySku) {

    // Create a map of UPC to list of SKUs from Mercaso items
    Map<String, List<String>> mercasoUpcToSkuMap = mercasoItemsBySku.values().stream()
        .flatMap(List::stream)
        .collect(Collectors.groupingBy(MasterCatalogRawData::getUpc,
            Collectors.mapping(MasterCatalogRawData::getSkuNumber, Collectors.toList())));

    // Map store UPCs to Mercaso SKUs
    Map<String, String> storeUpcToSkuMap = new HashMap<>();
    storeUpcToRawDataIds.keySet()
        .forEach(upc -> mapUpcToSku(upc, storeId, mercasoUpcToSkuMap, mercasoItemsBySku,
            storeUpcToSkuMap));

    return storeUpcToSkuMap;
  }

  /**
   * Maps a single UPC to its corresponding SKU.
   */
  private void mapUpcToSku(String upc, UUID storeId, Map<String, List<String>> mercasoUpcToSkuMap,
      Map<String, List<MasterCatalogRawData>> mercasoItemsBySku,
      Map<String, String> storeUpcToSkuMap) {

    List<String> skus = mercasoUpcToSkuMap.get(upc);

    if (skus == null || skus.isEmpty()) {
      log.warn("No sku numbers found for store ID: {} and upc: {}", storeId, upc);
      return;
    }

    if (skus.size() > 1) {
      log.warn("Multiple skus found for store ID: {} and upc: {}, skus: {}", storeId, upc,
          String.join(",", skus));
    }

    MasterCatalogRawData mercasoRawData = mercasoItemsBySku.get(skus.get(0)).stream()
        .filter(ReplenishmentRecommendationService::isValidPack).findFirst().orElse(null);

    if (mercasoRawData != null) {
      mercasoItemsBySku.get(mercasoRawData.getSkuNumber()).stream()
          .filter(ReplenishmentRecommendationService::isValidPack).findFirst()
          .ifPresent(m -> {
            if (!storeUpcToSkuMap.containsKey(upc)) {
              storeUpcToSkuMap.put(upc, mercasoRawData.getSkuNumber());
            }
          });
    }

    if (!storeUpcToSkuMap.containsKey(upc)) {
      log.warn("No suitable package item found in Mercaso for UPC: {}, storeId: {}", upc, storeId);
    }
  }

  /**
   * Retrieves inventory data for items that need replenishment.
   *
   * @param storeUpcToRawDataIds  Map of store UPC to raw data IDs
   * @param upcsToDoReplenishment Set of UPCs that need replenishment
   * @return Map of raw data ID to inventory quantity
   */
  private Map<UUID, Integer> retrieveInventoryData(Map<String, List<UUID>> storeUpcToRawDataIds,
      Set<String> upcsToDoReplenishment) {

    List<UUID> rawDataIds = new ArrayList<>();
    upcsToDoReplenishment.forEach(upc -> rawDataIds.addAll(storeUpcToRawDataIds.get(upc)));

    List<MasterCatalogSquareInventory> squareInventory = masterCatalogSquareInventoryRepository
        .findAllByMasterCatalogRawDataIdIn(rawDataIds);

    return squareInventory.stream().collect(
        toMap(MasterCatalogSquareInventory::getMasterCatalogRawDataId,
            MasterCatalogSquareInventory::getQuantity));
  }

  /**
   * Generates forecasts for each UPC that needs replenishment.
   *
   * @param storeId                 The UUID of the store
   * @param pastDays                The cutoff date for historical data
   * @param storeUpcToRawDataIds    Map of store UPC to raw data IDs
   * @param mercasoItemsBySku       Map of SKU to list of Mercaso raw data
   * @param storeUpcToSkuMap        Map of store UPC to Mercaso SKU
   * @param upcsToDoReplenishment   Set of UPCs that need replenishment
   * @param inventoryMapByRawDataId Map of raw data ID to inventory quantity
   * @return List of replenishment forecasts
   */
  private List<ReplenishmentRecommendation> generateForecasts(UUID storeId, Instant pastDays,
      Map<String, List<UUID>> storeUpcToRawDataIds,
      Map<String, List<MasterCatalogRawData>> mercasoItemsBySku,
      Map<String, String> storeUpcToSkuMap, Set<String> upcsToDoReplenishment,
      Map<UUID, Integer> inventoryMapByRawDataId) {

    List<ReplenishmentRecommendation> forecasts = new ArrayList<>();

    for (String upc : upcsToDoReplenishment) {
      processUpcForForecast(storeId, pastDays, upc, storeUpcToRawDataIds, storeUpcToSkuMap,
          mercasoItemsBySku,
          inventoryMapByRawDataId, forecasts);
    }

    return forecasts;
  }

  /**
   * Processes a single UPC to generate its forecast.
   */
  private void processUpcForForecast(UUID storeId, Instant pastDays, String upc,
      Map<String, List<UUID>> storeUpcToRawDataIds, Map<String, String> storeUpcToSkuMap,
      Map<String, List<MasterCatalogRawData>> mercasoItemsBySku,
      Map<UUID, Integer> inventoryMapByRawDataId,
      List<ReplenishmentRecommendation> forecasts) {

    // Retrieve order line items for this UPC
    List<SquareOrderLineItemInfo> lineItems = masterCatalogSquareOrderLineItemRepository
        .findAllByRawDataIdInAndOrderCreatedAfter(storeUpcToRawDataIds.get(upc), pastDays);

    String sku = storeUpcToSkuMap.get(upc);
    Map<String, Object> metadata = new HashMap<>();

    // Find best pack item from Mercaso
    MasterCatalogRawData mercasoRawData = findBestMercasoPack(sku, mercasoItemsBySku);
    if (mercasoRawData == null) {
      log.warn("No mercaso pack item found for store ID: {} and upc: {}, sku: {}", storeId, upc,
          sku);
      forecasts.add(buildForecast(storeId, sku, upc, null, BigDecimal.ZERO, metadata));
      return;
    }

    // Get inventory information
    int availableInventory = getAvailableInventory(sku);
    metadata.put("availableInventory", availableInventory);

    int quantity = inventoryMapByRawDataId.getOrDefault(storeUpcToRawDataIds.get(upc).get(0), 0);
    quantity = Math.max(quantity, 0);
    metadata.put("inventoryQuantity", quantity);

    // Handle case with no line items (no sales history)
    if (lineItems.isEmpty()) {
      handleNoSalesHistory(storeId, upc, sku, mercasoRawData, quantity, availableInventory,
          metadata, forecasts);
      return;
    }

    // Calculate replenishment using velocity algorithm
    calculateReplenishmentWithVelocity(storeId, upc, sku, mercasoRawData, mercasoItemsBySku,
        lineItems, quantity,
        availableInventory, metadata, forecasts);
  }

  /**
   * Finds the best pack item from Mercaso based on package size.
   */
  private MasterCatalogRawData findBestMercasoPack(String sku,
      Map<String, List<MasterCatalogRawData>> mercasoItemsBySku) {
    return mercasoItemsBySku.get(sku).stream()
        .filter(ReplenishmentRecommendationService::isValidPack)
        .min(Comparator.comparingInt(MasterCatalogRawData::getPackageSize)).orElse(null);
  }

  /**
   * Handles the case when an item has no sales history.
   */
  private void handleNoSalesHistory(UUID storeId, String upc, String sku,
      MasterCatalogRawData mercasoRawData,
      int quantity, int availableInventory, Map<String, Object> metadata,
      List<ReplenishmentRecommendation> forecasts) {

    log.info("No line items found for store ID: {} and upc: {}, sku: {}", storeId, upc, sku);

    // If still have stock and current inventory is 0, set recommended quantity to 1
    int recommendedQuantity = availableInventory > 0 && quantity == 0 ? 1 : 0;
    forecasts.add(
        buildForecast(storeId, sku, upc, mercasoRawData.getName(),
            new BigDecimal(recommendedQuantity), metadata));
  }

  /**
   * Calculates replenishment using the velocity algorithm.
   */
  private void calculateReplenishmentWithVelocity(UUID storeId, String upc, String sku,
      MasterCatalogRawData mercasoRawData,
      Map<String, List<MasterCatalogRawData>> mercasoItemsBySku,
      List<SquareOrderLineItemInfo> lineItems, int quantity, int availableInventory,
      Map<String, Object> metadata,
      List<ReplenishmentRecommendation> forecasts) {

    // Calculate weekly velocity and requirement
    double weeklyVelocity = calculateWeeklyVelocity(lineItems, metadata);
    double requirementVelocity = weeklyVelocity * REQUIRED_WEEK_BUFFER;
    metadata.put("1.5WeekVelocity", requirementVelocity);

    double requirementQuantityComparedWithInventory = requirementVelocity - quantity;

    // If current inventory covers requirements, no need to replenish
    if (requirementQuantityComparedWithInventory <= 0
        && requirementVelocity <= Math.abs(requirementQuantityComparedWithInventory)) {

      log.info("Not necessary to replenish for store ID: {} and upc: {}, sku: {}", storeId, upc,
          sku);
      forecasts.add(
          buildForecast(storeId, sku, upc, mercasoRawData.getName(), BigDecimal.ZERO, metadata));
      return;
    }

    // Find optimal package size and calculate recommended quantity
    RecommendedQuantityResult recommendedQuantityResult = determineRecommendedQuantity(storeId, upc,
        sku, mercasoRawData,
        mercasoItemsBySku, requirementQuantityComparedWithInventory, availableInventory, metadata,
        forecasts);

    if (recommendedQuantityResult.recommendedQuantity == -1) {
      // Already added forecast in the called method
      return;
    }

    // Create forecast with the calculated recommended quantity
    forecasts.add(buildForecast(storeId, sku, upc, recommendedQuantityResult.mercasoItemName,
        new BigDecimal(recommendedQuantityResult.recommendedQuantity), metadata));
  }

  /**
   * Determines the recommended quantity for replenishment. Returns -1 if a forecast was already
   * added within this
   * method.
   */
  private RecommendedQuantityResult determineRecommendedQuantity(UUID storeId, String upc,
      String sku, MasterCatalogRawData mercasoRawData,
      Map<String, List<MasterCatalogRawData>> mercasoItemsBySku,
      double requirementQuantityComparedWithInventory,
      int availableInventory, Map<String, Object> metadata,
      List<ReplenishmentRecommendation> forecasts) {

    // Find the smallest non-zero package size that satisfies the requirement
    String mercasoItemName = "";
    int finalRecommendedQuantity = Integer.MAX_VALUE;

    for (MasterCatalogRawData mercasoItem : mercasoItemsBySku.get(sku)) {
      Integer packageSize = mercasoItem.getPackageSize();
      if (packageSize != null && packageSize > 0) {
        int ceil = (int) Math.ceil(requirementQuantityComparedWithInventory / packageSize);
        if (ceil > 0 && ceil < finalRecommendedQuantity) {
          finalRecommendedQuantity = ceil;
          mercasoItemName = mercasoItem.getName();
        }
      }
    }

    // If no valid package size was found, set quantity to 0
    if (finalRecommendedQuantity == Integer.MAX_VALUE) {
      log.warn("No valid package size found for store ID: {} and upc: {}, sku: {}", storeId, upc,
          sku);
      forecasts.add(
          buildForecast(storeId, sku, upc, mercasoRawData.getName(), BigDecimal.ZERO, metadata));
      return new RecommendedQuantityResult(-1, mercasoItemName);
    }

    // Check availability
    if (availableInventory == 0) {
      log.warn("Out of stock for SKU: {}", sku);
      finalRecommendedQuantity = 0;
    }

    // Limit recommended quantity to available inventory
    if (finalRecommendedQuantity > availableInventory) {
      log.warn("Recommended quantity {} exceeds available quantity {} for SKU: {}",
          finalRecommendedQuantity,
          availableInventory, sku);
      finalRecommendedQuantity = availableInventory;
    }

    return new RecommendedQuantityResult(finalRecommendedQuantity, mercasoItemName);
  }

  private void logForecastCoverageRate(UUID storeId, String batchNumber, int totalUpcs,
      int upcsToDoReplenishment,
      int forecastsSize) {
    double coverageRate = totalUpcs > 0 ? (double) forecastsSize / totalUpcs * 100 : 0;

    StringBuilder sb = new StringBuilder().append("Forecast coverage rate: ")
        .append(String.format("%.2f%%", coverageRate))
        .append(", storeId: ").append(storeId).append(", batchNumber: ").append(batchNumber)
        .append(", total upcs: ").append(totalUpcs).append(", upcs to do replenishment: ")
        .append(upcsToDoReplenishment).append(", forecasts size: ").append(forecastsSize);

    log.info(sb.toString());
  }

  private void deleteTheSameBatchNumberForecastData(UUID storeId, String batchNumber) {
    log.info("Deleting the same batch number forecast data for store ID: {} and batch number: {}",
        storeId,
        batchNumber);
    replenishmentRecommendationRepository.deleteByStoreIdAndBatchNumber(storeId,
        batchNumber);
  }

  private String generateBatchNumber() {
    return DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.systemDefault())
        .format(Instant.now());
  }

  private static boolean isValidPack(MasterCatalogRawData m) {
    return m.getPackageSize() != null && m.getPackageSize() != 0
           && m.getPackageType() == PackageType.PACK;
  }

  private ReplenishmentRecommendation buildForecast(UUID storeId, String sku, String upc,
      String name,
      BigDecimal recommendedQuantity, Map<String, Object> metadata) {
    ReplenishmentRecommendation.ReplenishmentRecommendationBuilder builder = ReplenishmentRecommendation
        .builder().storeId(storeId).sku(sku).upc(upc).name(name)
        .recommendedQuantity(recommendedQuantity).batchNumber(generateBatchNumber())
        .metadata(metadata);

    return builder.build();
  }

  private int getAvailableInventory(String sku) {

    Optional<ItemDto> itemDtoOptional = imsClientAdaptor.searchItemDetailBySku(sku);

    if (itemDtoOptional.isPresent() && itemDtoOptional.get().getBackupVendorItem() != null
        && Boolean.TRUE.equals(
            Objects.requireNonNull(itemDtoOptional.get().getBackupVendorItem())
                .getAvailability())) {
      log.info("third-party supplier inventory availability for SKU: {}", sku);
      return DEFAULT_INVENTORY_QUANTITY;
    }

    Optional<ResultInventoryStockDto> inventoryStockDto = wmsClientAdaptor
        .searchInventoryStockBySku(sku);

    if (inventoryStockDto.isEmpty()) {
      log.warn("No inventory stock found for SKU: {}", sku);
      return 0;
    }

    return Objects.requireNonNull(inventoryStockDto.get().getData()).stream()
        .map(InventoryStockDto::getAvailableQty).filter(Objects::nonNull)
        .mapToInt(BigDecimal::intValue).sum();
  }

  private double calculateWeeklyVelocity(List<SquareOrderLineItemInfo> lineItems,
      Map<String, Object> metadata) {
    Instant now = Instant.now();
    // Initialize a list of lists for each week's quantities
    List<List<Integer>> weeklyQuantities = new ArrayList<>(4);
    for (int i = 0; i < 4; i++) {
      weeklyQuantities.add(new ArrayList<>());
    }

    // Group line items into weeks
    for (SquareOrderLineItemInfo lineItem : lineItems) {
      Instant orderTime = lineItem.getOrderCreatedAt();
      if (orderTime == null) {
        continue;
      }

      long daysDiff = ChronoUnit.DAYS.between(orderTime, now);
      int weekIndex = (int) (daysDiff / 7);

      // Only consider last 4 weeks
      if (weekIndex >= 0 && weekIndex < 4) {
        weeklyQuantities.get(weekIndex).add(lineItem.getQuantity());
      }
    }

    // Calculate weekly averages with 25% weight each
    double weightedSum = 0.0;
    for (int i = 0; i < 4; i++) {
      List<Integer> weekQuantities = weeklyQuantities.get(i);
      if (!weekQuantities.isEmpty()) {
        double weekSoldQty = weekQuantities.stream().mapToDouble(Integer::intValue).sum();
        weightedSum += weekSoldQty * DEFAULT_WEIGHT; // 25% weight for each week

        metadata.put("week" + (i + 1) + "SoldQty", weekSoldQty);
      } else {
        metadata.put("week" + (i + 1) + "SoldQty", 0);
      }
    }

    metadata.put("averageWeeklyVelocity", weightedSum);

    return weightedSum;
  }

  public PageableResponse<ReplenishmentRecommendationDto> searchReplenishmentRecommendation(
      UUID storeId,
      String departmentId, Pageable pageable) {

    String latestBatchNumber = replenishmentRecommendationRepository.findLatestBatchNumberByStoreId(
        storeId);
    log.info("get latestBatchNumber: {} by the store id: {}", latestBatchNumber, storeId);

    // Return empty result if latestBatchNumber is null or empty
    if (StringUtils.isBlank(latestBatchNumber)) {
      log.warn("No batch number found for store ID: {}, returning empty result", storeId);
      return PageableResponse.<ReplenishmentRecommendationDto>builder()
          .data(Collections.emptyList())
          .pageNumber(0)
          .pageSize(pageable.getPageSize())
          .totalPages(0)
          .totalElements(0)
          .build();
    }

    Instant oneWeekFuture = Instant.now().plus(7, ChronoUnit.DAYS);

    Specification<ReplenishmentRecommendation> specification = ((root, query, builder) -> {
      // From Andrew: Sort by 'recentOrderCount' (DESC) when in all/department view

      query.orderBy(
          builder.desc(root.get("recentOrderCount")),
          builder.asc(root.get("nextOrderTime")),
          builder.asc(root.get("id"))
      );

      Predicate storeIdPredicate = builder.equal(root.get("storeId"), storeId);
      Predicate batchNumberPredicate = builder.equal(root.get("batchNumber"), latestBatchNumber);

      // From Andrew: Only show recommendations when next_order_time is no more than one week in the future
      // This includes both past orders and orders up to (and including) exactly at the one week cutoff time
      Predicate timeFilterPredicate = builder.lessThanOrEqualTo(root.get("nextOrderTime"),
          oneWeekFuture);

      if (StringUtils.isNotBlank(departmentId)) {
        Predicate departmentIdPredicate = builder.equal(root.get("departmentId"), UUID.fromString(
            departmentId));
        return builder.and(storeIdPredicate, departmentIdPredicate, batchNumberPredicate,
            timeFilterPredicate);
      }
      return builder.and(storeIdPredicate, batchNumberPredicate, timeFilterPredicate);
    });

    Page<ReplenishmentRecommendation> recommendationPage = replenishmentRecommendationRepository
        .findAll(specification,
            pageable);
    List<ReplenishmentRecommendationDto> recommendationDtoList = recommendationPage
        .getContent()
        .stream()
        .map(replenishmentRecommendationMapper::toDto).toList();

    return PageableResponse.<ReplenishmentRecommendationDto>builder()
        .data(recommendationDtoList)
        .pageNumber(recommendationPage.getNumber())
        .pageSize(recommendationPage.getSize())
        .totalPages(recommendationPage.getTotalPages())
        .totalElements((int) recommendationPage.getTotalElements())
        .build();
  }

  public List<DepartmentDto> searchDepartment(UUID storeId) {
    String latestBatchNumber = replenishmentRecommendationRepository.findLatestBatchNumberByStoreId(
        storeId);

    log.info("get latestBatchNumber: {} by the store id: {}", latestBatchNumber, storeId);

    // Return empty list if latestBatchNumber is null or empty
    if (StringUtils.isBlank(latestBatchNumber)) {
      log.warn("No batch number found for store ID: {}, returning empty department list", storeId);
      return Collections.emptyList();
    }

    Instant oneWeekFuture = Instant.now().plus(7, ChronoUnit.DAYS);

    return replenishmentRecommendationRepository.findByStoreIdAndBatchNumber(
        storeId, latestBatchNumber)
        .orElse(List.of())
        .stream()
        .filter(recommendation -> recommendation.getNextOrderTime() != null 
            && recommendation.getNextOrderTime().compareTo(oneWeekFuture) <= 0)
        .collect(
            Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                Comparator.comparing(ReplenishmentRecommendation::getDepartmentId)
                    .thenComparing(ReplenishmentRecommendation::getDepartmentName))),
                ArrayList::new)).stream()
        .map(this::toDepartmentDto).toList();

  }

  /**
   * A record to hold the recommended quantity and Mercaso item name.
   */
  private record RecommendedQuantityResult(int recommendedQuantity, String mercasoItemName) {

  }

  private DepartmentDto toDepartmentDto(ReplenishmentRecommendation replenishmentRecommendation) {
    return DepartmentDto.builder().id(String.valueOf(replenishmentRecommendation.getDepartmentId()))
        .name(
            replenishmentRecommendation.getDepartmentName()).build();
  }
}
