package com.mercaso.data.third_party.entity.finale;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "available_stock", schema = "finale")
@Data
public class FinaleAvailableStockEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "mfc_qoh")
    private Integer mfcQoh;

    @Column(name = "shopify_qoh")
    private Integer shopifyQoh;

    @Column(name = "sku")
    private String sku;

    @Column(name = "product_url")
    private String productUrl;

    @Column(name = "reservations_qoh")
    private Integer reservationsQoh;

    @Column(name = "stock_sublocations")
    private String stockSublocations;

    @Column(name = "record_last_updated")
    private Timestamp recordLastUpdated;

    @Column(name = "created_at")
    private OffsetDateTime createdAt;

    @Column(name = "updated_at")
    private OffsetDateTime updatedAt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb", name = "stock_items_on_hand")
    private List<FinaleAvailableStockItemsOnHandEntity> stockItemsOnHand;
}