package com.mercaso.data.metrics.repository;

import com.mercaso.data.metrics.entity.MetricsOrderAmountEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface MetricsOrderAmountRepository extends JpaRepository<MetricsOrderAmountEntity, Long> {

    List<MetricsOrderAmountEntity> findAllByAddressIdAndFilterDepartmentAndDateTypeOrderByDateDesc(String addressId,
        String departmentName, String timeAggType);
}
