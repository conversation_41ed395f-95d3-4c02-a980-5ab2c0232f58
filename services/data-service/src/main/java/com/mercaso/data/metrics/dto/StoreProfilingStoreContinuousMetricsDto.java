package com.mercaso.data.metrics.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreProfilingStoreContinuousMetricsDto {

    private String storeId;
    private String metricCategory;
    private String metricName;
    private String metricDesc;
    private BigDecimal metricValue;
    private LocalDateTime metricDate;
    private String metricDateType;
} 