package com.mercaso.data.third_party.entity.shopify.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(schema = "shopify", name = "orders")
@Getter
@Setter
public class ShopifyOrderEntity {

    @Id
    private Long id;

    private String adminGraphqlApiId;
    private String appId;
    private String browserIp;
    private Boolean buyerAcceptsMarketing;
    private String cancelReason;
    private ZonedDateTime cancelledAt;
    private Long checkoutId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode clientDetails;

    private ZonedDateTime closedAt;
    private Boolean confirmed;
    private String contactEmail;
    private ZonedDateTime createdAt;
    private String currency;
    private BigDecimal currentSubtotalPrice;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode currentSubtotalPriceSet;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode currentTotalAdditionalFeesSet;

    private BigDecimal currentTotalDiscounts;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode currentTotalDiscountsSet;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode currentTotalDutiesSet;

    private BigDecimal currentTotalPrice;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode currentTotalPriceSet;

    private BigDecimal currentTotalTax;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode currentTotalTaxSet;

    private Long deviceId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode discountCodes;

    private Boolean dutiesIncluded;
    private String email;
    private Boolean estimatedTaxes;
    private String financialStatus;
    private String fulfillmentStatus;
    private Long locationId;
    private Long merchantOfRecordAppId;
    private String name;

    @Column(columnDefinition = "text")
    private String note;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode noteAttributes;

    private Integer number;
    private Integer orderNumber;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode originalTotalAdditionalFeesSet;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode originalTotalDutiesSet;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode paymentGatewayNames;

    private String phone;
    private String poNumber;
    private String presentmentCurrency;
    private ZonedDateTime processedAt;
    private BigDecimal subtotalPrice;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode subtotalPriceSet;

    private String tags;
    private Boolean taxExempt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode taxLines;

    private Boolean taxesIncluded;
    private Boolean test;
    private BigDecimal totalDiscounts;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode totalDiscountsSet;

    private BigDecimal totalLineItemsPrice;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode totalLineItemsPriceSet;

    private BigDecimal totalOutstanding;
    private BigDecimal totalPrice;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode totalPriceSet;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode totalShippingPriceSet;

    private BigDecimal totalTax;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode totalTaxSet;

    private BigDecimal totalTipReceived;
    private Integer totalWeight;
    private ZonedDateTime updatedAt;
    private Long userId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode billingAddress;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode customer;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode discountApplications;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private JsonNode fulfillments;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode refunds;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private JsonNode shippingAddress;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    @Transient
    private JsonNode shippingLines;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Fetch(FetchMode.JOIN)
    @JsonIgnore
    @BatchSize(size = 30)
    private List<ShopifyOrdersLineItemEntity> lineItems;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Fetch(FetchMode.JOIN)
    @JsonIgnore
    @BatchSize(size = 30)
    private List<ShopifyOrdersTagsEntity> tagList;
}