package com.mercaso.data.metrics.service;

import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.dto.StoreProfilingDiscreteMetricsDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreSummaryDto;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreContinuousMetricsDto;
import com.mercaso.data.metrics.dto.StoreProfilingContinuousMetricsListDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoExtDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreMetricsDto;
import com.mercaso.data.metrics.enums.ContinuousMetricsCategoryEnums;
import com.mercaso.data.metrics.enums.DiscreteMetricsCategoryEnums;
import java.util.List;
import org.springframework.data.domain.Page;

public interface StoreProfilingService {

    List<StoreProfilingStoreMetricsDto> findMetricsByStoreId(String storeId);

    StoreProfilingStoreInfoDto getStoreProfilingStoreInfoByStoreId(String storeId);

    Page<SearchStoreAddressDto> searchStoreAddressesForStoreProfiling(StoreAddressFilter filter);

    StoreProfilingDto getStoreProfilingByStoreId(String storeId);

    Page<StoreProfilingStoreInfoDto> searchStoreByTags(StoreAddressFilter filter);

    List<String> getStoreTags();

    List<String> findContinuousMetricNamesByStoreId(String storeId);

    List<StoreProfilingStoreContinuousMetricsDto> findContinuousMetricsByStoreIdAndMetricName(String storeId, String metricName);

    List<StoreProfilingContinuousMetricsListDto> findContinuousMetricsListByStoreIdAndCategory(String storeId, ContinuousMetricsCategoryEnums category);

    List<StoreProfilingDiscreteMetricsDto> findDiscreteMetricsListByStoreIdAndCategory(String storeId, DiscreteMetricsCategoryEnums category);

    StoreProfilingStoreInfoExtDto getStoreInfoByStoreId(String storeId);

    StoreProfilingStoreSummaryDto getAllStoreList();
}

