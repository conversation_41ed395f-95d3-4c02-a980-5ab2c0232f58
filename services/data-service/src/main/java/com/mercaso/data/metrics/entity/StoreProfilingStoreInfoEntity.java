package com.mercaso.data.metrics.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "store_profiling_store_info", schema = "public")
public class StoreProfilingStoreInfoEntity {

    @Id
    @Column(length = 50)
    private String storeId;

    @Column(nullable = false)
    private String storeName;

    @Column(length = 255, nullable = false)
    private String addressName;

    @Column(nullable = false)
    private Double latitude;

    @Column(nullable = false)
    private Double longitude;

    @Column(nullable = false)
    private String zipCode;

    @Column(nullable = false)
    private Boolean isNrsStore;

    @Column(nullable = false)
    private Boolean isSquareStore;

    @Column(nullable = false)
    private Boolean isGoogleStore;

    @Column(nullable = false, name = "is_7_eleven_store")
    private Boolean is7ElevenStore;

    @Column(nullable = false)
    private Boolean isMercasoStore;
}
