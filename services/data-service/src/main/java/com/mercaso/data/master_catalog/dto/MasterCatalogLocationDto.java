package com.mercaso.data.master_catalog.dto;

import com.fasterxml.jackson.databind.JsonNode;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for {@link com.mercaso.data.master_catalog.entity.MasterCatalogLocation}
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class MasterCatalogLocationDto extends BaseDto {

    private Instant createdAt;
    private Instant updatedAt;
    private UUID storeId;
    private String name;
    private String addressLine1;
    private String postalCode;
    private String city;
    private String country;
    private Double latitude;
    private Double longitude;
    private JsonNode metadata;
}