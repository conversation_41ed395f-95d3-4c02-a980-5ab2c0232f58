package com.mercaso.data.third_party.dto.finale;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinaleFacilityDataResponseDto {
    private List<String> facilityId;
    private List<String> facilityUrl;
    private List<String> facilityTypeId;
    private List<String> statusId;
    private List<String> lastUpdatedDate;
    private List<String> createdDate;
    private List<String> actionUrlDeactivate;
    private List<ContactMech> contactMech;
    private List<String> facilityName;
    private List<String> parentFacilityUrl;
    private List<Boolean> shippingDisabled;
    private List<String> actionUrlActivate;
    private List<Boolean> receivingDisabled;
    private List<Boolean> returnReceivingDisabled;

    @Data
    public static class ContactMech {
        private String contactMechId;
        private String contactMechTypeId;
    }
}