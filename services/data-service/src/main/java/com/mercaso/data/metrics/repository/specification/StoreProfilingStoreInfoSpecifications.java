package com.mercaso.data.metrics.repository.specification;

import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import jakarta.persistence.criteria.Predicate;
import java.util.List;
import org.springframework.data.jpa.domain.Specification;

public class StoreProfilingStoreInfoSpecifications {

    private StoreProfilingStoreInfoSpecifications() {
        throw new IllegalStateException("Utility class");
    }

    public static Specification<StoreProfilingStoreInfoEntity> addressNameContainsParts(List<String> parts) {
        return (root, query, cb) -> {

            query.distinct(true);

            Predicate[] predicates = parts.stream()
                .map(part -> cb.like(cb.lower(root.get("addressName")), "%" + part.toLowerCase() + "%"))
                .toArray(Predicate[]::new);
            return cb.and(predicates);
        };
    }
}