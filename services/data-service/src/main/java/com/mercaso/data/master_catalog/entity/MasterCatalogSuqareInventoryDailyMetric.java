package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@Entity
@Table(name = "master_catalog_suqare_inventory_daily_metrics")
public class MasterCatalogSuqareInventoryDailyMetric extends BaseEntity {

    @NotNull
    @Column(name = "master_catalog_raw_data_id", nullable = false)
    private UUID masterCatalogRawDataId;

    @NotNull
    @Column(name = "store_id", nullable = false)
    private UUID storeId;

    @NotNull
    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @NotNull
    @Column(name = "in_stock_quantity", nullable = false)
    private Integer inStockQuantity;

    @NotNull
    @Column(name = "out_stock_quantity", nullable = false)
    private Integer outStockQuantity;

    @NotNull
    @Column(name = "date", nullable = false)
    private Instant date;

}
