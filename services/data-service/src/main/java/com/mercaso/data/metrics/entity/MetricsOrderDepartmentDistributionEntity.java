package com.mercaso.data.metrics.entity;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Data
@Table(name = "metrics_order_department_distribution")
public class MetricsOrderDepartmentDistributionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "address_id", nullable = false)
    private String addressId;

    @Column(name = "date_type", nullable = false)
    private String dateType;

    @Column(nullable = false)
    private LocalDateTime date;

    @Column(nullable = false, name = "date_length")
    private Integer dateLength;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private JsonNode deps;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
}
