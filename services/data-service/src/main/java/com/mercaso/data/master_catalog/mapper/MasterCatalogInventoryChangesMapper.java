package com.mercaso.data.master_catalog.mapper;

import com.mercaso.data.master_catalog.dto.square.InventoryChangeDto;
import com.squareup.square.models.InventoryChange;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MasterCatalogInventoryChangesMapper {

    InventoryChange toExternalDto(InventoryChangeDto dto);

    InventoryChangeDto fromExternalDto(InventoryChange externalDto);

}
