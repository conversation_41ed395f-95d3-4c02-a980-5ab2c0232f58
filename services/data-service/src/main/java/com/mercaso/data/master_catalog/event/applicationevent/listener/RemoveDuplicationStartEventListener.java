package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationStartEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationStartPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePredictionService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class RemoveDuplicationStartEventListener implements
    ApplicationEventListener<RemoveDuplicationStartEvent, RemoveDuplicationStartPayload> {

    private final ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private final MasterCatalogGeneratePredictionService masterCatalogGeneratePredictionService;
    private final MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private final MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;

    @Override
    public void handleEvent(RemoveDuplicationStartEvent event) {
        log.info("REMOVE_DUPLICATION_START listener start handle event.");
        try {
            RemoveDuplicationStartPayload payload = event.getPayload();
            MasterCatalogBatchJobDto jobDto = payload.getData();
            List<UUID> rawDataIds = payload.getRawDataIds();
            MasterCatalogBatchJob job = masterCatalogBatchJobMapper.toEntity(
                jobDto);
            List<List<UUID>> duplicateRawIds = masterCatalogGeneratePredictionService.generatePotentialDuplicationInBatch(
                rawDataIds, "remove-duplication-temporary", 0.93F);

            updateJobStatus(job);

            applicationEventPublisherProvider.removeDuplicationInProgress(job,
                duplicateRawIds);

        } catch (Exception e) {
            log.error(
                "REMOVE_DUPLICATION_START listener, error processing remove duplication event: {}",
                e.getMessage(), e);
        }
    }

    void updateJobStatus(MasterCatalogBatchJob job) {
        job.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_START);
        masterCatalogBatchJobRepository.save(job);
        log.info("Job {} remove duplication start with status {}", job.getId(), job.getStatus());
    }
}
