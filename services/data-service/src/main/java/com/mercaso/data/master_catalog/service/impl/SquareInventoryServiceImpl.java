package com.mercaso.data.master_catalog.service.impl;

import com.alibaba.excel.EasyExcel;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareInventoryChangeDto;
import com.mercaso.data.master_catalog.listener.SquareInventoryChangeListener;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.SquareInventoryService;
import java.io.IOException;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class SquareInventoryServiceImpl implements SquareInventoryService {

    private final SquareApiAdapter squareApiAdapter;
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository;
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private final MasterCatalogLocationRepository masterCatalogLocationRepository;

    @Override
    public void batchChangeInventory(UUID storeId, MultipartFile file) {
        log.info("Starting to process inventory changes for storeId: {}", storeId);
        try {
            EasyExcel.read(file.getInputStream(),
                    SquareInventoryChangeDto.class,
                    new SquareInventoryChangeListener(storeId,
                        squareApiAdapter,
                        variationMappingRepository,
                        masterCatalogRawDataRepository,
                        masterCatalogLocationRepository))
                .headRowNumber(1)
                .sheet()
                .doRead();

            log.info("Completed processing inventory adjustments file");
        } catch (IOException e) {
            log.error("Error processing inventory adjustment file", e);
            throw new RuntimeException("Failed to process inventory adjustment file", e);
        }
    }
}
