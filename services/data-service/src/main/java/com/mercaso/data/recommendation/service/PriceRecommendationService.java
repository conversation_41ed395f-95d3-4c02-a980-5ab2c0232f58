package com.mercaso.data.recommendation.service;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.dto.SearchPriceDto;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;

public interface PriceRecommendationService {

  PageableResponse<PriceRecommendationDto> search(SearchPriceDto search, Integer pageNumber,
      Integer pageSize);

  List<DepartmentDto> findDepartmentsByStoreId(String storeId);

  PageableResponse<PriceRecommendationDto> searchAll(String storeId, String searchText, Pageable pageable);

  PageableResponse<PriceRecommendationDto> searchEvergreenRecommendations(String departmentId, PageRequest pageRequest);

  List<DepartmentDto> getEverGreenDepartments();

}
