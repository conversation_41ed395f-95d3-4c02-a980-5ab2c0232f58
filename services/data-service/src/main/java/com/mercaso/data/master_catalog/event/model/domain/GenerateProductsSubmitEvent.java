package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsSubmitPayload;

@EventPublishConfig(
    publishLocal = true
)
public class GenerateProductsSubmitEvent extends BaseApplicationEvent<GenerateProductsSubmitPayload> {

    public GenerateProductsSubmitEvent(Object source, GenerateProductsSubmitPayload payload) {
        super(source, payload);
    }
} 