package com.mercaso.data.recommendation.service.impl;

import static com.mercaso.data.master_catalog.constants.SquareConstants.REPLENISHMENT_STORE_TO_CUSTOM;

import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;
import com.mercaso.data.recommendation.service.ReplenishmentRecommendationCsvExporter;
import com.opencsv.CSVWriter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.springframework.stereotype.Component;

@Component
public class DefaultReplenishmentRecommendationCsvExporter implements ReplenishmentRecommendationCsvExporter {

  @Override
  public void writeHeader(CSVWriter csvWriter) {
    String[] header = {"Batch Number", "ID", "Customer ID", "Quantity", "SKU", "Product Name",
        "UPC",
        "Sold Qty: Week - 1", "Sold Qty: Week - 2", "Sold Qty: Week - 3", "Sold Qty: Week - 4",
        "Average Weekly Velocity",
        "Requirement: 1.5 Weeks", "Available Inventory", "Current Inventory"};
    csvWriter.writeNext(header);
  }

  @Override
  public void writeData(CSVWriter csvWriter, List<ReplenishmentRecommendation> forecasts,
      UUID storeId) {
    forecasts.forEach(forecast -> {

      Map<String, Object> metadata = forecast.getMetadata();

      // Get the metrics from metadata as formatted strings
      String week1SoldQty = getBigDecimalAsString(metadata, "week1SoldQty");
      String week2SoldQty = getBigDecimalAsString(metadata, "week2SoldQty");
      String week3SoldQty = getBigDecimalAsString(metadata, "week3SoldQty");
      String week4SoldQty = getBigDecimalAsString(metadata, "week4SoldQty");
      String averageWeeklyVelocity = getBigDecimalAsString(metadata, "averageWeeklyVelocity");
      String oneAndHalfWeekVelocity = getBigDecimalAsString(metadata, "1.5WeekVelocity");
      String availableInventory = getBigDecimalAsString(metadata, "availableInventory");
      String currentInventory = getBigDecimalAsString(metadata, "inventoryQuantity");

      String[] row = {forecast.getBatchNumber(), forecast.getId().toString(),
          REPLENISHMENT_STORE_TO_CUSTOM.getOrDefault(storeId.toString(), ""),
          forecast.getRecommendedQuantity().toString(), forecast.getSku(), forecast.getName(),
          forecast.getUpc(), week1SoldQty, week2SoldQty, week3SoldQty, week4SoldQty,
          averageWeeklyVelocity, oneAndHalfWeekVelocity, availableInventory, currentInventory};
      csvWriter.writeNext(row);
    });
  }
}
