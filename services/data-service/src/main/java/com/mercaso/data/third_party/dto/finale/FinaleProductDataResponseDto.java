package com.mercaso.data.third_party.dto.finale;// ProductDataResponse.java

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FinaleProductDataResponseDto {

    private ProductData data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductData {

        private ProductViewConnection productViewConnection;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductViewConnection {

        private Summary summary;
        private List<Edge> edges;
        private PageInfo pageInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Summary {

        private Metrics metrics;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Metrics {

        private List<Integer> count;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Edge {

        private Node node;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Node {

        private String name;
        private StockItemsOnHand stockItemsOnHand;
        private String mfcQoh;
        private String shopifyQoh;
        private String sku;
        private String reservationsQoh;
        private LocalDateTime recordLastUpdated;
        private String stockSublocations;
        private String productUrl;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StockItemsOnHand {

        private List<StockItemEdge> edges;
        private Summary summary;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StockItemEdge {

        private StockItemNode node;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StockItemNode {

        private String quantityOnHand;
        private Sublocation sublocation;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Sublocation {

        private String facilityUrl;
        private String name;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PageInfo {

        private boolean hasNextPage;
        private String endCursor;
    }

}