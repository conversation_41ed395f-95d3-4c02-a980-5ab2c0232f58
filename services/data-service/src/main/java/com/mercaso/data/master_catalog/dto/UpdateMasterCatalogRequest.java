package com.mercaso.data.master_catalog.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMasterCatalogRequest {

    @NotBlank(message = "Name cannot be blank")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    private String name;

    @NotBlank(message = "Description cannot be blank")
    private String description;

    @Size(max = 64, message = "Brand must not exceed 64 characters")
    private String brand;

    private Integer packageSize;

    @Size(max = 64, message = "Department must not exceed 64 characters")
    private String department;

    @Size(max = 64, message = "Category must not exceed 64 characters")
    private String category;

    @Size(max = 64, message = "Subcategory must not exceed 64 characters")
    private String subcategory;

    @Size(max = 64, message = "Class must not exceed 64 characters")
    private String clazz;

    @Valid
    private List<ImageRequest> images;

    @AssertTrue(message = "Only one image can be marked as primary")
    public boolean isOnlyOnePrimaryImage() {
        if (images == null || images.isEmpty()) {
            return true;
        }
        long primaryCount = images.stream()
                .filter(ImageRequest::isPrimaryImage)
                .count();
        return primaryCount <= 1;
    }

    @AssertTrue(message = "All images must have valid path")
    public boolean areImagePathsValid() {
        if (images == null || images.isEmpty()) {
            return true;
        }
        return images.stream()
            .allMatch(
                image -> image.getImagePath() != null && !image.getImagePath().trim().isEmpty());
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageRequest {

        @NotBlank(message = "Image path cannot be blank")
        private String imagePath;
        
        private Boolean primaryImage;
        
        public boolean isPrimaryImage() {
            return primaryImage != null && primaryImage;
        }
    }
}
