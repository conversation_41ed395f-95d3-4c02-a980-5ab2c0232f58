package com.mercaso.data.master_catalog.dto;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MasterCatalogSquareAuthorizationRequestDto {
    @NotNull(message = "state is required")
    private UUID state;

    @NotNull(message = "store_id is required")
    private UUID storeId;

    @NotBlank(message = "applicationId is required")
    private String applicationId;

    @NotBlank(message = "applicationSecret is required")
    private String applicationSecret;

    @NotEmpty(message = "permissions is required")
    private List<String> permissions;

    private String accessToken;

    private Instant accessTokenExpiresAt;

    private String refreshToken;

    @AssertTrue(message = "accessToken, refreshToken, and accessTokenExpiresAt must be provided together")
    private boolean isAllTokenInfoValidated() {
        return (accessToken != null && refreshToken != null && accessTokenExpiresAt != null) ||
               (accessToken == null && refreshToken == null && accessTokenExpiresAt == null);
    }
}
