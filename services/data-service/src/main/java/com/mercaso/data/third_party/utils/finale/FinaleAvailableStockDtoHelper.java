package com.mercaso.data.third_party.utils.finale;

import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockDto;
import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockItemsOnHandDto;
import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockEntity;
import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockItemsOnHandEntity;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FinaleAvailableStockDtoHelper {

    public static FinaleAvailableStockDto buildFinaleAvailableStockDto(FinaleAvailableStockEntity entity) {
        FinaleAvailableStockDto finaleAvailableStockDto = new FinaleAvailableStockDto();
        finaleAvailableStockDto.setId(entity.getId());
        finaleAvailableStockDto.setMfcQoh(entity.getMfcQoh());
        finaleAvailableStockDto.setName(entity.getName());
        finaleAvailableStockDto.setRecordLastUpdated(entity.getRecordLastUpdated());
        finaleAvailableStockDto.setReservationsQoh(entity.getReservationsQoh());
        finaleAvailableStockDto.setShopifyQoh(entity.getShopifyQoh());
        finaleAvailableStockDto.setSku(entity.getSku());
        finaleAvailableStockDto.setStockSublocations(entity.getStockSublocations());
        finaleAvailableStockDto.setStockItemsOnHand(buildFinaleAvailableStockItemsOnHandDto(entity.getStockItemsOnHand()));
        finaleAvailableStockDto.setProductUrl(entity.getProductUrl());
        return finaleAvailableStockDto;
    }

    private static List<FinaleAvailableStockItemsOnHandDto> buildFinaleAvailableStockItemsOnHandDto(
        List<FinaleAvailableStockItemsOnHandEntity> stockItemsOnHand) {
        return stockItemsOnHand.stream().map(entity -> {
            FinaleAvailableStockItemsOnHandDto dto = new FinaleAvailableStockItemsOnHandDto();

            String quantityOnHand = entity.getQuantityOnHand();
            BigDecimal bd = new BigDecimal(quantityOnHand.trim());

            if (bd.stripTrailingZeros().scale() > 0) {
                bd = bd.setScale(0, RoundingMode.UP);
            }
            dto.setQuantityOnHand(bd.longValue());

            FinaleAvailableStockItemsOnHandDto.Location location = new FinaleAvailableStockItemsOnHandDto.Location();
            location.setName(entity.getSubLocation().getName());
            location.setFacilityUrl(entity.getSubLocation().getFacilityUrl());
            dto.setSubLocation(location);
            return dto;
        }).toList();
    }
}
