package com.mercaso.data.master_catalog.event.payload.domain;

import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.event.annotation.BusinessEntityIdentifier;
import com.mercaso.data.master_catalog.event.payload.BusinessEventPayload;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MasterCatalogRawDataUpdatedPayload extends BusinessEventPayload<MasterCatalogRawData> {

    @BusinessEntityIdentifier(value = "Master Catalog Raw Data id")
    private UUID rawDataId;

    private MasterCatalogRawData previousData;

    @Builder
    public MasterCatalogRawDataUpdatedPayload(@NotNull UUID rawDataId,
        MasterCatalogRawData currentData,
        MasterCatalogRawData previousData) {
        super(currentData);
        this.rawDataId = rawDataId;
        this.previousData = previousData;
    }
}
